<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		mnLbgBhrpRwdlXh4UKzYj73lYuA=
		</data>
		<key><EMAIL></key>
		<data>
		W0gmQxmINA6vbi5M4Y5KotdMnVA=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		k2fBD/jokRFQCCj8xCDSOt3s78k=
		</data>
		<key>Assets.car</key>
		<data>
		cy1YzELDH+/mHL7BtTSVauRmCRk=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		28xWMBQ91UzszfdXY91SqhC7ecg=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		5RCIbdUlNJk08PI3p7NqMnMOW5s=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		dGdGW93yrZKSMww6fXlNF9X1UjU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		20ssJUICcWqUvdA38co1ywUs+3M=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		zFab2umkYe2YoDxd2eJz2emcMFI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		+D1xbIOooc3ypce1+jh+mmLy1J0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		A/Serqf9uzJDYgzPBKizW33PZC0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/HyderabadiBiryani.jpg</key>
		<data>
		aDa0h5UUTIhAPmCkfOVYuIspqI0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/Idli.jpg</key>
		<data>
		Pdh/6tCID4/E5WjHeJTaN+UU2H4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/Pani.jpeg</key>
		<data>
		j2pvr+dSlHKrinCk2B083AaNdXw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/Samosa.jpg</key>
		<data>
		VQxDGhzVIUFPbXG3M0FkHxjhgso=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/bandiwala_logo.jpeg</key>
		<data>
		7qoypRPkbGL4mc0ZXaPM2XdvHWc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/chicken_burger.jpg</key>
		<data>
		9wzDZY/A3nXlj5rTeWObgmEUok0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/chicken_manchurian.jpeg</key>
		<data>
		J1bQKTR7qk7a2Wrrsw45/Ccr7G4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/french_fries.webp</key>
		<data>
		osFQymIBB1xAg9YjDnDSZIUEceE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/google_logo.png</key>
		<data>
		bxxoUJw6xTArlbB7c0RZIJ7GFgE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/gulab_jamun.jpg</key>
		<data>
		ueqQv41ZBH3jJfKabUe+ZA2BbBI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/idli.webp</key>
		<data>
		hPJF99EpBsTn6myHZwg9Q3Kds3s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_chai.jpg</key>
		<data>
		xxcGjEM+LBJLse/G8K9Umo4PPZA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_dosa.jpeg</key>
		<data>
		TiJ39BYUefOK6UOUOwdrI1R2Xfc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_dosa.jpg</key>
		<data>
		OL/sD42J5ZU7UI50BlpJFmDJ9/Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/non_veg_icon.png</key>
		<data>
		1YJS+U7aU6KPeEtZlPjMSoR/7Ts=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/paniPuri.jpg</key>
		<data>
		y0k5l11mSTvzwUuCKzUpKPzGXXA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/pavbhaji.jpg</key>
		<data>
		rC3dghEDS9MkFgiu2Hk9MfjXBrs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/uttapam.jpg</key>
		<data>
		bxxuFFz1kClCuk+A1Ewti4fGA/E=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/veg_fried_rice.jpg</key>
		<data>
		frt4/Lp+k+rSDHKLOm6KQfwhxXg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/veg_icon.png</key>
		<data>
		WrOakWx8Te3fylxZJchB5yPEaQ4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/welcome_image.jpeg</key>
		<data>
		22WZEhGOR8Blw00d7T5nUIbHdDQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		w9WcwIYVRl07EMLuLi7qukruhIs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		I/v5KAI452DNZDU1Mb+2/YZpKaw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.css</key>
		<data>
		HVkEhoid+n6bwOYCwZg4DdmGBHY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.js</key>
		<data>
		v5LMft1C/Ps/+tw6D+zco3p3Urk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		SoHnDHqzNKozN7o/zhsvOoXVErY=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		YKpQQSeCi/pS9xpZ2PxBTAn3/EQ=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		FSKj8+jea1tJz5Wmal2szkIiAGU=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		vBonAqoVJGE3y6WXKxWnl3w/QBg=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/Razorpay.framework/Assets.car</key>
		<data>
		Bju3rMGH/QCKRwqZN0W5qAnnnS4=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/Info.plist</key>
		<data>
		DFcy10+V49NKtjJ4RLpByN10P1k=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<data>
		c8N/593lOrAw9wwSo2JSseKACUI=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<data>
		GAbF07uihY0DE3w15+txJ0RlSNw=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<data>
		Ffh5EuOShB1cS/eITOW9HbEzxi4=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<data>
		6QJ3pwC0+qM8m2WjXuCS4IdsbG0=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<data>
		GuaLaOgcxzpvPP6jj7CE6UW1x54=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<data>
		Ucg3HkU3DNFIfnKf6gQlzd/zqTg=
		</data>
		<key>Frameworks/Razorpay.framework/CommonAssets/Razorpay_Logo.png</key>
		<data>
		C/QPifs1kjcxzxgwUgDFDlLjpRw=
		</data>
		<key>Frameworks/Razorpay.framework/CommonAssets/check_mark.png</key>
		<data>
		6d4pPz33KoUobYRDPpGmnPiTVMs=
		</data>
		<key>Frameworks/Razorpay.framework/CommonAssets/warning.png</key>
		<data>
		gxArEMTCcu4a+ueYNB3oMoIh48o=
		</data>
		<key>Frameworks/Razorpay.framework/EncryptedOtpelf.js</key>
		<data>
		A893KbMpygzZy6/G1xrQkAudMxw=
		</data>
		<key>Frameworks/Razorpay.framework/Hash.txt</key>
		<data>
		gN8QKnsfFYPlPa6NstmuiJETxJ8=
		</data>
		<key>Frameworks/Razorpay.framework/Info.plist</key>
		<data>
		Cw5egKGuBPPchZLu1T2oHdF2hp0=
		</data>
		<key>Frameworks/Razorpay.framework/PrivacyInfo.xcprivacy</key>
		<data>
		62HpNLqPh8tKsg+iNP/pSbF2S6M=
		</data>
		<key>Frameworks/Razorpay.framework/Razorpay</key>
		<data>
		MG47XP4y/K79aEo+sNLcotFaP74=
		</data>
		<key>Frameworks/Razorpay.framework/_CodeSignature/CodeResources</key>
		<data>
		6xpm3tQ6oR036pXPpHIggHin6PE=
		</data>
		<key>Frameworks/flutter_email_sender.framework/Info.plist</key>
		<data>
		9evi1joZByFzDsHw9MbupvRwHLw=
		</data>
		<key>Frameworks/flutter_email_sender.framework/_CodeSignature/CodeResources</key>
		<data>
		i4MZdwHozOTTcBDqL8NhmyqyjYk=
		</data>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender</key>
		<data>
		b1kpnNkaBaZkpKjfWPjH9/ti43o=
		</data>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender.bundle/Info.plist</key>
		<data>
		tWKko2+w3toR5sOvNHzjakwEY70=
		</data>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/Info.plist</key>
		<data>
		zJ6kBcIZ8clnt31pByoR5RZ4dsk=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/_CodeSignature/CodeResources</key>
		<data>
		NZg1J3LP4X63nE2iW1LfX4PnjE4=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage</key>
		<data>
		Ol7PHsUXnpEKI7sK4OCJJ3YVWac=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage.bundle/Info.plist</key>
		<data>
		quYZJIT3tqW0QyzEfIgFfAFGSnY=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Frameworks/fluttertoast.framework/Info.plist</key>
		<data>
		3fri2OrLEukNAPCUZlNp4hbhN5U=
		</data>
		<key>Frameworks/fluttertoast.framework/_CodeSignature/CodeResources</key>
		<data>
		yjwYfM+3/OFcRHZMY1gc+eX/2e4=
		</data>
		<key>Frameworks/fluttertoast.framework/fluttertoast</key>
		<data>
		5gnLBGH8j+KO/hP9aDnXLvTtPiY=
		</data>
		<key>Frameworks/fluttertoast.framework/fluttertoast_privacy.bundle/Info.plist</key>
		<data>
		hdjpSEMBTOm5aw7XLW7Yj1m7KRg=
		</data>
		<key>Frameworks/fluttertoast.framework/fluttertoast_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/geocoding_ios.framework/Info.plist</key>
		<data>
		zXdL6+hs0uS5imdg05TID89NrwA=
		</data>
		<key>Frameworks/geocoding_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		9ouAYdEVWYcDXH+PZ5uKc8xlGLo=
		</data>
		<key>Frameworks/geocoding_ios.framework/geocoding_ios</key>
		<data>
		b2TAK+EXSlJLQPeZb/YeMbfr9RM=
		</data>
		<key>Frameworks/geolocator_apple.framework/Info.plist</key>
		<data>
		0gWQL4fCkmVkl6eHI+Oik4pfCsY=
		</data>
		<key>Frameworks/geolocator_apple.framework/_CodeSignature/CodeResources</key>
		<data>
		QKl9QXN7XB4pcaUiX+LBFh5+An4=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple</key>
		<data>
		X6cr1wK+hTvQCMFpS4C3Awjg6jI=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist</key>
		<data>
		cqdU9hQTSmsb2xKFT6pbvrYW9GE=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KFiVi4mWKmBFkTjfe3H4jsOvLNM=
		</data>
		<key>Frameworks/image_picker_ios.framework/Info.plist</key>
		<data>
		LgMr/BznMDFv2SyiiaP4uvIHaY8=
		</data>
		<key>Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		tLIMIRbDh+jYAL2Yr/woJEwb+wQ=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios</key>
		<data>
		7BG19RINFJlvGq3ykMWtJBaWi1o=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist</key>
		<data>
		0/mHmFCimC/OeD9jRZp+hzOcrVE=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<data>
		i1hvJCsvbp4ieGd63z1telwmXOE=
		</data>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		j2kG4sZNt8NZwAlnhzywG+GkQt8=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<data>
		uRyxi3uk6G4RnUKmaF3+4XX2gUQ=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		G1+swig4aw4rF6mq58rr1bd/oqI=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/razorpay_flutter.framework/Info.plist</key>
		<data>
		OJxR6AE8TmGf8kTLIvZoOn6oSqs=
		</data>
		<key>Frameworks/razorpay_flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		+PoN4LLNW1lxVfJ3yomuaiOD318=
		</data>
		<key>Frameworks/razorpay_flutter.framework/razorpay_flutter</key>
		<data>
		v7OXvso4USf6343iDwEnDcFu4IE=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<data>
		t2qlSQTTWWDQl8c2UElKNhNhgTM=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		hjEOl7ueZ0xAzip9edAWLVgE48c=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<data>
		U/C1mcBwTHv4bC+Bx7jYWvMvlTk=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		0HHriUh3tR2FKqurgFds0IbURP4=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
		<key>Frameworks/smart_auth.framework/Info.plist</key>
		<data>
		R5zfz0pA8buixAW6P1tUyXDkCG0=
		</data>
		<key>Frameworks/smart_auth.framework/_CodeSignature/CodeResources</key>
		<data>
		4haP0xCHztRe/GY7hVhQIAf/5sU=
		</data>
		<key>Frameworks/smart_auth.framework/smart_auth</key>
		<data>
		i0+ev4ljk1iBWDft+Y1EJ/LT4uk=
		</data>
		<key>Frameworks/url_launcher_ios.framework/Info.plist</key>
		<data>
		UobAQo3p8wUwPX9DzVSvy5+XpoQ=
		</data>
		<key>Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		WJwxrohvUav8ek5ji5lYS61z8aE=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios</key>
		<data>
		s8KPBV6alG4NtzQ1enLy5iiWi0M=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist</key>
		<data>
		pGR8wuj8SotDUXkl8k8DbB1CGGw=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car</key>
		<data>
		uHtUXyInZ+Lc1A7Jieav33WssC0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom</key>
		<data>
		NrHn/ViDUH2fAqit33VYrzdA8LA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom</key>
		<data>
		dPlGdP1JgBEu4alyWXRFtS3RWPE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom</key>
		<data>
		B8aUgMWMRMsXJIoOtT8LRBWkF70=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist</key>
		<data>
		hEdXNZ+LQ9JE16hahq3fUgmYtjA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car</key>
		<data>
		NkF2AkRDyLEBH7pj7LSzLsGqU/g=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf</key>
		<data>
		IM7pUc3dgbBfKBC7eTUgbJOheGI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavNightModeSprites-0-1x.png</key>
		<data>
		HxeIZcBiI/0W9ppa4pyaF+T1pig=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavNightModeSprites-0-2x.png</key>
		<data>
		KKCvX9Y8jN3KCYjsTsSvu6TcLeM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavNightModeSprites-0-3x.png</key>
		<data>
		qcmwyaAb5iqL90p8t30zgKE2KiY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavSprites-0-1x.png</key>
		<data>
		Paz3KNNv0IAGgY14B3CO0uNpoSI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavSprites-0-2x.png</key>
		<data>
		DH4wPaTIA94fYZEU1mkfn9PseNg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavSprites-0-3x.png</key>
		<data>
		AkjKcu07/PbJ9ikj9Ftg8Y6Juu4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2Sprites-0-1x.png</key>
		<data>
		+5ZC8YQbAeh6DlzD3Pupi0ZKo6s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2Sprites-0-2x.png</key>
		<data>
		m43XIUQMPgGZ+pAEYfl0S1h1iAM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2Sprites-0-3x.png</key>
		<data>
		3eCcCQ3tDOoU2UQQ4lHc7YgHED8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png</key>
		<data>
		KBiuGmPvZm6y3heWHurqjBMitJw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png</key>
		<data>
		/ZC7kLA33LLUaGoy4elYV+v1Pss=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png</key>
		<data>
		UPqwKRHKhiKSYvXhNjoHx17Nuzw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png</key>
		<data>
		mfB/MyxUmXFg9Vpp/U5VsQY7S1g=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png</key>
		<data>
		jjx4hEkp/WAC6uAe/KXdwGjm6CI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png</key>
		<data>
		71kfAzHiEMvBEzOv7EHvlQHjfuA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib</key>
		<data>
		bhGLQfEGeJI1kCqN1jaNA2pV2n8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib</key>
		<data>
		FydAimIQilKSZ7ywSUMS8jeweZ4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png</key>
		<data>
		30NREArtxH4fd6LNRyNqgW0TjU8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png</key>
		<data>
		qSe4memwmm7C5Lrxsuj/sf78MnQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png</key>
		<data>
		424eZw8Lbvlz/5QRNvc4vZ1RHPQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist</key>
		<data>
		HPAd5GQiIWp3t8KFjYa56npEHXo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf</key>
		<data>
		QKmhT0236O/x77A3aFViLI3U0RA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7dBCadATDRa3cLYE4D35t6CT0lg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			r+xwpKfqmDJiI+lehRwq5xLOxkA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png</key>
		<data>
		58HUtPew0VfX28iT0hoZoqqV8R0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		6gBjAkHF3kLwzqmfNBlRZY9e/kA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		wI+5cU+Y/voatJ+IHCVJWAv6jQQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png</key>
		<data>
		DSedvNbNTixsQO+c0IlL1PkNCdY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		euWXSDkE7B75wvPAU1ssfZA9NaM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png</key>
		<data>
		d+Mu/JBZVfGIW30YjWEaWiC/sMA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		lE3Jc6hIhQEfpuGBhw6EKFWV+Q8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png</key>
		<data>
		1h5giTFrQI++3fVXazIMR/7gzoY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		xT8ttVKmlQQ7RypGMAzTOIjuhLs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			E2xtkVyAo/NwbsLOj5YaYGrRvZc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OzDRbuYmQFAmtdpAUMrXWczrR38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lSMwPZVZRcENnJS37dsqq0zBtUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png</key>
		<data>
		6ZZOqO9aCr59xWXdfdxHYMMTkEM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8sXBL7cRGpwl97VczC3oWtLulvY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eWQKwjKgwkp9PgluXyhtUd5kslE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Qi8Oy4ZCi0BRp9q1/spA2HayuM8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NjdHWhomwMHzP39vpoQqfGQgD4g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NjdHWhomwMHzP39vpoQqfGQgD4g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NjdHWhomwMHzP39vpoQqfGQgD4g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Oz4ra4AnoNuDwqm3/F92ihwI8dk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I5UM7/Kwfmk4xIntsEH2PWq2lKI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I5UM7/Kwfmk4xIntsEH2PWq2lKI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ID8CgPAQ1SpAGP6lT4YwqZLWICk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KWbHbYnN2TAVwF2AnSwHhwFMR7k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DLTXYTVj3B5ORSlaRBpW9ihhYFw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cNB0+9imA0ugzhuC1UgtQhVvtiQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jufG560MykkSiK2FKNJew9h2lMU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rxw/bb3p/31N4nvPIv/zqtmvZk0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ArQqUVp6bHcrK5VWN9LX7Dba9uQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B29xm6hqf7fjr6DU7S66ISOCxGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png</key>
		<data>
		bFfwK83wBj2pyvoGrW0gsjjayaw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		hQkZK9WIJ30yrzBkesaTSfM5C1k=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		FCUpCQIg8t8Kle0t7ba+v99x9x8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png</key>
		<data>
		//Qx3mc4+ewO3CjLc2Y1//23DEY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		KrTZRKDI7Kduyxjzb3s8GUaEn68=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		jAhA6BSLXPLryZQnXa1GUxjVKi4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png</key>
		<data>
		FczO+2H9ZOJL3KtoFkr7ZEbUEo0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		ik1Nng6iqN/+ruu7baKmTu3Cy/M=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png</key>
		<data>
		IgSXn2hc/27HC0evGvhDhTXKIE4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		byDfU2A4NkxQbzsuboUTK7iuvnI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		tHnPy6KqF69QBs6+b1B9h0FqGRU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png</key>
		<data>
		/WPXm9lH4rlc3x4vdnr7k2RHkEg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		zbZAM7mPNNvouB2QcXsTxOCJtxQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		P08jpJQZdro2N5VhzgQnu+jsSZk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png</key>
		<data>
		b4HAoXXQ9MHUkvo2sAUYv1olAFk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		aRQYYYNELohm737W3DQtx10B1Sw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		cuJK6gmSOjEhVI30xIeEIONVSTQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png</key>
		<data>
		jNNswlCF0SH6V+5se0OWriTR7uQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		lUI1aCanVG7Yzr2hHEiHEha2VNQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		JWzMUhmhmba6Wxo9dCHPf5IQb8s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png</key>
		<data>
		8O78ScAfBRbkyJp9FhiadAejM50=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		ZHFlVPKPc/Zwb3ySc9nBC689uOU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		B+VEFeaeFm2C6uIBDXzUldmMLZM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png</key>
		<data>
		KhSMRxKx1AkoUpWWV+VBZqgpDsw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		nwdneM0KoilU07pSKqKyw4UeWxI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		GIkw+XPhxJ6mPgnW6nZLmostL4s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png</key>
		<data>
		yxcShW6PUH4L7SvAnPK6znnipCk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		YEJpYFlKpPtva6iPBIofJQGmrF8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		mpZTRyQo0MeDf8tFP5XVApaPBB0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png</key>
		<data>
		xh3gGmRY86tJc5Lh3Nd6zBYqSbs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		NTF65BOR+sJY4SRV1e2mgkz9kmc=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		rQpisYk5GDcvEAM1KX1zN7Tk0UE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png</key>
		<data>
		v210WlF/V/X0MFX7rrbTkt6/gKg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		+WqWJySO0WVHmdN9btErTRncON0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		Nda7w6LKUWLwuNgEGSa3UV+Gu4c=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9NEJD6pQ1DpsKtuY+2+GjV6U3yk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2PmkF7D38ntCBIUvIfhD6CXR7bQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oFv9jh/UUZqYYIr+5XMATQggYLk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ht0cpWVTwe7Xf2vxJaoJF2FQhfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GY+oMb5n2lyrP7oAkJMXHP0mKlM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FGx4wUVRFhG9K9cqGG2xJ78U9Qw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_A.png</key>
		<data>
		dcyqr4rgmzUXWELeD9Zn+YPjQts=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		dyXuSsfq5t3NzjuTvJgkMqWeT7A=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		q9lFeQVpfirbGU48t8/DsfndhMA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_B.png</key>
		<data>
		HKuokg7TiYLqj8iXDhMc7kwK4N0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		WtYP3q9xTr/H8TMZDKI83bxvjUI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		VWt6d8EPq2YLcbk0Ov79xKr3aQ0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_C.png</key>
		<data>
		CumPPgQmSXZqHIBlT5MIJ+6sphU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		MVxlT3lAwHjw9W1lx95e0X9JmZA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		IAGv7Bpm7LbyWIpYQPluxIUgU0o=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_D.png</key>
		<data>
		1/uVsnJ7a51TD4OqxGjlhmSxdYo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		60Oh3uilWh50L2VaLOrtBa8A6TQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		azMvVFnIAadSgIrsRCfO3Ijca9w=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_E.png</key>
		<data>
		oPPdSWuVgPfXLqIrPBGlF7/HDlk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		ypLds7g+fPaMCVK856W6W9as8N4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		LNhhsAubVJ3Ndyx8w1xmWNLKGXE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_F.png</key>
		<data>
		r3z8HTzzijki3Wz2aCj3xgqAi5Q=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		LDSxbIRyM5F/B1i9howsxkngTpI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		pTRQuLWlv2eLJsclr7KIIHZ05tE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_G.png</key>
		<data>
		Jg3xX0NUmdKuoAvyiSXxAO3X2lU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		2tV2AQCTxW4duWM/YSTgeZQ0IaQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		bi4XQawufNe7f6+9Ib4k8b2fzmQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_H.png</key>
		<data>
		cB8FX65vDhaRxA7/IKNmmbSfgFY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		jdymGiKYreremcDiZd+6lTuvomA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		KoBhL+5kcD1ThhB3wKqPGu+DJHQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_I.png</key>
		<data>
		z7E8u71slwVDN/Na/v1PW0ctoHo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		SlpjHSjV3VB1fXdSKcQdI0YgHX8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		MAudYA7HZfAXJ2fcrgB72ZwhwU4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_J.png</key>
		<data>
		/Hk3LU134ZPf0aUv/dtaCR6wPsQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		3rp7y0MA6iBn3qZClUvT30sqW8s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		kgUhnrmLmAQwjS79rKEfosAaw1s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_K.png</key>
		<data>
		e8RpJ5w3x9+JzeE5bf2CGOxTTNk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		WX7GiEez8pbP5i+vtANKkzg3+XM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		aO22ARdFv+4RsXQwafnIiq3uJHs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_L.png</key>
		<data>
		aL1lPen9RmSt9zyiu/rtEP8/sGA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		WNaSfA8I0/5TvrIPGZthwTuAY2U=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		8WXquDl1Gh2qIqsXK0PzHL4m+WI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_M.png</key>
		<data>
		XLTmYLuQ5+ooGFodYLEOt2NDfxM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		jCy1ldAcYd2M8soMjR5w+Q/XO1M=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		rjNbMd28WcBM2rUUwD2q7+URACk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_N.png</key>
		<data>
		IhIw8ruixg/ws7uO2/VQjP0WDxw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		jSvL7zPuS+fpJdDGYah5pzp2uLc=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		0NhyBk55MKGXWiFM4RWmPteen5U=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_O.png</key>
		<data>
		n29xieW3/IUJqhn3jVDkiftqlvI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		muyB19qDQTv9yxln0rui5FrH5WM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		TwgZvNui1wRxfeQsQEvUt/19S00=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_P.png</key>
		<data>
		fPLifWBPz11m32ijKfQHWt79Pf0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		5ZtMiZ844TomEH78aRquCQCEjiE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		TnIjQxlXpPjnj9i0nmhd+y8G3v0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_Q.png</key>
		<data>
		Vj4JKAuFAzTP8i0ONDZVTVQScSg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		iVN7RHvpWOhJeMeW3NqoqY0Ow6I=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		Qw2IFObuFCFc6qD7fP1yvZAW51g=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_R.png</key>
		<data>
		WMKZhYOpgZLCdxiYmsgMEpIRHLg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		aS8TIWgbFcPV/b+cbsC2nLvqbx8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		IYsvNFibVz9vXa0eq7uoi60Ek3g=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_S.png</key>
		<data>
		QKXs05BbkSmJhU0YdqVMKcEFGQE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		XvcDNd6mCHGLPnvVB0+unTfnr+U=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		0av4NJW7WWzKUz9l0KA57V5c/Xs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_T.png</key>
		<data>
		mR8TfDZYIw7XLeLXlZdtz3nj0OE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		bUE/9+BMUnwagCgUmELQbbSaG9Q=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		tIVEgj3RkVVAgE10FbVWtXiy2MU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_U.png</key>
		<data>
		i0HKLSuJ7B4jB4V0/vX4jOZNMGk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		eryfx9sicVf4eYKrhaWwneBJblk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		SpJIpMdtcKpcOKiJ4s9P4V9qtik=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_V.png</key>
		<data>
		LUjgTQsjr7Au8TjtDJ+HpI5S1Vs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		jUAXXVsx220mRK8rIn0gxyb4SV8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		OCbcOMfnnAC9A7vIvgmICd0fLiA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_W.png</key>
		<data>
		vZIyF19wyBDi7oK8WtPpGlEDBmw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		+X+uuf7mx35d3H50SXBIMhRtsiE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		d1KNJRULJxm4OFtLsnCiEYFdB70=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_X.png</key>
		<data>
		u8M/KLaha2gPNCfksguyvWFEH7w=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		IgXLVrMLd4pesRmT+F/hjXRhHJk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		RMKyIN2C/Mm0kA3WqR4WI6jOvYE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_Y.png</key>
		<data>
		bMevczm8tP1tEkLTuoTblnknRDc=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		8+marSBCTmOA6qsHaQnyInVN2cU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		O1h623u/s9xTj4HxJk2bLGL8IFs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_blank.png</key>
		<data>
		+9ZMz8cOAE7LZsQl1Rg3g7dF3Ps=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		aZLE04FyZIhE/HV7/kT+Teb0oN0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		XKIi4tDiwiEmtlZuJ3rtVC7/1Fo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_blank_small.png</key>
		<data>
		4hr49P2w4cE2fa/x3E/g+XyLNH4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		vt7Z9s4oojvsqAdxio2mZusMU4M=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		3WIqLivioZmlS455XKvi0rLYp5E=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_blank_tiny.png</key>
		<data>
		B4DZCY2NNWuOQD9t+mrT+4h+g4E=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		wzEk5Dl7+cKGBUGQZp19PE3BtAc=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		ZA9wfrZ79KKjnQn+E0Awi9WpTYo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_dot.png</key>
		<data>
		gNFjpey2bIuOcQboNdy6jrMTeUU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		mbyxE3RqlroEvdfKAjREXOa7bJM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		j50IVFQ5DiMefP59FmLIdFE9/0I=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2LbPSEYUGFqlYRMAxz8nzLi1kjc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			O3vCg01DIxEBXrWLw6oyQWdNhuY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0x9nBOCB1q/JvJMUdZnwkEWxnOU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BhvfqFxVsZmCaxweOwwI7+X67Ec=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5ITo+8xoaJZOoC1OvKdKaEx+te8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png</key>
		<data>
		z1kf4/sWpoPOzvXlf8K5f337H7o=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png</key>
		<data>
		YoDHbGi2Kzr6KzaLWpIC+iHNW24=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			q5ia8OeRJ1ggoTLcrvclmMgoivI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			q5ia8OeRJ1ggoTLcrvclmMgoivI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qifEkZQF+RmmyNFCBRHW6ARkhcs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rRpdOnRG5UdG+xxdPT7rtZfmezs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png</key>
		<data>
		HG4lAzW99jq1X860ruCadRTP1JI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png</key>
		<data>
		Y9Cc3P9fnqQlvUq6hljpmps6Rfg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png</key>
		<data>
		RnyN/LbKvVpEJtWMNkWwn1v0jnY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png</key>
		<data>
		liaTlaCHAiLcpdhS15/FoNQuex8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png</key>
		<data>
		TgH7/QavDGQI/skL1YE/6rzYH6I=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png</key>
		<data>
		5lTmhJEnxGk7JCSt1o7hQvsaZE0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png</key>
		<data>
		RMtreAjsSE5oStFTdN9QLiWrM14=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png</key>
		<data>
		solZa4fFXHU4p61KSegryhqGfMM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png</key>
		<data>
		IGEsKFv6icS08/TMkp+MormykLo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			o4/7Vs8ToVFso1l9BFMrayQf3bk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9yDBK+/HfI/4cCJQHa3avpbcK7U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j+Y9H4EF0wG04685jy7Gj8IwuIg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rzUCfVYfaZ62o+9MobG5PQycBtk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			d6dUma9EG7baOTHPbA+PNxUkoh8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			csnehjSaD21HDCm2LYt84sPFtco=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9/ChmaNsFGA+FRYe+EeyGldbp2M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dF04UuBEAMeTQCMX19E0rybq+cc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BXIqSIDyRLoijxpV83Ejsv7w4uE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			UsXGfn/Pi+SZZbPDTy8ymlgj8tQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x8FY7C+3iy2ItbI2ZjjgY1wBOzI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XUEoVr4V7Mb237Z4FNIKHDVLwnY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B6eAvPXC15OAQj/XM/tWRIpUkEY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			c3gZe/3u0UEpLcVJ+Su1sNgHfI4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist</key>
		<data>
		AmnCvg4zqc5SzokEkCRK9HKov+M=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		IJWHKZ81G0g9IVxCYsXkBiMC1qM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png</key>
		<data>
		wEgufayRRGE/XJ0qEoTBxkKrlHk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		Nk+SqgFNjGYaDU/37+ZTq+lPIrk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		PeAkYTqZ90NC+h3Mi5El7hzRBgw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png</key>
		<data>
		y8j2sPykvEUgCCWbqkMQajBUA70=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		szkKZfIOIr+5aD8GPzuDEHHmsBY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		YSf405gjD1uaF7YDsam6q4yvNso=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png</key>
		<data>
		0CVIeN+d0Z/e6FHcEzk0GpRz9KM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		BYvkcO3ien1dBURGTX7/iQ61684=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		4z+GVXIKMXonei9wATmOpaG2Yp0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz</key>
		<data>
		zIqOtk8jLETzlLmb3AcgilvmMFc=
		</data>
		<key>GoogleMapsResources.bundle/Info.plist</key>
		<data>
		h9xLa65e/WA/uh65idDfKpc+Oe4=
		</data>
		<key>Info.plist</key>
		<data>
		oMwpSDBf/p3OBXqhRVZUHq2zBwg=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		kP6lLb9cGegf96JareB+w5edqRU=
		</data>
		<key>__preview.dylib</key>
		<data>
		R/izgb2QpyIjUuvnyz3dGpkycyk=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		dW9p2HILcQ6OeXEMDkj6ujYKO2g=
		</data>
		<key>google_maps_flutter_ios_privacy.bundle/Info.plist</key>
		<data>
		bQKXzpxBASjuhelr2s7nGx/T6cg=
		</data>
		<key>google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		2883mGk+25yVMT1GYe05XI1vTMQ=
		</data>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<data>
		vEizg3fnXIRy4OhNHy2tjCR3O6o=
		</data>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PgAJpgZlblxKbgx9eihlgflAQU8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9VR5aeuJMnm/RgXM3cr4LUNi9UZgxKD7xAgkid0NI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			nTKNHUXzhjdeKHNhWbT/pTxJMOOWIBbb+YsCmk6AEhw=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KaU6BpCNLefiwNS1DZZ8xhrLM/dmicIH5D8o6XJMOZY=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			rtKR8PPETTJqZdkJA0c9e/MaMluQvg5zTEcwOTtdO+w=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			by6WshwXWgbEYiAy2bvh0UtjSVa3EwySkNFc1FazGdY=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			srrkeJ+QMlN9zR+xpdyFNkZ8rSPE0bXy1vgiISCwG+w=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SES+IrctFtb2ATzloiQHKMcw7x/vnPQ6XFUZixhhSAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			65QptC54C5gnd6RthUcwSpaRdL/0oymMfk5u3kVGNFk=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			XhkARm67kx0Za5UTSpy8CqPtlZSXxrc271Aor6j3dog=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hvXTwY8U8BnGOdiJ9h2zphrz7I4sRCTFr9pMHaD0a5U=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KLHrKz0uGtYLjIsPkQCxzL9JL3+pf1vrtR6pfnOSbn0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			PBU9JvrJhW1R+eJuNwtQ5emGyNLKMmVHsrwZA/7E+I8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/HyderabadiBiryani.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			u8KVFhmiQAuUmrMyvX0XF6Dz1rgsNaiOZcz4jglV0S4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/Idli.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			UBCWv0KgrDlF+nU91YyaQsmv/K0PONsPOB/9vHJxf3E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/Pani.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			V0qyZ6ER5WCKR8C1C1jRxEFcmRE8ogsof1iyTBKWCWs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/Samosa.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			tgMuz0di7teIIohWRCEgRzcOYLlXZ5d34iRN72fOYCY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/bandiwala_logo.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			SdeRgaOT1A2aKMW6k9TJcqJd8WRCkqJksINLWKVrrPw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/chicken_burger.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			IUHLdCJ04eOLQYc9B2/mp7TkuBPzenChhwcEbG8Mi64=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/chicken_manchurian.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			oSA4wvSrdI0S67mb4OI1lh/1SxdGgxC23CUWwrEJwTo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/french_fries.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			UniiwYT3gpxztroF03TYW4BXhPOFdFvpdG17lnx7sPk=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/google_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KYKk5rTBI4NdrbQ2fnxEKwmQCAxZrpEHJvNVFiJBUJw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/gulab_jamun.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			oconroH6jzVK5VSm0CXRnxSLNLJpg48IwX3NO5Xrcg4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/idli.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			R9BWm/tiugrkPMzgLa3o0PtfFderdqg1cWg+1aXl4Us=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_chai.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			CiEJk1bZSJhSo/iB6dcNOjItHd/NTUa8KxgIDs5zZYo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_dosa.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			Op+hM+Gi9TftG3MhRTNQgte/O2LlCkeXfDxltm0H6ng=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_dosa.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			NMkLUoL85FNIySQadb7ZTe/kouRRhR4gYsL99ZjfEtg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/non_veg_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+KYnzZCXDY50n7MGEJnKYzV9b/jLdK4bJ3pkJKORr4E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/paniPuri.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			GmR+j+0EUT5z5CS5TgiKMOSZjWsWIC7cgS9qwW8IZAU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/pavbhaji.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			k/RpeVlJvZz2GJBpL4uVt6hREVGPHzRRPiBNJGobFXs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/uttapam.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			lOlLw0ljkk5aS4on+DTiwPkOXi1uoIY1CaiKVw8uRb8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/veg_fried_rice.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			4Ry7VtPbi17Z802aFVBa1Q1JEXY5CIQ0oRw0LcufFPA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/veg_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2gqDntuDPD+HHjLAp4AsqM9IwuGHOyWduoFfeTh3sKM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/welcome_image.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			xGjbOLR4kVLq5NEIiTttcFiJNx63MRf8YhRp5+OjdLM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			PBOAqqWi5ujK3/k/uWcKrZyOam5QktQ5MqxZKes8MBY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			7pH1n16tHnEmNKpqwp8p9TY1sjvIpEd+bxlmV03HuoQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.css</key>
		<dict>
			<key>hash2</key>
			<data>
			abr0T+pbPv0zd7q8FU7xuDv4JjuRAgKox9bnRddRNJ8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.js</key>
		<dict>
			<key>hash2</key>
			<data>
			CRyacJlKIATuArBPER1Aq+QMB7BmhWOZ9YxYcELsKZA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			RTqty/gYjcrXxgR3QLjNukvL8gGpfV6MdaYktQJaIa8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			UAdepRfDzlKSLMmgMOm47eAZ7UU2/zX/uUWq1bln4hs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+33F9+1+BOcx8Bl4XSagM9ATcVmw1/qzCNe1OPmnD0k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			1dtlVSYHX7a7266RT1WFpkgXa70YMAhMCBG9cOrS61c=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			PHb2ZuQOlqYYHw0P0haHf/aFuDNeeCbe+axDReTr7h0=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XV+Km0uI0aCCx6b8FFBL8ctnAUqg/+iH2HKwpDJJDns=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			9gbR1Bca1fy1VmXM6YTr9iMj/H9FiV5FH80kOSCS5cA=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0aP4/XYhE9pTrC1von5mej2B5hZzviQdM4PQj/nojlY=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			kuyVflk39J/m/Tqjb+YY8Y38/6KfwNjCkTXASxoCEXQ=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			cAbddAE/I0qU6/oKTC87iT4nJ2fd0p9/BUaCe80QOek=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			I8p2+VocnQu/7nTAHxHwPLoZ9J+et+y957CgfHi+MQo=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			h05YMT+1oAA6um16sH5KVB2c6toLm935YPmV2d/ZDU8=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/CommonAssets/Razorpay_Logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			udRErjoaEwN536HIEl+2sH6KQ0Q2KzlKwLYCkQlBGKE=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/CommonAssets/check_mark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			s+l4gXoMSGUj0xR2eSdXwQTHoyRU5F4+aTSVjO+I8wU=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/CommonAssets/warning.png</key>
		<dict>
			<key>hash2</key>
			<data>
			S7OOo4xdlAEiEgfkEuic4ap4JZlhvtYIwFSHWu034SA=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/EncryptedOtpelf.js</key>
		<dict>
			<key>hash2</key>
			<data>
			85Tocsg1aPekBFPeMKMV2IruNGVkOyyV6xlbQkQOH3A=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Hash.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			B20CsAEe8H45a3Ivw23aBTWNU7qGh1JYKb/iAwwaIEQ=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			bCMBqQ+1ZkZtTVn2jzCsPtk/FzeIz/cvylmt3RjpqCE=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			0GK4q+J5XVD2O8agumlONknk2PSlkzr97y/P84XeCyg=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Razorpay</key>
		<dict>
			<key>hash2</key>
			<data>
			FljXgddJjwk4Wk7tQvKsEY0YTA295XnNH1aXmmWKubM=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			M+UQqskRMD8hIPMSttm5T0WRuARg3UQIyC9waBPKsCQ=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			1yRFriBbFHXDotQAfeHLSu5/SsXQoX244C3gK4lqc9Y=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			PPCA3hYouoOGtytdxp9dXIiYaxMwl94qg6jO30jln2E=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender</key>
		<dict>
			<key>hash2</key>
			<data>
			cBJ5dFy5GEIXlZtw4e25XPodLAq8px+mXfo+CpBoc6Y=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CJMSsT3Mewm8fgZFi1jeT25Eg8Wzl1Z0kKUa4563vig=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WDi5AqL9WmvwLSOLHFf3WY+JMoCA5McnxT7L2GxXKus=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			6Sw1autHjXcqcSlBnC/1PZJhACZyFgXTAIjSgAbogc4=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage</key>
		<dict>
			<key>hash2</key>
			<data>
			qutCECv1OJsTXIoS0LOzTyq95OUIIOM4nbb95kibYo8=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			T/XM4fbSiTpDG8X3nC5vL4Eiq05Wjip0d9z9nxUXLms=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			diHntz4fTCv+WSzANj04y4/lEEjf9yBRL2RF7Ou8xHo=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZEPS+n6vHJMa7yft79aql7oDhoG5oBRgI0qSEqPLJ/M=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/fluttertoast</key>
		<dict>
			<key>hash2</key>
			<data>
			IdiVfoG+kiHkzz18GOhgVwUUapQsNFobkGj54d0/ZUc=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/fluttertoast_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0XcHsMs9wqEScsakoXbS4p+tjfVivANJ8ZDK6i09zds=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/fluttertoast_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XRg1v5pGmt7+GihLD0LrRHIcMntS7uaNfLcqC6UKx8g=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			uB7y97sHzBE7Gt7kvw2DM+SFOHTPLJoH9vXDGlvkmGY=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/geocoding_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			GM7ZA5xTLP8mq3JaLjwN/zNtXu/pfDlTgAucQrslbjM=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sW3d7gXarQgOwVL09QqbL10ekjkJ1zn5AWUOHsxNJtA=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			BSptDdYB1o8H983Y3euG3cOmU2bFwraSCs17+H3xS84=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple</key>
		<dict>
			<key>hash2</key>
			<data>
			MjA8U3hw2p/twNOJ1rV/pnHm+jd6RFaPzrqRpMh8opE=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HxtmpAlzUiCq5Tj5FXMFT4NRAwPiocw1GzT9qMx4a0Y=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			p+c+xOFN/pYr5bFknUH/y01YGEb8+JJchzbtJ60mdTI=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			MSYeb83ucEHgahbrPOLzU0Y6U4nh6VVKb8S9ESIaIFc=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			eUj4uGmuKWPDgU/AqUfrOv2alI1JMgOMKY+JJV3rTac=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			Hs2eHZ5tz+YlA+BVt81iyecGR8kqqZsDFzGNQC7oEMQ=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Z/qtAhdvW85/75i/gz0uWX4Xi6QCMV1E8Yu9lamYUck=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			YK04DnI+6N1TVsvt4sAmHAmCW9eN3iLaMdHWDYdDQsM=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			lwUVXl0RlhhhVr9YWoUklLzsHwzLjfPDQNXIbpda6ic=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			r2zVk0BUd06vwPNJ4csSEV/lZk4mMM7tPJh+Xixe4Bo=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			8VLfHHpfd3Sx8qdRl8YzicE7gt4is+eCTjoQr3lJZIQ=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/razorpay_flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			lhHwQPrjOIfHe5Ty6aQktk14ZbWOdttQmuTDlN6eOIU=
			</data>
		</dict>
		<key>Frameworks/razorpay_flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			OHU13B1NZCjX30OGJ0TyKkgJW7XdEldfZETWfUSt/0s=
			</data>
		</dict>
		<key>Frameworks/razorpay_flutter.framework/razorpay_flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			K4ZquqJyj2P3UDjcCW8Kh3A65ODXAaaBQXpRJ6CEpSs=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CV0PA9B2bk+WS9FVdYvMXkZVob6euoy3xwsAIZ131gY=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			tI09wWn2OL9VCyg/u0R+pIHYlWkuikFsMSuudXkvLR4=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			jaVc5DpsK4Yb3tLMgKV0u+Q6YV5naOGdCuveO4v3P6Y=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			jj5NEDTE8Pqo0sbUF/RVBRpCWQ+EqVy+gvA6pXksVlk=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
		<key>Frameworks/smart_auth.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			oWWahrTmKevl1q/FETksmWCovr0BBwepmHDA3J5OvnI=
			</data>
		</dict>
		<key>Frameworks/smart_auth.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Mp+E05g59KGRhhwSPa2useuF/+5plGpMODqa0dKx1Ok=
			</data>
		</dict>
		<key>Frameworks/smart_auth.framework/smart_auth</key>
		<dict>
			<key>hash2</key>
			<data>
			/cAoL31dCWgubwvIMNhCrpOomNVSy0lQFhzX3hRgYf0=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0SeGBTL3OUYi5DEKgLePWP8XaxZBZ2BF+WIwgglRzzM=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			SoptlIvonapzDnYjwd0vtVZlr2/xCvK+pw39NEUzK8o=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			5CyyjYGdRitP8zHLKeTGuCME/P/07T9l/KxjeBeskWQ=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			KMPpm7bkdhz9o4/1npueuzczJ6L7iVgOdLl/QScHJgk=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			BfBznrtol72AAMABWhBNc1MfXAJlX4zvJqmqayyVzUM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			CxbYE3wrXcJQndigSesh+ZVypV0EuSoLn2UMMOXup7Q=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			QPNrVpjA5tEkPZBzFckhF2CCw68NW9I63BsVr4k5QxM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			y3rz7PyCC7cNJFLdBu89wxgK8v5ZJ4YC0wIJJE73Dqw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PF/YKChR7L57aUIvMjGUOGMpsBVwpQ3wFtp5PMRpvOc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			e43/oJ1QuclQg0xWZrPiw1KmD/e2BJ2HSNCkOQwuor4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			oRgIxXbtc2TA+CQC4IYwuDWJiwC79rQTLdnyhCHh0Pg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavNightModeSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			PrdjdlFEPixe++FHoBesbK2Q97NRuxEPMeTTzaNuubc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavNightModeSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6fW1wAfi1PnBUiyzPnead4/YuMihqSm49qVFVf0ZVwg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavNightModeSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CCveNx1hDJazfuLYKTPWcysJEKe9tDefzRS6fXgDN9o=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			A16QAtUDiO2ulu5vAo2ouplMflXdLo3VDAtmwMF8fe0=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			91mOcD9AAQHkjnKcFUgNcpQCb9Cltr/LA7fEBGlev2Q=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2NavSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			vfPpN/6NeYrsMvPJhsAd+T7Q5+LycPEVqUBhC0AJie4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2Sprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			LjpyiyM8f3MxkQRbYQZX/2JCHOQ7KCuu/vELdKmGOGQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2Sprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			j6+euGVEBCtR6tuppaDmUb3nyQGlYKT3b68THq+vOtY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSMapColors2Sprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			W23RS3ANiWZV9juBv4YbF7F82Kv8Jp4pSqfaEBUS04g=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6I9AZoWd/JQ8PV9b4FcAqf5GJT6iUEe7YeuyNGEkwfc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			35EHfeztT/7K9NKjZJGCUlauQ2HOF3PBQxkLgM2OGUw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yGhIyrKtKLoQ2oJfnH4RrIxHQUQYODC5SL+bLjiNbxo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jRfU5I+eBrG4NBhLV9IBsXXb0DJ9cXoHbCE4CN0QiUc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			S3sLYbDleAUGAjWIVe8mvnmB6ONkTxlmkd9BPlylOWs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0SyDaAgdNWt5XirK8fh9r/KPbbBqlWeaa1+p4aMYRvw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			IGAgmq5lgrytpWMw24Pu7jyPpbMBeCZr0HSAWh3LZvw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			C2gPbHa8L7jhyoYGdCoaYj6AQcUa+VLzW4ksDvC3V0M=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			geas/0xQ8DP9+GEy1er6S4x8a76/OXVVmkqbjgQtjp4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+iYPYEluzYhBRYuyl9xF3jA7Xb3jXiqAwDrAQZmziuU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CtdKcxbVwWhvcb+95gXiWlRCSVrdDtAx/YRmXRZbeT0=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			t2bPNHOx7qFE/u1KJYf6r/n87bi7C7wr2u704hkG+I4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			4HMV9v4N9IH0373fJ215YuoHOKrnkWwgbsN63C7LlNc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jn7VxEtOsjN9/OFiVGSu8T34KDaw2OJ8nbtQcpwgiXw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			YiKztQeVUpBvQBUJpKxhUoptWCB1c7zYDgWM1qeCVNM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VUfuf1zHkFbUsHfs0W2XgKZU1+OccZ1EFTBKqUPRvpA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			L3cYb4rSz5yv0DjIESC1J3/cb8P0Js0Ff9mVn459XmA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			UJlmp7f16dQ1TqC/a0uyZ8RGy7o/exUVKOFU8OsbbFU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fFQ2JJGO00f80s6uU4uleAHJ+R9BTG1nUEYfsfZY9Fo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			KsU6yUyahIrTFyLyw00wG+zkDkxGGocVZMhX46e19Gs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyxSDpdORHSyv61cmUzZqM8ndta0mAzjXNz1I0LMonI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CriYgwzkN/I/cVy1eJf4HI6kfJaC2PT7qk3sqSPG60g=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qHyHEkW8JdVyAqojiLMeEvdbdu29vAMj/80KkzOxCoI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Lit0EakIiwZVnj6fbN0D3wgszTkSE9/rnfzmpGBwUps=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Y5nhZuTZstbPtHi+aLXNZnJwJ93786LyLlKZVQEV8oY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ci+lqSfJY4Gxuwy2jAiTeSDwvzR4dzCYaPlMboLFCuY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4s1UPpfDBZjPOHrYf0lGeBNzlL0Bt9rQH3J2yAnTcek=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7/vwjVbNju+XCu1xevYaVQ4/JUsqO/RCsRDlPNq7kaY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			QNs0wRZ+xNJ+eFYmt2KOoanjWsiOwfKjzpA4+hokaX0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			sZe6x43YMGH2g7W5FH/KVqioQm5811igN/5U0VPp2qg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hzAbSP+rLNyotOhy1lFJIK8jbjKvm04cbA/IABgU9Xo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fzb1hVPKEgobPqLjI/bUytoFGhWt4D0GFoGsEmSbjtM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fzb1hVPKEgobPqLjI/bUytoFGhWt4D0GFoGsEmSbjtM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fzb1hVPKEgobPqLjI/bUytoFGhWt4D0GFoGsEmSbjtM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0DCb8kxpkqXGzCUEb/fUaatOeFcEkIJvLVwcV65cXBk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7TI2SJGccUCP+83/eYLwbdLxTmUiHQUjj9EesVAGWHk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7TI2SJGccUCP+83/eYLwbdLxTmUiHQUjj9EesVAGWHk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Pq+4X/KKFxtUTKcJXMjHmKyEIuZdGcAeK58eGWivsKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PcpbREDhLBfDzrLAoZToilcEbkLR1qt7IN3NMSW9bMM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gE3N8AZWTeh4ki7Kgr/RoRfI7PIOdnV6fImbmvOWErc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fpdi6n8GfuqjQOWygQ8XIN8MeNTbowa9Ua/troj4zas=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rzNZR5Ax8zRl4T6cq5YKKBPWDCGUruAXrumU9AVJ7S8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			83PJzvZcw//uDqgC1UJjO+6cndxKIzAkv7nnyg2pyos=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			I1y95/I5wUZ5J2Ilk5+TXBFPQEAEQtbmGecZ498dCJw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6S2Y+q09A+uEOQyZkmun5IYf9I8KsqsGvdJYXJxU+Fs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			XB10YfoFMMA/1hJ7RcpPwRn4FPISn4/GfcLt1yacpbk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6Zoc/OxM+MGLTiTWiXfGiG/vQbaZu+4BpYdjwEWmO+k=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			32rNSQbMm/k11a9saRft5TUhSAEwkY3nI3izgMIVsWc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			tA6fq0SW033/kwgkIJ6B6sQ3DSvEwrztHLfbLzPMZJ8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			7w1goskgoHENDngEcZmMetZnbBY202XkAvwpkFUUGmc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			9NGwfkNI8H2ANbn1RB4cLWXlA/Nj4yeB5459a6Vr9Sc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png</key>
		<dict>
			<key>hash2</key>
			<data>
			V2HcZbiTq9tAJFbLYxUnzbb0utxxO6kEVKiZsw0cr9c=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			l7DXZC/yFWAiJnn1y7b8MOf2wCeJgHDmWYdKB5lMTS8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1TKLueEVV6y48hWYWBESJz23RavEBopB2CMUaHlutSg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			HcFbnY64QuHwGzcNE6SaFXzwaKhtLHJf4uQ9wDMEgGA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			GaTms7wugZAVzLWzjmz62i/7XEjgP6usP/nU20AYYP4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0TDx02zeOyLukli+xhvRlGoyfDPfmHWEXjHT3uzpDk8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			H8OXFdacv5KzG+qLR5RrChts7GKxClFlKqHyXNg/6qA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			z9EXEDKpCzvyZ7kGENSEXZznpcbyINaD1V8HSi9sgSs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yBxDOtAyeWqzpC63ugLpJhxYcAscliU1BgZnNdEcxmI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			0RtBxUvJfAOQCu0YN4P+NXKIgVGXshxUEVhfsd5RBek=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ccFj0dMmtprxMVMeVoKpD1/Kb4WJxACoK30HziyMr+E=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kE33BkYF1sZUQlzcuqab1WvHw2bKowuYmlzrbJpShF8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			1VhiJ1I7+yEdNt0CQMVPQt3sqbXWCS4N+l+OU4f+Wik=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			hl7Q3DhOytiS5PxACeNjtBEwRxXf7l8c3w6kbzMZtLY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jDxAgphhHwy2VvYUHX82RqRNnQDGP80tsgbIm4TM/Ik=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			OflLl1p4nwACrp977dRsNpKM9fZyFj0Yckli+0LJ7kE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			OiZcsS9CGROol2GoyCRvbD/1OnnO5Cr9tMmymx+gJuE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OW3vSyC7N+/XTypt4QcbljeyrObk1gU5n9aHHmZk5oA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			4JsF2Fuy38x5T4fSQJsUzzGbcTca7TicSUHZ04JfcyE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			PfQ+g1OVrp2reKoxUY29V6sSev0XbPSVORza8Pa4s+4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			HgTUWKNNKDODh4K+sGwW4Ydlmqb22QS0xe1R27RpXcY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			9i3bLmY8j1wPb8oYLkoXlfbtaxJSCwouObY+wUi4aUA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			RyZlVNbjaAqlDgKugE8FWdRwJhKx7ew7iQzSCsccclk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JtnHExDZopxjHDdkxKvEUTYb16WTqGyOKzA5Al5e17k=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			yRv8itmfa+f7QkgZ03TsPrjc+YcMHHZz9Oxeuc5C+I4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Kswz6NBndW1QRhKZxc87C8BWIjBoKm5mPxHp6tqgvug=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JtJ9rKoWknZ/eIujTeaSz/i9MKoY20wUUxxgY64wMz4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6PWtTGpNSDY/JmLM2KVXntVhoQhJKZUP/moGRbY3NMU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ILRnt9YuI4SFIzANE21lIdVzBhRC5hfsfl+AshH7LAg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0x6U0Xrguwydk0vtv+WD6taAAqHiJqWbh8Lu4dxcO6Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aY5oo718LSyPOZG4Qb5fRsX5SKoZdPpcBvUFEsz39RE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rT9n1bnYSxuc3E3t5Cv8lrL4hPaBqax5mZxiZZPo9+s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			9JmDCKUFNhAT1apHQmrJMHdt/cC5P8LYxQN8ZH4n4uE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			iLUNWK650M4dmALjYq6farPgW3gm+NH48fC1lQOdKhM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			A44yKp9vaji1h68sxVvl0GGmM7q/PGgLYF18R0iSl78=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_A.png</key>
		<dict>
			<key>hash2</key>
			<data>
			bv8lmrmPf6O1qRX6mYDd6bVw6nvLkLv71MjIrD9egXg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			UwLY74C9a5DOCAsP8u4O12xd0BiMsHgnpFltxVZFs/w=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Noxjs6q7sRr3xzWwmF1Jks7Ihf5wRGhKSnhX5wQwDys=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_B.png</key>
		<dict>
			<key>hash2</key>
			<data>
			UdfOWKeeYc9h3ymD/aFxWeNlkU5aXCTJ7WVmxQcAHp8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			/ab4lGjNZ+7qABGZMsQ2HT4zMApaygSOoBWFlqFWoqk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			cTqMhE1xrM6WG5xPyRE1j4QMu79Onb67aelq8HdIewQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_C.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JjlqnA+Gza0G9iWPRj+NeuWQA4sqPYE8yZORgD2bvxI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			HOiAbtjgzSpYP7/HMrNt0qXc1QdLeNaenYYaXMwIO6A=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			dz7Ng4cashE7Y3l7P3S2GwCTJA+WpKn66Z7Op3zMCk4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_D.png</key>
		<dict>
			<key>hash2</key>
			<data>
			XuPfgomTgOMzd4PytXoDORPRTyMHyMHWI9YmnsI+Cvg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			GR0E4udynnuye5YxcDw/0owpk+WnxcNJCIo75dOnNtk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			cpjnfiIqo/vr+O1/IoLmUYcUs8ICTnkwtecNt5v/ofg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_E.png</key>
		<dict>
			<key>hash2</key>
			<data>
			NhurobK2cyjn1JgksNCGba0b23LhZN7bklJK3HmXqgk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			XFT47WlGSD0KD/s9CAGH9cPHATMl/AdAIuOVhihnqzY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			cJkN3c4l4OYnWzJiyolfYVR3q/vzjabr4eDgXsdB1Mc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_F.png</key>
		<dict>
			<key>hash2</key>
			<data>
			e3FV/QgiKqIOeyY80cnA4/2ppsside9CvxLpeeNp1DI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			+8vd0sUKFCLlgOrzKIE+lYA1oWJfoXePUvtS8MxdXlk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Zq8qyri/ymwSMQLtDx8QNBh1kW3iAufsxZOQAaowk1g=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_G.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6ukIn/yIy/XWIRnDuV0vgfreNpe1Jw8ARXnuhfe1BiQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			XBhwvxt8G9pAkuU/Gp/I7WrHBP3S0wOjNk/GkwbXJ2Y=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			axsFK9cMo8NLBeVDBVMP1Xl22kYcGIqG0Ej4aEQNU/g=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_H.png</key>
		<dict>
			<key>hash2</key>
			<data>
			DgQdc4Ml9M1bfLLpRp2Mo+Mgx3+y7GIunGK/yG3LBSs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			VXdUOjASA+9To0lPF4f5S2HCeHBDa00lWi7+ygkkTIM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			qX6mE4dOAycZrdbMPfvCN1xZB2s8H/jt5XU2J3F0thI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_I.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pDA6hzEm365h5CrsZJpq2tCHRZnugcbxJjf1p6lrAv8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			jVKYMx75ElKPF0f9nXJzEvV6BCusTR1B9UG3uBXZFEg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			lChACVi8/bShfFqx3iu1E2/xH6C2XpluTvyfAdSOFck=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_J.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RoqHPsIlaXxr+I5MG5b0wAr9HwEiAIlwdtHOI0Mkf/4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			AlFlVUtHoOsf3992KoSIBVV7MLCezAMUTTrLA4YynfM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CYz3Y6glOpcabSQMy4ZM8Rp3Lk2cxtD2LbiiPSljyiw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_K.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3I/IFGoj7zC9laSLOdVtpa6Rq6VFCLf3b4OlSpBozug=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			yWT4syAxvYU7Is09YRzZrBxH+wh9T1h6V9w1Q9vpDRg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			31zdEML5pciVATl6m7/e0U/vwsYdwS5+skSBfZvWDQs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_L.png</key>
		<dict>
			<key>hash2</key>
			<data>
			eWKAaYBEP7wVsISbGZ4OR/TirQU0KFpOLdmWlfj1YM8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			PXIFP7LAD+a3Ct1Fc1jDFQ8b8ngeng2NVeFcDmwuIcE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			QtjQpa5Qttk4GhPvQYl/1fbkHvHyjmzaLLMLAb2+Jfk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_M.png</key>
		<dict>
			<key>hash2</key>
			<data>
			IpoCiVbTCOjz9wFgrNppvOx9VjLbZek8zTydiFcW40E=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			zYoToGSKyyGZ78u7ErXkJTHe8oA72E5eH9q5r/D9cok=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			JVTB9ZUOg2D+tA+FZF5+r5/4zfcdDmF/zZHr0nSC1g0=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_N.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Q9YkJgOLZLr41FEn3YwSTsDKVkt6qo8l8wnbEMcbX+I=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			5CQtfE2yk8EXzX90xbpswoghCp9kgckTMZhPIXztKWc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			M5fhJnsxT5vGDAvWXL8ZVNqsIkBk7GT7lM1Ay/POn9o=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_O.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7pLdmORuPvpG2oe/AYYcNlCZ04R/G1DcI+zSSAQyDoY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			DQQm3STQbpk+vImSEOh7S24ypgEHFEjlg77Lifu5W1E=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			HpCtgcL1WoUlFA1JAH2gSnatQHruvCTG0E4qWm7XF68=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_P.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qRl2RthilPa9wP9VYbWIDdgFFrHxKfpAgWjrzO/DUIk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			9F0xihW8PFG+HLbcnt/rSZI4G2t29W0VsKtI31S4y6w=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Kkg6CE7q1N777O7FMtIBFyEA/kyZukGqwq0PR8j/t4o=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_Q.png</key>
		<dict>
			<key>hash2</key>
			<data>
			O+RoTBC6getUPo64eWqJBMXXY18SnNWuvTHaRQ93nqE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			V/NwyX8I/y12vyH5Btq9cc6EF6Jqe35KHZBBQbuKtk8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			GETLpfuiNINL22PnuiLSoqnLI4k6z01fOSFfv9SVQ6s=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_R.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5HoZOx874Hv+c4bXKIg+gJW0pnmcEYxqN+1RsCs3FXc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			DzBixcIDfzg54AhJazQ/3muoUOu41QQIGAnQv/pqYAs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			cdvF4KgNf9BQZwJX2EUg/ohCMixqeXw0dieAlcgM9ZI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_S.png</key>
		<dict>
			<key>hash2</key>
			<data>
			stHk9t5pykHkkPx3G4Tv1TlJVL0pb/dooxhrNuGSSlU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			oOZob5taYgTFa20dalMSKpa46NgjpRk+Z5uIIlmNNOY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			S8KR4h7Qg281expnlRxZCmr1k41zts+P79pRte8obNo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_T.png</key>
		<dict>
			<key>hash2</key>
			<data>
			EGG2DExz5OizJRU/Gy6ucEcuPB/oTnyAJyz64IjwOUQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			SlcZ79horPVm1X1dZ5DELm0rsH/uc7ynAZ2iSgAOn2w=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			NLW8LUeYYv3L9wThuufcrk54kAQBumJiLrcq/QbSUSg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_U.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6LVWpbwvukxuanKNqmiN3hMaE9E6s9puC6Op91ceimk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			mtGGdrsuq4xXrZy/MO5FR7N7r8iSAuS/VI+Si4BX9Gw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			1T2ErJzHzP9JIekbpjDqPuYnII7MWs2QCPpaPU3A6qY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_V.png</key>
		<dict>
			<key>hash2</key>
			<data>
			g7KOxIChsvXIixJX3iKiUy/20nEVprsFoEs7x+8tjlU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Y3BFiEH2NsS83KBF9XtnzNFkhr+F5j40TxAnR6MdP2c=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			HBqgu4gblmnm05KtLhiYsnvAoBu20suWfcIqBq58a3A=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_W.png</key>
		<dict>
			<key>hash2</key>
			<data>
			PszOuET/aayn4tjn3yd3jpN8oLNfSeDD1UDiaqjzjhI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			fVQRpq74Auk1SPNCP3cz1dJAbo4TqnKlQWL/OlD9ggc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			n9f3OKWIrmgc2fQPmw+OuCwn1vunjf/xtHYYV8uF++Y=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_X.png</key>
		<dict>
			<key>hash2</key>
			<data>
			i31d3IObjm3dKvB/dfE7xztyoATl/yG6B4oql0CftIY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			qprE+igFuej9XC2W/D4yi8jkmkS2LEUrKfrd2cRrv+c=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			t7CjRV8xze9Qqbx5A5R09uAoPHGGNrU5UpiPYmXPSys=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_Y.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ivah/Gc/zqJFShBX6JS71diEpoVUCbjYs5d5RJTcC/Q=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			5E0IlsZMeQhansHxOdcdXTllr/gAp0uD8kEVEKIb0Wk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			KBHnE1Vv5NUQA9AHOuq25MZdYA76qErMyxT41yHT6eI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_blank.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GCMnITxLjfk9cbEbFhjyDZ8pdArKA6EK3tMjjws4B5I=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Mhip/DodstKEYiga2dlf4L+50/xxu9D7kmE2/XM32+M=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			pZZOk+EJtk8C4W9T3o5BeGRjshEEReVZtrtwMBkG7xY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_blank_small.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5sj1ykKgnDg5D5IpCd6x2bAl0MGkdESNO2lZEGj7qHU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			DT8L9qRjPfEAyzBKYYK1X7BF2y5seRxwIMU0wkbcLok=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			laFavpsSWppAbV1j3UYOZbgDvz34iQUtyL17NAZUDlE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_blank_tiny.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7PA/h1vt/YdjuwvGqU9Pu/q26HK/LOBYyE6BWSMEpEQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			36zo4W9hfilepGECu8VQW6oPxV+AI7zCRW+s7MLgQKc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			/JpnXqaEcF9H/xQsxpismEsEvpOhJVTj5G99C3/BRps=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/measle_dot.png</key>
		<dict>
			<key>hash2</key>
			<data>
			txkEvcEMMSwZnmx87FMzLMtZGo7W3VvGb+aAgst7dRk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CxrqrHG+WO/DXSUCk/NzjASZqbfmAndJ/R9ur9SuHjQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ZQzoPCWvgdpbXAVqxZsn4T2qQ0zh5IFB9cDFhNlF8Zw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			viirrHtoCrqG/Zl8imJsUBfm/G1vJBBGOmjikybNYF8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fbtcX2rTnaUgxH10/0Q27DBkL2LOKyybhsp0eJocs+4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			l05s3Mb0PmxN5EaY2B/rmylVb0zJLpaIIdCh3CVOfMI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aag/pBn7C1B5mmCUY4XaXjItYdI+kxOSvetk11NlPyM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Qp/y7TGBKo5We9C64xiYIvJJhW/hw6cSh46EBRqyrps=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+dg+/X/HQz7XU5A0YjxVVb409jFOvCba/h/Dk0xwgJg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png</key>
		<dict>
			<key>hash2</key>
			<data>
			utLkKWB1FOz3jZ0C25RC8IQUZKhnBiX4Zhqy+V9G1sM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wL9S4b54XoQcChcwUB+kt+Xoy3iIv6m21yovTe6Ptsc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wL9S4b54XoQcChcwUB+kt+Xoy3iIv6m21yovTe6Ptsc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IL0T5NwiwyJ2bRN3Z6ygZRblnRs4uzBD5PJDuQS02L8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1RECfmi94+7AILJYfwCEHpVXHeKTEddmTtN7HK+EdDE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lzM7SlPvx18FzuOhp0AfctCjH872VUYhotrEmV/bjj8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png</key>
		<dict>
			<key>hash2</key>
			<data>
			nxJMmc39cm6sjewdRFv7mVHN62HynJzISegXtemahqE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			gBiXF2YDIq+dMhZNzmXGIviFDDKW65Wwd5MGxawE07s=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			locpte2xHJxxYikj3vpYPAO4Fm0BJbKH3qAMUEHC6AQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png</key>
		<dict>
			<key>hash2</key>
			<data>
			xWNHfxSxFwcTcbRx6ksenSsdQax+gxZj5zqKa4P5bWE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iBDn2L41GYIiiENv/p0efRgu7EM6L1eut92OA3dwsiM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ucj2pK8WExUWdTnnnvWIu9iE9PEOo0rSvhyP+qAhUe0=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7X62FR3qKLKgCi3mkwhoJfSYdhXVzy/pTp6j1wtdE1s=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pvfCkomVwJ0KZwclEtmUR11mSagaN9LrXrn58aC8Afw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LlHYytHV+vHUl3KwWuSN/zlxb96Yk9814X85Rx/RGNg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SMP0F/UqJEhU3S42BOFsqEElTXpSCUb1srWp/BpXFdg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UNr66iN4aiIxfR/cBfo1UzNPIlc0b9ir5OhmySreiQE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NVMsaDDFitszv+dDbTXNQiswF8c3YTZ9FNREzjC0Qrk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CCXpHTB0yrKAhnIHNiDk4HIPn4u1QzjzgGTChebKASs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WhLzM19Q5+lLHpGpwFbDJkMVFJm8Ebzr1T61rTmTWcM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FGqxjT4U1nPLLrwU7gqh/ZvYN5ZDG8Tn3F6jcQDoOdE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MvWHspQx9qKyqW1Xz1VgLkn7LgFUPjvzC4Cg1i1hI3I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kaond3f0Zgm1jo+dYjNgDjHO/E+bureyujiDFhY944U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			78c23rPKmoUWoxwAr4gai7v4ZKSU2Y1CFIaiqTWkGQ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			dFyskRzAO46yOc30ZAjTyNzlFxCU6e9c/tL88CpPf7s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LJvkr1P63cbLU8WBQ05wPjBep8Vxlg8q4NKZE4ljOjU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DnkqDBZB18vUtOUPDil299TDwquQEoBWn0HOT0ph3D8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4lbIplYNb9kl05AXRo+4wvOXJ4ogXNUVRU4YnSi4D0s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			xEu7IGmDw+EB8y+0doz6TxwqznL+VruuiHQ6WJwH6hU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			R3NEF/P4YXdD/fpu/dqd8EZk+KkVGf8iII358CJZhQE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png</key>
		<dict>
			<key>hash2</key>
			<data>
			esJZo0DQ/iA9fGTeZgnhHBaaXjKiwF0Q+D77afKUdtQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			85j4wRMjUhbJzw2Cj/dcdiSaIcvVJMiwY8pyr9uAvoQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CC5oWJdSl7qbV3Nhcm9VlpgXL0MZNtSwnDkXPFFjOAs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png</key>
		<dict>
			<key>hash2</key>
			<data>
			af0tM2SZMSGEXPSOUwN7JZ7as/APH644Mlk2l4IWqms=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			2tW3XA24iExRY3gHrbutr+hAVYLNOk3LrSXZmHRjUIA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			02zlKov8Ubrnz7aJ/JXa+qQ073+mO2gy2CgeYrDNF18=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZELicqEYSjWiwLH5Bpm+L59wifWIeso6rvF1uk5JPN8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			g+br70qT4Wo8OHYO9GEnd5pkjYLVQsyw1W8T8+pd7Xo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			l/lLCEplaZnTitN6/IXGuLZM5QXaxaSim9fc8XsRb3c=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz</key>
		<dict>
			<key>hash2</key>
			<data>
			Vvqhoi5smgHc1vOiwl7EFTGWkUE4EFjGrdsPe1C0xdQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			RuVnbZHSOcUiba/YYXA2AGV5xT4566OucnDf6uknlRI=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			CwRstjYL7/ETHmctk2Ivi7jc+G+3aBXMwqX3M7cWbG8=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			dfQ+A971Rqqj5KRbTrfBr1iBJB0esQoX5Wv0VfjDFDo=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			iIp24BkfVXA7+2Obi9t+YK8RluhX41lpQ9vbdITfhLk=
			</data>
		</dict>
		<key>google_maps_flutter_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			gFWtFkwxrAdovv57cC6oY3RZ45DzkBgEUMARvJB5orw=
			</data>
		</dict>
		<key>google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			puoWk/6MP69ytJdvlbT3CeVa2vnstAliuu/Zl9LBVPc=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+zVMz3Gll45ijEO8VAuUqHwVLPvCPPcRCBJiBQRoCVg=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ETZWiZY6EZHpaiLgs59i8FuG0NJKvoBAXBpc7vCamxs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
