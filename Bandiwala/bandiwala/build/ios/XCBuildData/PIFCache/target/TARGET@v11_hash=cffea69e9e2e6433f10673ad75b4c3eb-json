{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d394bc4927d3d7857746a6aedde20926", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986680594b8dfde228f799c9ba96f14a44", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986680594b8dfde228f799c9ba96f14a44", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984c336903caa221479a233dd0dfc6dc9e", "guid": "bfdfe7dc352907fc980b868725387e985313256a510b011e2759536c8a79f8f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b2b920d6e005de2eb4e89c3ace824ff", "guid": "bfdfe7dc352907fc980b868725387e986743ba96c21332df0a802c8cc3fc6310", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981289b04a7ee6a9091f9d9beba9d944eb", "guid": "bfdfe7dc352907fc980b868725387e98565531d315263341b6fb457436d338af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6c1df7290d78c6890905ad2a265858a", "guid": "bfdfe7dc352907fc980b868725387e986c822d96e5205157f671717041302615", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbddd0c9a2587b001354e980b6496483", "guid": "bfdfe7dc352907fc980b868725387e98a8f0513fe2893e92e96324788d9b4917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98831d86f757d172d79f71eedfbd054220", "guid": "bfdfe7dc352907fc980b868725387e980a4b0972a59be26e570895e113aa9912", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d740a4082502af78afae0b1497796b1", "guid": "bfdfe7dc352907fc980b868725387e986606958bfd72412b745608a042669811", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f966cdb254f1177012944ffa34f8d1", "guid": "bfdfe7dc352907fc980b868725387e980b7634dec2debd5d4c48294692593eb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98099e7c1d29ce83e97bb9e1bd87fa7c7b", "guid": "bfdfe7dc352907fc980b868725387e9894da536d15f9597ff92bc3ccad6a9db3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980028530c3c04e118a87beb89d51d6d1f", "guid": "bfdfe7dc352907fc980b868725387e9842e6e72d4d489d6b1bb0a76d186163a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a71afd32423c2c18eb3031347272e761", "guid": "bfdfe7dc352907fc980b868725387e9891b95c06c7480471d65ca2af47e60a75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863c01e9c6f156cba1e36beb748948586", "guid": "bfdfe7dc352907fc980b868725387e98145b55a45b354d0d7da410d0a400fce9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98568a6826979471caa2ab469b4f201ab7", "guid": "bfdfe7dc352907fc980b868725387e98a9069efb26f01aace13eeff6aa65835e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c33dc9096ea2036625f2d42bd9321700", "guid": "bfdfe7dc352907fc980b868725387e98a1f80c37e9a69972286b4ae9c0aa1945", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf77a70cff35d448e0233edbf3b4c97", "guid": "bfdfe7dc352907fc980b868725387e98ef885bcf0be6a431c09a6322b5e67395", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a86dea0995c4718aece97ef0aba76795", "guid": "bfdfe7dc352907fc980b868725387e983d066d6dbf30db01da57291e503358be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98302fa52bac38047fc0ebf4c3eda9f874", "guid": "bfdfe7dc352907fc980b868725387e9851b2c36537d4687535c1d046324f2ec3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853f0a314a401808ca85cf13818f61d7e", "guid": "bfdfe7dc352907fc980b868725387e98e4c205df6e9079b27b0f6be3be40dcc6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd142fc7f97fb0d19fff31ba6db30824", "guid": "bfdfe7dc352907fc980b868725387e98f29b3a6642e877e0fce3e5b8c8ee08df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98436fcc80569ab6f57918579c3fbcdefd", "guid": "bfdfe7dc352907fc980b868725387e980494face2df63d55bbb1a332ca6d31f0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b4bc9e037f14a997f62d0c5ae657ab74", "guid": "bfdfe7dc352907fc980b868725387e981029880a3e3b2ec153689d4873394e12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e2c7b6e6309225d38920cce1f4e3ad", "guid": "bfdfe7dc352907fc980b868725387e98e7be7bb2e0d8cc5e2e08bec15e120e08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863be8b38fa9187433bd83ada522cba74", "guid": "bfdfe7dc352907fc980b868725387e98c98863976a8ae6299503093d4d2641cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0466e5e4f70149f0dcb61409ae76303", "guid": "bfdfe7dc352907fc980b868725387e9898484fa5b84bc6c0300eec3acf4f0072"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd743b84bb8321bbac037518a518d4b", "guid": "bfdfe7dc352907fc980b868725387e983b72a2972e9f23c919aaa4b127b75a68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823cc12d53ea145f7463ad3a5d4681ccb", "guid": "bfdfe7dc352907fc980b868725387e98883c0000cc9e5b3caa4f8644a855a36d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ded6b730f7c21f6ac41617176a04c5ba", "guid": "bfdfe7dc352907fc980b868725387e9813ae5b89b4b137a5cfbd2a01f85078ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e27e2b3ecdf4e5fc28ce1a62934eb660", "guid": "bfdfe7dc352907fc980b868725387e98cd6560d4a62dbadf11e38c4acbb178ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98996644d266212c7f850d5d65457ff1b3", "guid": "bfdfe7dc352907fc980b868725387e988c99b238362c1a1013ce7b31fc8684fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856f5d59b06e6a9a0158bec808c83a081", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d32e4ce5b388f9f6219bac272b7a2ebe", "guid": "bfdfe7dc352907fc980b868725387e980ac696b6a17a1a111f803753c7a9a6a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f688b1df0591c782d0bb54e986586a7", "guid": "bfdfe7dc352907fc980b868725387e987ee4a3956ee46e9a6e4f1a1eb8d5e24c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98266e2445a753f3565d275d7b6615e755", "guid": "bfdfe7dc352907fc980b868725387e98f1e73bf9ef5939b987dcbc3e580685de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd612d6e915ac03cdaa10e34ce66462d", "guid": "bfdfe7dc352907fc980b868725387e98298cd9d8701de03bb9dd94e6a103156d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987439fc329440992440de5ab347402b7f", "guid": "bfdfe7dc352907fc980b868725387e98a6e229af330981254295c67be4b1cda3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861b8920e2db371c0015fe21b247399a6", "guid": "bfdfe7dc352907fc980b868725387e98914a66fffb6fb11174e8df60cade589c"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}