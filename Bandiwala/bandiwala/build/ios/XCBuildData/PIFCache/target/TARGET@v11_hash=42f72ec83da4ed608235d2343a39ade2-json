{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f86b6b31bc6d7ffaa57b3ff059464b0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981b07e78648f4387a080c7312cac56f42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981b07e78648f4387a080c7312cac56f42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986f876aba40b17fefcd9c033267880973", "guid": "bfdfe7dc352907fc980b868725387e987f28047e02a7d34a46f8ada17b829c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d4f3864e9606a8bc909524328a6c8d6", "guid": "bfdfe7dc352907fc980b868725387e98f6c67c4679e7f7927d29cc6777b4854d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982059717bc43e94b9c6ad1ce63e451c19", "guid": "bfdfe7dc352907fc980b868725387e98fcb7e61931146dcd711e99248d6ccb4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb12e7eb912c1560420169daa13c637d", "guid": "bfdfe7dc352907fc980b868725387e9895fe4b33f65c965da7dc45a450744ad5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cb8841849e3e432559597be43e9c7b7", "guid": "bfdfe7dc352907fc980b868725387e9881f94adb811a31f238513440b73eb9ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837269c4e542d22b69a08167af032660e", "guid": "bfdfe7dc352907fc980b868725387e98d2993dc352971d9dec3337f6492eedd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f4fb108d3bd4782f849ee1d4f4ac209", "guid": "bfdfe7dc352907fc980b868725387e987744d4bc1141e8c3af7814a94ab5d191", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b01ff76a024eddf94cf4a0fcd173d044", "guid": "bfdfe7dc352907fc980b868725387e98d44ce0ad517ce468c11abe8a1a7ee842", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f85104c27a03c23699ef1b9cbb9d2a07", "guid": "bfdfe7dc352907fc980b868725387e986dfc835f86ba61de853986e0c332c9bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827f60f2d9d7891b61057751e403292ad", "guid": "bfdfe7dc352907fc980b868725387e98832bcfeadec19c9672e7d13bba695944", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989768ca5f8cadb27f878fc6ab22c0527a", "guid": "bfdfe7dc352907fc980b868725387e987522b569e1888c59443fabbfd9eec3d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e09edbdd279dd8470b9196bade7288a", "guid": "bfdfe7dc352907fc980b868725387e9826df2a365ab1701d04b81b250e704469", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987340ef7ac6db6ba4d38360807ce1110a", "guid": "bfdfe7dc352907fc980b868725387e98683700ed014e2584117d9919dc247bfe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bed5f3266c756d87c459acfd8aac4dcd", "guid": "bfdfe7dc352907fc980b868725387e98a95b46a3d3c80a8cb5734bbb7717809c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d12943d0d6ed3bc2f7696528b9ba61", "guid": "bfdfe7dc352907fc980b868725387e98c814b053e6b465d54fefe875995c908b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46b74b120f2c861870ccd5a50923e0e", "guid": "bfdfe7dc352907fc980b868725387e980e4020bf220b21c849a1247a598ef500", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889c00983fa57d314b61dd9075b3883cc", "guid": "bfdfe7dc352907fc980b868725387e9867e1d59733a195924e43c7221ab3b507", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fac0658cde3c1f075ef66f980d4f7019", "guid": "bfdfe7dc352907fc980b868725387e9836a664f9634f39c09eb263d32eb33de8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98110be04b3278f0a6e0a72e7b3909662e", "guid": "bfdfe7dc352907fc980b868725387e981012013b7c46227b76d2a26c3973c5f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807feada0fb58bb5c42272f83ba4c88d4", "guid": "bfdfe7dc352907fc980b868725387e986adce0f63de9503184a2571c68ae8037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b2df65abfd9bb392b692c392b8b5527", "guid": "bfdfe7dc352907fc980b868725387e98c97111306fd51e26ac236698d182d1fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984810780cbe4e949fad921751f890bb39", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce8248d0fe656487c3fd04952eed9dbe", "guid": "bfdfe7dc352907fc980b868725387e98c97bce089e46629de083eac4838dd800"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98696e7a934cd62fc776fced745fd50b96", "guid": "bfdfe7dc352907fc980b868725387e985817ad63c8e4f8db81700ac14cc618cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe6853b64599e4c18fcd77737f97bdb5", "guid": "bfdfe7dc352907fc980b868725387e986e494a42d2c0843cec709c09ffdc69a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ce7d5a2e542220505a56d8af72c1375", "guid": "bfdfe7dc352907fc980b868725387e9805c8314d750be278604bc8677fcf4420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877037b875dbac39319016372968f4d76", "guid": "bfdfe7dc352907fc980b868725387e988270e34481adcde5ab4b6a9daa89a011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e5b57a9bb1831c9d6ebec5e6c58e8b7", "guid": "bfdfe7dc352907fc980b868725387e98615ad6c8a17ee1f04050629a5a2a260a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd29150bf9a951be1931b6383b19c35", "guid": "bfdfe7dc352907fc980b868725387e98fd9db82003eb1b08fd6976b7ba6920aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805d0408e0e0e49aadb591ae940f85bdc", "guid": "bfdfe7dc352907fc980b868725387e9836872f22a07d006d803b66800b0fd898"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981745184723784ce571e1a4a78b97f32a", "guid": "bfdfe7dc352907fc980b868725387e985cf4803afdb0ccdba56bf5e79afdbebc"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}