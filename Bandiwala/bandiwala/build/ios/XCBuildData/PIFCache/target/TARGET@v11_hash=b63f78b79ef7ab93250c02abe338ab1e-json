{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986467395957fcd8f16878f687660d9193", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleMaps", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleMaps", "INFOPLIST_FILE": "Target Support Files/GoogleMaps/ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "GoogleMapsResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98613eee47cf5f630f7ece3fdde3906f14", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d64a564f34583512c2b54504bf0984b8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleMaps", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleMaps", "INFOPLIST_FILE": "Target Support Files/GoogleMaps/ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "GoogleMapsResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a664f06851e0ad0905736fb7fd56037c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d64a564f34583512c2b54504bf0984b8", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleMaps", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleMaps", "INFOPLIST_FILE": "Target Support Files/GoogleMaps/ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "GoogleMapsResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98956fe3bc8b7c7208ca0c97f935b6a91a", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980bcfa7b14aab945769d5de7f919e5a12", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fd0a0519b6837a0145c79473049afbe2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9842f88c62b02970337349444ff92e2612", "guid": "bfdfe7dc352907fc980b868725387e98b17c37d29857e88980d106a25aba8d1e"}], "guid": "bfdfe7dc352907fc980b868725387e98c7734795eeb379d06eb0f7ec156dfc58", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9877354dc0c1379e634078de2da2deba6b", "name": "GoogleMaps-GoogleMapsResources", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e1226e3627f386c3cc556b927e8c995d", "name": "GoogleMapsResources.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}