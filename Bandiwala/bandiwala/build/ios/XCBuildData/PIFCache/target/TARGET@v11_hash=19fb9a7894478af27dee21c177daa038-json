{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98650b093d402c0acf4eb37ce918f21ceb", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3c161fa41bde276ac4ec087b656689e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3c161fa41bde276ac4ec087b656689e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98beb8af698f8e99b3fbe06e8ff2bf164a", "guid": "bfdfe7dc352907fc980b868725387e982ef32830090a5f180689e16980928fd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b11ac24bbe9739bd8cfd44697e58880c", "guid": "bfdfe7dc352907fc980b868725387e985a916ced4ab8bf3396d2522efbf655a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7166abe2647b501c189e23394ecc6c", "guid": "bfdfe7dc352907fc980b868725387e98ff53f399fa1e2ecb7849daf4286e9ade", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2d82483788fd3ae5527b013057fc446", "guid": "bfdfe7dc352907fc980b868725387e988ad8e0322ec48311f0d9e5a75f54c724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc4ad3ddd3170fa0d2f917294369e0aa", "guid": "bfdfe7dc352907fc980b868725387e98b11ed937bba18c7408a49a9a028bb33f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5e82cb18bb1270e13c6001e5239a4d", "guid": "bfdfe7dc352907fc980b868725387e983d80b84cb41fbc5fdcc8bc3d451f292d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984170e9d8eecba91b2ed9267a959b1c21", "guid": "bfdfe7dc352907fc980b868725387e983bf5db94459714fd2562bfa5ba3794e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9505697e71babbcb853e3c6884c4c1d", "guid": "bfdfe7dc352907fc980b868725387e98b4cf2c1398499b177aa5f12416a2cefc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98139f97f1e2eaf3e2d3513f9dc9cd405b", "guid": "bfdfe7dc352907fc980b868725387e98a5931e111640051bba64bb234ef28a63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c40acd3bdaf1336a8b29834c9709b8b", "guid": "bfdfe7dc352907fc980b868725387e984e2d3cd6dd0a8b8ce5a9345bf5eed7de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889b25747b16697db50057efdfa0c5239", "guid": "bfdfe7dc352907fc980b868725387e984bcefc59b1341fdd0e8bad71dba39c92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980577c09d9d664469c9ca3bdcbd98e093", "guid": "bfdfe7dc352907fc980b868725387e98c7eb3ea66f1fa44a1f5858283c92137d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b165ce6eb79e49189c69478c86d9466", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756c5fe62d8c266bcdc6a92f6faffacf", "guid": "bfdfe7dc352907fc980b868725387e98cdc5844f930b9d0fd5308dd5856a500e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f256ec6524caa260d4fdc368ef67eec", "guid": "bfdfe7dc352907fc980b868725387e988f5bb97be79cc2d8abff7c92bd4b7567", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2903aa45fd3a7f3e98577395ed76482", "guid": "bfdfe7dc352907fc980b868725387e983de4f1e8f5a97a6028a6bb34227f49d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee7513e741aab5a580e9363c7225aedf", "guid": "bfdfe7dc352907fc980b868725387e98f2f63511bf606bb48ce3eb1b71b306f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a926bf3261b4d60b5bad2e859ffbccba", "guid": "bfdfe7dc352907fc980b868725387e98327b38ee62f98da7d09431ea5004a053", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671acd43951f13c80dc58180f8ceb9e3", "guid": "bfdfe7dc352907fc980b868725387e980188baa19b7914aff2e34c8da54773e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac08b091af3a687666fe7f201b56d016", "guid": "bfdfe7dc352907fc980b868725387e9812d25da0977d54b4f93c43814429ebf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984992f4727f35d20a83d2a9ad2fa84e31", "guid": "bfdfe7dc352907fc980b868725387e98b9ff284581573c4ba830aef19b6cb271", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aedb11fbe0d8a0831a4ba96a3c7360cc", "guid": "bfdfe7dc352907fc980b868725387e98938189fe00ada6ed049fb839ef6bf7d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f69bb56ad83c9b9313fb3b367c606d61", "guid": "bfdfe7dc352907fc980b868725387e9864333a3fadcb2636b3f7fea467c141f6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fec5b4e4d31fc7e8ec31d6170dec8ec1", "guid": "bfdfe7dc352907fc980b868725387e982a61503d8185fa65d0d61935791efb04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a512a94d373c7dbc7de0736169059f94", "guid": "bfdfe7dc352907fc980b868725387e9835220589345efb92af42631dbf6b9459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98083ac4d51dc3cbc1b3d5dce2ac861692", "guid": "bfdfe7dc352907fc980b868725387e98c4ed4b65682f6200b53ab06f33e7c896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98371fac6af78c392f149a0cfa894c2ad2", "guid": "bfdfe7dc352907fc980b868725387e98f67d51cdaaf5e1f78be421f374efde62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef3c76979c7fb4fa9ad614d800476fe0", "guid": "bfdfe7dc352907fc980b868725387e98aa97ddbe032d174d7e0e5ddbc64b9d5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cb269a9601d4d96a40210608a390053", "guid": "bfdfe7dc352907fc980b868725387e98ff8928328841298b0ed62804c598770b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98469c6b7ffb5538fad342735ec8b554d8", "guid": "bfdfe7dc352907fc980b868725387e98ef84c0ffd43efae0c864668786839def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f120d6aabfd5f63f26cece7c9dcdec9", "guid": "bfdfe7dc352907fc980b868725387e9848cadd1ba7bb24a85b533aabf8e22799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98848b5925bbab84e2f4ce26f8a16a34dd", "guid": "bfdfe7dc352907fc980b868725387e9821497750e138db752421f5224d128654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f408a1058c653718bbafdb6b2264b366", "guid": "bfdfe7dc352907fc980b868725387e984a4a9db5329a7711e351ab57794c53a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98624b1837bb3d8bb01ec1e545a6a9df41", "guid": "bfdfe7dc352907fc980b868725387e9833885db617a4d69d1571730987fbd3fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8617b469dc5dc5e68c363cf494c231", "guid": "bfdfe7dc352907fc980b868725387e9824cbbfe0ddc5f68e6717f61ca670184f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980744e20a459c9fc4e61cc3eea80577f1", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c50332b76b6dbe58f8a80b3560fe0f2", "guid": "bfdfe7dc352907fc980b868725387e984e5371b8cd4eca1a3ea6dfcf7d307c37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dabcc304ea1ebcc927053162311d79f", "guid": "bfdfe7dc352907fc980b868725387e98d3572e24141325a30bc3c0a31c41fa85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf5ce238dd3c01f8d542c7318b232e46", "guid": "bfdfe7dc352907fc980b868725387e98b2f53712d1d0af9bc7d13919388a2c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985597d75428582f57b5c9dd83fc75f16f", "guid": "bfdfe7dc352907fc980b868725387e98e29bf2527a03f8a7f0ee4f8fb682157b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c1bb590082a72babd2121ecce467e40", "guid": "bfdfe7dc352907fc980b868725387e9827d1f3d71d300da27dcd08b0f8c4d523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f6443141a9742bde26dc66d2130bb5a", "guid": "bfdfe7dc352907fc980b868725387e98eee17b6918192a58e3242e8202b1af3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98455139085decf7f40dd7c0563ce37fea", "guid": "bfdfe7dc352907fc980b868725387e98319f0ee3ee4a0d94236a327f7214191b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880dc1e0b40921cebf8776e684faa2606", "guid": "bfdfe7dc352907fc980b868725387e9889d6808f96db31c0fa88901646d50905"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}