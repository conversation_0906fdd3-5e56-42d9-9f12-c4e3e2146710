{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c329620c51892527db69ac984ef9321b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e986eaba3bbf34fffc52894406988f981b0", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9804db47a3ceef83edd118018eb43bf272", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a771e8847c6531a9a5a06e3bf8b1cb84", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984142e59a488fe2bb10790b24cd5f6197", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9810df5c4db0421c8be28019622fd56b37", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f71e7b90d533942ea1aae8d07faffcaf", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98697b447066610540e0493b62900973be", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da70976aa7192f4cd57d7145c3a13f80", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e3739a9269f19989794710c2e6d8bbf2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/ios/Classes/FlutterEmailSenderPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc72909fe1d9c1408f972c4ec3e4ab27", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/ios/Classes/FlutterEmailSenderPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980fda1a4cd1155758e6c6eaef93497e56", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/ios/Classes/SwiftFlutterEmailSenderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985a43557ff5a07bcad06d937fad6121bd", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984553d46ecb48cf24bf4aacc2c845db06", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9837ba14e1a3662f2b514656351225920d", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836e668517c345ed2e2580d1e8c5801bf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892d147e3ac1d00a9332f84eae0d54eb3", "name": "flutter_email_sender", "path": "flutter_email_sender", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afae7442a2cdc5eb3609299f7ee39ab5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d98aae7b59ca49bd6255089a4e95d88", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839d15d3086fc29b40d1995417c433596", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826408deef529fbd018ff36355721838d", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873c1dbc4a09e2082cc9fefea14042e83", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986fb7c9990125811ac288ffacf5e4ba15", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fe0c5b71a01adb4cae5e6f28f9a326a", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834493c36239d1d8b08a983f117a28d11", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811581833e4bc5e17e90f84683bf53592", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f296d328a391f301a6fd7bf1f1b154f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807234f1cc177ab6a5eacf49230a4bcf9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d233d5b25204d297dbddc22eef9e09b3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bca09bb99f0ae4696f04e2e6bd62a388", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d4e749af3ce36f8e89395bef651fa36d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/ios/flutter_email_sender.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e989da9e3484cb70ee719d4f9b348945eb4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d8a5653e1db6d1ecd6a28c9f366b2ff2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b93ac70ede81bba73aa04d2e83e1613a", "path": "flutter_email_sender.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc134165e44668a38a517998d1ad7c71", "path": "flutter_email_sender-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9808f911668a15737ed0f50144dc9c804c", "path": "flutter_email_sender-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae69f64e4c9c30254dc23d96f6743678", "path": "flutter_email_sender-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837accaa619f5e2a1039e31ba08b54c0f", "path": "flutter_email_sender-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9872dfc1238e4ee8e5490d9a906a395508", "path": "flutter_email_sender.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9823d79c3679ce0bff7d0b3f242b0f635f", "path": "flutter_email_sender.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98faccbff4717c805a9c1095153c24b1aa", "path": "ResourceBundle-flutter_email_sender-flutter_email_sender-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98acbbd130225d741fbebafe9b31cea627", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_email_sender", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807ebd04d36f9f94ecf1ff5b1dd10f3e2", "name": "flutter_email_sender", "path": "../.symlinks/plugins/flutter_email_sender/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e8bd0aeca28b1f5d176de84ac6c5da28", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef545de851e85a0c051f06e55d5827a5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStoragePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982cfd16e6afaf7f8ccd04198d00100566", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStoragePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a440ec61cbf4fddaadaa8da586616815", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/SwiftFlutterSecureStoragePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9883461590183638beebda7eab9dfceafd", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9825be4c09def8f304172e614b10fe56f8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982a6b703e1d5529462ff005662ad65415", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856d907e7d3f49c0aebddb885bb748ea0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae89019e05914b914532b09a42ada6a9", "name": "flutter_secure_storage", "path": "flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989306bea823249ebe5195401d743b9751", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861bc67790189a7c24a4ddb9b746fc0a0", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c86627e9c26b187aadb7478a4b83c27e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c3cfbc89cabf3729f01190840c27ac9", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894f7aac27a5bccd8ffdc8d33b8dfc557", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9a4d834419f8e06334c04a8d355e154", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c505854e1ac6cbd817ce67feb27da16b", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ebe48e3d11435f44fd0662216f3e4b5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807c273f9b1f166053bec1ec2b10ba108", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985393e363cca68a51048011c36b730727", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df346e18ecc51da4568e0d27425f282e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b445e8fe5b651c522583eed9898dba96", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981903e665f8a324762e9d520cb3215340", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b2fd30804b413a27e67f6f9502896265", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/flutter_secure_storage.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988e8a430e48a8f8b8ed8192a780f375a3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980e51de499429cb9b52922b30188000fd", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fc1cca031db88066a73a213477ec66a0", "path": "flutter_secure_storage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f4dbdec6a7cb610d18ac432da252d58", "path": "flutter_secure_storage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984c2efe5445ad4c090b0d914d09dfe431", "path": "flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b40792d11c96e5b366f38d143b638185", "path": "flutter_secure_storage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b513c5df4b447a5cc6797500eaf5985", "path": "flutter_secure_storage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9898622822ed969961904aa9338b634824", "path": "flutter_secure_storage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980b50f9908097fa4b46034cf86904296f", "path": "flutter_secure_storage.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988236d85bf1076c0e5e7855b938e55a22", "path": "ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c809108550f299b1fd35e1b42e026f4b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98122b18761b87db8942fc51e8a4cea9f1", "name": "flutter_secure_storage", "path": "../.symlinks/plugins/flutter_secure_storage/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e837f0c8461fdbe4310e0537d911d1a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/FluttertoastPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef1c7f43943598197637d9ba084feb98", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/FluttertoastPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b46f90691d57ef6f038735a27982810c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/UIView+Toast.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6cd6aa574afda6736c60d29e0421e7e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/UIView+Toast.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98509465a7f7a6424ad835cb789de16b87", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986e459efc3fc245dd55396fdf85d6d732", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d63319a1d092f32a93c30350380aa348", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7662d19c621cbb9cecfee78dfd97625", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98932a4a187494469c63eb24d2a0441887", "name": "fluttertoast", "path": "fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a67d13db9052abcbcef0e3577e00df21", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818a8d7e1680e66e07b048b894a32a8b7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98376ae471391377304128d71f901dfcc5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb990a9b91d221506cfdd35569439516", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1648bac071ce9da7231d439df3a1f7a", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98988038ab2d90b1e31d1d8b39d81fa52c", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e860a13b1adb25ce340fdd6faece437", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872d6a49c7879cd58765f54e7e212c6ef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98481e1bf412985f8496a814cab280b288", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984769102c6bdb5e358206f3592824cd73", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836560cfbde347bfd095b809f85c05d56", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7173926dcb27882b574ebaa551e2675", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875b59e0668e3a4aad766d6b6b79cd604", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983473cb45578af6c10416b506750d0974", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/fluttertoast.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a96fc1396605c3d9e9c104df25f3e74a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984aed694035540c3ef4bf9f34170849db", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981626f2ae9004d4e33524ff39b760a8d2", "path": "fluttertoast.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a2e952bbc43ce4e641922b9504f3a0a7", "path": "fluttertoast-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9812b5dfc72a4d4ba171a102003fd3befa", "path": "fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d60e5419ee63faddf61dd03d6a59d25c", "path": "fluttertoast-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e68f0adf19f4f9ce6ed0a8c4ce3ef27", "path": "fluttertoast-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986f87e62253c35d045ed3837ffcd133fe", "path": "fluttertoast.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98549cb48ceff06a92c26dee213d018c2e", "path": "fluttertoast.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9804b16f787cf782c4703f835565735cd7", "path": "ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98baaa30d01cc806af612bd5647d6c2f9d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98291928aa8294fd8d9abc49e91b4504a7", "name": "fluttertoast", "path": "../.symlinks/plugins/fluttertoast/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b5243147786b560041a302ed77a92dd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982855a625d807819a61ceb8fb03adf7f4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833ea43b052fc705ff5ef5d38a818075f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fdcb40ac3702ec33355c4e1ef74fb823", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2182951e334117cd5777c5092fa4afe", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/Extensions/CLPlacemarkExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899931a9c688fe498c99c93ea1470a51e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/Extensions/CLPlacemarkExtensions.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98550e0c6ac418700e313001ce7df61eba", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98989e8b818b73835a2739152efa133781", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed52b737cc83fd2d95cbe3285a47d4d2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824f10d082bbe79b705b0860ef0a75d1a", "name": "geocoding_ios", "path": "geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98389e6d12f3f729abb3d1975b7fe85163", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a701546bc7b53f7e7de595c8eba5b6a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985db23a62c0101faa65678d3cf640ed25", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897a36141c7aabcdad118bb66e92a441c", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a1491f1e2674885a36a62be488acf89", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b18e24026ed5bbdc4ee1d443a3601b96", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882ce734235e1277454a4a11890dfffa4", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98097d2f9611ce629d0516fa6a7031ec0d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98286e056200279c1af7fdfa291def7758", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98140a3da99c903ea4d082bbb2349516b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98751ac2bae5d7ac40c71fa3b0c706e5c2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ec6230d331ec53ebada11de7bc0dd36", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d161bb5de6e8c9105a4481c3056d63ec", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98fc9458b458e35be6ca9685b2ba1f5e7a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/geocoding_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9875a8a3a729b161510ba792f16456f911", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98791be48c7d23f55094371a553457c41c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a4b3840eedfcb630d4b6fc11c80dfd4a", "path": "geocoding_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986d7bc43e66a01f9dd0e8cfcb21eb7d05", "path": "geocoding_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b4798fe95aae330628eb3b8f52c899b2", "path": "geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d355ad047136cce98e1b1f99e8b5b98b", "path": "geocoding_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce676d1bf1af80aa0590cbc21210655e", "path": "geocoding_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983d4b750bc6ff63a0d02d777b3ab6b680", "path": "geocoding_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bf690b58b029326d3730b23d90d11a8f", "path": "geocoding_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b30809c091e32cf257467229b768afb1", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869c3a29cd0f696cb4a5430ebc336c736", "name": "geocoding_ios", "path": "../.symlinks/plugins/geocoding_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce8248d0fe656487c3fd04952eed9dbe", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/GeolocatorPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9847f029cfd1fec6d73e9799f7c20a6387", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807feada0fb58bb5c42272f83ba4c88d4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Constants/ErrorCodes.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f8552b332ebc3220fdb0d80df8828eb2", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b2df65abfd9bb392b692c392b8b5527", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/GeolocationHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98696e7a934cd62fc776fced745fd50b96", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/LocationAccuracyHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984e5b57a9bb1831c9d6ebec5e6c58e8b7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/LocationServiceStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988bd29150bf9a951be1931b6383b19c35", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/PermissionHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981745184723784ce571e1a4a78b97f32a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/PositionStreamHandler.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ef9688d625d0bee51febec8304cea068", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837269c4e542d22b69a08167af032660e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f4fb108d3bd4782f849ee1d4f4ac209", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/GeolocatorPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b01ff76a024eddf94cf4a0fcd173d044", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/GeolocatorPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982059717bc43e94b9c6ad1ce63e451c19", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Constants/ErrorCodes.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98529e90f195aabe14da88080f18ac7826", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb12e7eb912c1560420169daa13c637d", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/GeolocationHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981cb8841849e3e432559597be43e9c7b7", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/GeolocationHandler_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f85104c27a03c23699ef1b9cbb9d2a07", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/LocationAccuracyHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987340ef7ac6db6ba4d38360807ce1110a", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/LocationServiceStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bed5f3266c756d87c459acfd8aac4dcd", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/PermissionHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e46b74b120f2c861870ccd5a50923e0e", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/PositionStreamHandler.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dea651ea37c7777a2709b87ef081e9b3", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f876aba40b17fefcd9c033267880973", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/ActivityTypeMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d4f3864e9606a8bc909524328a6c8d6", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/AuthorizationStatusMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827f60f2d9d7891b61057751e403292ad", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationAccuracyMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989768ca5f8cadb27f878fc6ab22c0527a", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationDistanceMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e09edbdd279dd8470b9196bade7288a", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892d12943d0d6ed3bc2f7696528b9ba61", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/PermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889c00983fa57d314b61dd9075b3883cc", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/ServiceStatus.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853cfb084c3a92007b51ca0da6055d243", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98630341824cd50486435cb7182597a18d", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2fa0aed916a8328ec1ecb3a99d4352d", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fac0658cde3c1f075ef66f980d4f7019", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/ActivityTypeMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98110be04b3278f0a6e0a72e7b3909662e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/AuthorizationStatusMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe6853b64599e4c18fcd77737f97bdb5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationAccuracyMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ce7d5a2e542220505a56d8af72c1375", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationDistanceMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877037b875dbac39319016372968f4d76", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805d0408e0e0e49aadb591ae940f85bdc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/PermissionUtils.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987a7b8fac99bf91943935903f83d7c2e2", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3684140675df9d6cf8fe7092090a988", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989be173684013e4b6853a3ca9a5668dee", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98707611713b3e7d79015cd69779c416e6", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98637da6223bf13e22bd76140b2114d73b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8f27584122742aac767247b579a305b", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb79bbc325266617b318b74e23df5acd", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850075a0a7f28dc0899c47634e2a4da3e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9df3edbc84982b2de1ad769174b5f75", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f918973b86d1ce5cb50428201a43bc63", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cedf97506ff606f7cb5d66bd5c6ed90", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab26fa676babc7b3d1bcbe3a31888af8", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebb5d6d1fab3371dc58bfbce6ebd3bfc", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98512112193fa64e0ebabe5a9e504bff2e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98360105027207ee3153b28b418a14c0ca", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d00285a48542773bca27ada3c5bd1c8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d3ca8418961ee00bd0104e8fda064c2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d26497288e692feda541178e3ca1ec4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b727ece9b9abd429a865690af859f86", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98024e93717cabdeceed29c3f2a9c1376c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984411e6d71178417791de1c9ff82705dc", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98fd77b17560d6df70582cec749ea01d2e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f7de5f2884ce434a6ec556aa03b599d2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/GeolocatorPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98160766a89d5948698d333f46ee5bf21c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982e284a520788c41a1e6f713455e780fb", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98dfb7dc687aec8a586beed32484d45918", "path": "geolocator_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984810780cbe4e949fad921751f890bb39", "path": "geolocator_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986cce0108328f6b8860023978aa781407", "path": "geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987aefaa3807e07f181d9353260400ecba", "path": "geolocator_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986f86b6b31bc6d7ffaa57b3ff059464b0", "path": "geolocator_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b07e78648f4387a080c7312cac56f42", "path": "geolocator_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98066aa9c2c8bf29a5d5f350316abf628c", "path": "ResourceBundle-geolocator_apple_privacy-geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985b0b6e640cb5c896bc37362bde0a9432", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891ac0c51b1980c840b15d569c38c1162", "name": "geolocator_apple", "path": "../.symlinks/plugins/geolocator_apple/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c336903caa221479a233dd0dfc6dc9e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMCATransactionWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b4bc9e037f14a997f62d0c5ae657ab74", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMCATransactionWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b2b920d6e005de2eb4e89c3ace824ff", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMClusterManagersController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821e2c7b6e6309225d38920cce1f4e3ad", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMClusterManagersController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981289b04a7ee6a9091f9d9beba9d944eb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMGroundOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863be8b38fa9187433bd83ada522cba74", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMGroundOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6c1df7290d78c6890905ad2a265858a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMGroundOverlayController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbddd0c9a2587b001354e980b6496483", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMImageUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0466e5e4f70149f0dcb61409ae76303", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMImageUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98831d86f757d172d79f71eedfbd054220", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMMarkerUserData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984fd743b84bb8321bbac037518a518d4b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FGMMarkerUserData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d740a4082502af78afae0b1497796b1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FLTGoogleMapHeatmapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9823cc12d53ea145f7463ad3a5d4681ccb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FLTGoogleMapHeatmapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895f966cdb254f1177012944ffa34f8d1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FLTGoogleMapJSONConversions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ded6b730f7c21f6ac41617176a04c5ba", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FLTGoogleMapJSONConversions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98099e7c1d29ce83e97bb9e1bd87fa7c7b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FLTGoogleMapsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e27e2b3ecdf4e5fc28ce1a62934eb660", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FLTGoogleMapsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980028530c3c04e118a87beb89d51d6d1f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FLTGoogleMapTileOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98996644d266212c7f850d5d65457ff1b3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/FLTGoogleMapTileOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a71afd32423c2c18eb3031347272e761", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/google_maps_flutter_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9863c01e9c6f156cba1e36beb748948586", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapCircleController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d32e4ce5b388f9f6219bac272b7a2ebe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapCircleController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98568a6826979471caa2ab469b4f201ab7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f688b1df0591c782d0bb54e986586a7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c33dc9096ea2036625f2d42bd9321700", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bf77a70cff35d448e0233edbf3b4c97", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapMarkerController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98266e2445a753f3565d275d7b6615e755", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapMarkerController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a86dea0995c4718aece97ef0aba76795", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapMarkerController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98302fa52bac38047fc0ebf4c3eda9f874", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapPolygonController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd612d6e915ac03cdaa10e34ce66462d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapPolygonController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853f0a314a401808ca85cf13818f61d7e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapPolylineController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987439fc329440992440de5ab347402b7f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapPolylineController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd142fc7f97fb0d19fff31ba6db30824", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/GoogleMapPolylineController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98436fcc80569ab6f57918579c3fbcdefd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861b8920e2db371c0015fe21b247399a6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bdce5b34e76c7b9032352135495606ee", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9897e979f58ff36d79a6cd287d90279c67", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9840a46d55ffd317614029aed8161515b6", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980acbf75b60185d1ef69bcb7c202db073", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab0565c72c37b30cd8d280890a3edd5e", "name": "google_maps_flutter_ios", "path": "google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986faad930c3eb47fa9855c4e8033a4467", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867055814ed8726bbec05fb126c6b5679", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98541fad10693e03a8a937295049426a3b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a778ac2141963ec5d465f78c82f1a602", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6abbda590e436ef0d49377ca6ec4280", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800e184f5c847f7e30157bd17847cd29e", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98337794f5b4c3b3b32183eac213430192", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e09f41850202988e43659ccde1103ff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842478e24d758bf59203a94f803c657c9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebfbf50a8a147030696e964ead1f7d01", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc6349b18872c0a94096da4274554501", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98551c5fba97bd106c0045f0c0f9b7a202", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98682fba59b27a248620845cef626d422b", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c252f7ad5a3f8f6de8e03b4cdcc95e7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/Classes/google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c33b1001eb3c95a3ab40a832a28f2611", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/ios/google_maps_flutter_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9823c29025abf387aa15d1b1944d4485b2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d244d3a96041379b3f2968cfd2c8f779", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986b63224c84f38eefbe4ff8de0a050b4d", "path": "google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9856f5d59b06e6a9a0158bec808c83a081", "path": "google_maps_flutter_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980bbc2dcb55d192430974c2cbf86af138", "path": "google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848af601337b94d5b66e639613361332c", "path": "google_maps_flutter_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d394bc4927d3d7857746a6aedde20926", "path": "google_maps_flutter_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986680594b8dfde228f799c9ba96f14a44", "path": "google_maps_flutter_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b267c716a884f312bfbf3e5581b1b46b", "path": "ResourceBundle-google_maps_flutter_ios_privacy-google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98731eebdf2600efaa5cfe9f68c646d856", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4a16f7e57cc35d8aefdef637f708c03", "name": "google_maps_flutter_ios", "path": "../.symlinks/plugins/google_maps_flutter_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9822ed353f9c141ccb89f8ceed27214475", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e215aec6ca43ae1a52d4c3f2caeaa981", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e4f65217ec4f1eaa3e38d01f21a13c3", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98024abb67ddda299f652876f230dcf351", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d3d9e956c8e8d38c922ef71874dca75", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e59e30c5f526fe6e568d1a2e42ffbb94", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d43a79ec917ae6f9a1fa3480521dd172", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867cc92d8595ebcdac9e4a1bde7ca1083", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987bae80c396c0837918cb1aa76214bc7b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbbcbe7d82931b1d22d15d9b879a6b61", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edf060ed7388db0e589e29a2912f0810", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980043e611bde5a68491f05b1615de4c40", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2a9555d1924447fe09e4c9ae002aaa1", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98859ce91f611a90452ff56287e3f258bf", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986480f5050c4900aa347b6096291f4d35", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98655a6af149cbd1c00866ab1fb4f8f4ac", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dbfea8740e9c1ef14f20b9a08fb9fef3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed847660b8a88707567c85ffe302145", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a7d38ac64ccdce6c48b5922f30a205d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9829c5b7f99a5f5ac0c45c09e522320372", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bcb4bce01704824e50004d5e0f056f0c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850ada7d4da30a139f970a67f64a0f8e2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d45c32471a8a20c07380dfc3e24e474d", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842a2b64fedf5c648fcdfa552e5c55c4f", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c04a4259882b0fbfa5bcfa1686f5e8c", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981cc73a6d151745e57f41578a4d3d6a7e", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98041ae2390e4cf9c304cf05f9573084f6", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd9c76b2cb6a4863930902eaebe5c76b", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98606c9b6fdb3c13c71f654c4de652870e", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9818a579258ca67dfada5d47e585eb3297", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850e6d4463f3558cf7cb6eae8937af537", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828a2590b034f939ce4cc146c13653515", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe21bad38bcde3ea205e9a7614e3ea9e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881e191a7c709c471da64633b6efe8d8c", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dcff1225b9cd6e11a3e1644ac3b84578", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7ebb65553efee698b34c4f36ba2b7f3", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acac5259c6661e65f472f495fc729ee7", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbb15aec80c302cb847df52e0a4930cf", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98619722523c49ee6683f1d844713a0aa4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876b43dec53c538a783151332c3345904", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872b6ea0a7dc0971592790e9ad4dd7538", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986dce6441ee010ca2df495e8e1347c43b", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0c033e895eff0c959107253cc5ca31a", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4695e6cfb7f59a1144daa234d554ab3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987903ee8d129844099d7bef2246612d5c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc2e2451d670f2b4220863043024fd27", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891ecd42c055a69c48e0c1deb63337a46", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804ef28e2d878b901b1f367ef7e8649cb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ed3decae4a81cdcccba99cfa8bdb141", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5c0d8b4397b83de20e588c55be7c2c3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871a133c942ead8d3ab30a6e34cdce40c", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98cc98206c8e67b39b62dfcfa59e8ef0c0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c87abf5d2dad060447a856e060c48657", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9844f7f45dec2086e004217a70c4191cc8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c6f83ca2fabc022b74f796e08ef9a701", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b5ee79fd872cefc2a935b5bf6488b09d", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a7b245dcef3f4376502b49c44230adbd", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982fc727447c302e8aab5a68a4f988bbac", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98546f0390a1ee6fe3691f6f37f06f3bb7", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9828c7dc53aab870ba451a2ab29aa56d3b", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d7b3f5e7759b49b59d548da1f85c03b2", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9867657f645a1d57bd17a781fbfebb84ad", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98db415163a3a40305b22655df8c287e18", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98727e93b666323b79daddabe0fb490cb5", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98282ec06fa8d82bd20a3ac2d860020f02", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984a1cdb9fefb173e85426b1197956a779", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988460894140700027d0b151121df65d1f", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809ddf813cc2663a9d6241f2ef34e333c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852b235fa976c11dd634e67406a5b5f31", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a223d6d3a344e8d29909b259f64b9eab", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8b5b5afc12514b58eb2562f911bdad7", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985241648f0572f250157f1a908c2fe073", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f41b2f4e682c2399a7b56559ae31146", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df41a1d34b8ed3edd6044ab4baf90461", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a4ab88b00789f29fbc93159614d3843", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849a16e1f23c0da841fc1dfa9fb3873d5", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eef723d147b9a77644815b11a2dcddbf", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af93ed8a5f693e2b54f60f23b742f21e", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986dad5bd47cde2c7637d371c4936c6d4e", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c6f965d644c2f87b30dc810cade6da08", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c9525cf9446ba97b06869b44d9d72e4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c8431ca9392bdcf03191234abddca6ce", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869ace23fb55c55670b2c19c58bf8da55", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee3e4ef23902850a55ba121f1af5bc7a", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b7a6b93f4da594dc63d6f56535a3dd1", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f20b67c92084832827dfbbd535fe1ed", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2bafbc016117739d064b9a6e1b660ca", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e084ba95a2833c887588d4836c4d5e9a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a01e3bd581df41c5bafc84abb6af286", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dccadb650f8de66d099d57d634ebd94a", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981165e3c046df9a15a4f1691e9d50dd15", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f13f01f999c19c90d0d7bede132d73b", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4fef14913f6b8c2679505599a3100a2", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828992c7163649b90862f7331d818f840", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6e2c82b30e272e856623352f670bb00", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98680bcb0017cad7d897305ade234d717a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803b0343de9c3e4e2b9907df7ea0dc71e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846faaabee4edeec57beba9379cc7971b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b8a7de34c73e1e2e14985779d5220d6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7737b9efabd8309440ec3c48bc130ee", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984466c354aba03ef642c0d759b2023994", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e980812f79b925d6756015db4a43b7cfc27", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98449a02d4af2378ba16c384b241345b59", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9857c7e403231cdae52acd2841d2a73ff5", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98797521b71b1abcf982d54bf3789e4d06", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98beabd67e6a87d0a153dffd1c7db97313", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9894165193aaae623c2569290dc748b419", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d0e1c8f5c00f2859b3e706042110a20", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862ca20f442faa604a0b4fbb6b7ad34b8", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98153e73169bb692b37d12572b2930c7ec", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984c01c0129c8466ed9aa933ae3463fcf7", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d49a6c79ef1c821ff5555e473049ad83", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984fff2b369ca24546dfb18495ac1ee385", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dac2f89b5eb05c51f5a3f8d3ba988f7a", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98756c5fe62d8c266bcdc6a92f6faffacf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f256ec6524caa260d4fdc368ef67eec", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986c50332b76b6dbe58f8a80b3560fe0f2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2903aa45fd3a7f3e98577395ed76482", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982dabcc304ea1ebcc927053162311d79f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98beb8af698f8e99b3fbe06e8ff2bf164a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fec5b4e4d31fc7e8ec31d6170dec8ec1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b11ac24bbe9739bd8cfd44697e58880c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a512a94d373c7dbc7de0736169059f94", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a7166abe2647b501c189e23394ecc6c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98083ac4d51dc3cbc1b3d5dce2ac861692", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2d82483788fd3ae5527b013057fc446", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98371fac6af78c392f149a0cfa894c2ad2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc4ad3ddd3170fa0d2f917294369e0aa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef3c76979c7fb4fa9ad614d800476fe0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984170e9d8eecba91b2ed9267a959b1c21", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98469c6b7ffb5538fad342735ec8b554d8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9505697e71babbcb853e3c6884c4c1d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f120d6aabfd5f63f26cece7c9dcdec9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98139f97f1e2eaf3e2d3513f9dc9cd405b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98848b5925bbab84e2f4ce26f8a16a34dd", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c40acd3bdaf1336a8b29834c9709b8b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f408a1058c653718bbafdb6b2264b366", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889b25747b16697db50057efdfa0c5239", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98624b1837bb3d8bb01ec1e545a6a9df41", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980577c09d9d664469c9ca3bdcbd98e093", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd8617b469dc5dc5e68c363cf494c231", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee7513e741aab5a580e9363c7225aedf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a926bf3261b4d60b5bad2e859ffbccba", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf5ce238dd3c01f8d542c7318b232e46", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98671acd43951f13c80dc58180f8ceb9e3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985597d75428582f57b5c9dd83fc75f16f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac08b091af3a687666fe7f201b56d016", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c1bb590082a72babd2121ecce467e40", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984992f4727f35d20a83d2a9ad2fa84e31", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f6443141a9742bde26dc66d2130bb5a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aedb11fbe0d8a0831a4ba96a3c7360cc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98455139085decf7f40dd7c0563ce37fea", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f69bb56ad83c9b9313fb3b367c606d61", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9880dc1e0b40921cebf8776e684faa2606", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98265fbefdec7217535162261fcdd90043", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe5e82cb18bb1270e13c6001e5239a4d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985cb269a9601d4d96a40210608a390053", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aacc2f945c790b7f559d2876d878202e", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853e4c03b990fbcb79109f1963a3366ff", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9869798391fbf3ee80394387b6740866b1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98980dbe8f7af8a8b761282d62e08aac9d", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd8404a0effb73a087d639d7f094fc92", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a4119c36feedbd4bbf851d0410d26dd", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886012d81b363db3bbb3f548c06d83dc6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc4ab75f1aa870ce9e18ae28c601ffe3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98507049867de473823209b31d8015fc8a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c19ac367c1b09bce96210985e9adf0ce", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db9762b04204d5dace3ca324f117afad", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827ac4f667b112db6b7da7a8e4af922b3", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837813fdb5501f088b72b236a4684f4f2", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ecf329a61bebddc1bfe5c4da7b8420c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fce10ef0ae5c178a3a7b4096ff016198", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ccb1154749c78d893b9735400347cc7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98990c3b5efefd45bf2f0e2f8c970c35b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b075c00060ff48239b013ef9970347f7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98798e63bc8b73a31577f1ae5bf541bdea", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982f86348f8bae77fb0ea68ff346af805d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f7be265fc8b3782ba37e5476989b7f18", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982c027132ac3d02dda5ae86bfba08ec22", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a674fd464deac5241b7d00c0d2cbbd77", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980744e20a459c9fc4e61cc3eea80577f1", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cfe24dd64a4b3ffd74104aed91d0cd21", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98742d287d8efb187d43e676b44155e92f", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b165ce6eb79e49189c69478c86d9466", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98650b093d402c0acf4eb37ce918f21ceb", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d3c161fa41bde276ac4ec087b656689e", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c290d48fa9c078ae2c97393ef31a2b00", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d2d48ab9f68da9778460ebf1c81af29d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a85ad1e4abb9942f09898080bedcf997", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff4dfebeba3b39d559e7a4144e69b4e9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/ios/Classes/RazorpayDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867d6bce2eaab82059882c4b38c9d9848", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/ios/Classes/RazorpayFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e86f73119d1bb91e71bdb34954e33585", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/ios/Classes/RazorpayFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989da9eb7aab8863884e188b8dc8524140", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/ios/Classes/SwiftRazorpayFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986812b524ffd1c1f36237faf97d1ad8ca", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98470185f38a30d89c1da34966367e0fab", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d146409c0a8ddcdd7baf928be0b287d", "name": "razorpay_flutter", "path": "razorpay_flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883b6eceafc692ff1ae3abe397fc9adc3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819996ab95342e86114434283c5e2ed94", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807d468525965533f25c77ca7cb8f9896", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adbce4164ef09c97001a7f20bfd6d557", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864e0ca65f6cc86bf5274da2476b03e62", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98092aaac070bffbff8fc1fd3205ffae18", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d00439ce39c8204e079423a9f8866915", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d170529d6d220b2c07a50286578c9ca", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcc8e0d4ce645320a24c07bdb4706e62", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814fede7191beb888e57727abf815d543", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98616ec543e498d14f2769229535069f12", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98742ca3a67a80045b8fc4e974434dd192", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c157c10dce3a382b987da7a3d7467d2", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fd49e549790ca1055c24d6191ad660a5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980c4e8c595025c8d188ca2c5df96f974c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/ios/razorpay_flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98954b2635c655eb0ac10f93c7a94788d5", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986c5859e3feda41eab2d15f5522714523", "path": "razorpay_flutter.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982689bb46e15d0b274c47c951b05fdfda", "path": "razorpay_flutter-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98aa8f1ad01b6286fc9b40c01db3fa53f4", "path": "razorpay_flutter-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1cdd2dba74878cd68c9693eb04a845b", "path": "razorpay_flutter-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e02895534e08c8686002f299a2f4f8c", "path": "razorpay_flutter-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ec60dbaa9ec84bcdb1ee99238ed8cebd", "path": "razorpay_flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98289acfb9512e62b01860f0b0306dd49f", "path": "razorpay_flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986492fee7d4602ea2a1d240fabeb5c12d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/razorpay_flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839a81ce3d6d9dd57464460964c40f085", "name": "razorpay_flutter", "path": "../.symlinks/plugins/razorpay_flutter/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9803647c3ef159cb8ebc3da9f9f0052010", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98178589f37d75008179f306eb0c4b65f0", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb360d58feafdc91fb4eef5835a016b1", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac542b0213defdcfee44c9fbd3f328eb", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98464061dd3a1a1dc1f545a8529ae00741", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c8adebbe828b94b1eda147df2bf67bb", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985dde2f3746d388f921dbdc6b904c389e", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cde6e556ab5e5b8e2ff2e9ed71cac8a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98824dc19afe62be21862e5ce677c8959a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7371b520aedc94db67e47490b6259fb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa46dbc4a41ec20ee6855b7e0d5edf0b", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c75cb61d3df6af0e67c82baf73938fec", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989197aea7ca115dfaa3df3cf5ff63ab07", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6f8e5e8d2ff8405c756bd4a20ce7fcf", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d51b362866a3acb4fb58b25bdb4acf1", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d04b19ec8bcae3826b4c9b761d14e6df", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983d41ea6d175fa09be16adb4f3d5600bb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d56de88c4db2d9edcc117aea74fed5e8", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c519ea30d32c16feb8903943b7526bef", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee55c5109f780e4b72938905236e8ec7", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989fabf626887402a2e76e2dae38d850f9", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e200e04302ce20977ad50f7f29fc4242", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ee995f5cccecaac328dc0717e9c0005", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a368acd99aabf2d6bbf65f7a786c6852", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6b8e32572255e2ee11fb89dd7a60a21", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980dc36afcc1dbc64556417036daa657d0", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98104b8d49b4ec385c86d5a3ee22be480c", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d1c2522dbeaed671929feb1397ec870", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984934f4b2ecf5e2ea7e1eeefbe147003e", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98009d12304baafe4f8bc9300f8d00ad3c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887437a08d71a7250d47af425cc16dc5d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857aa71a1f5b3e9111005a3be9a0557c7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98895a4b4afa3dc9863a27b43dc4476037", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3bf08fa0c0908ff4a31bf9f1cdab29f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6352d92cac5ee557d3c5c22bd4399cf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f04d06ea7ae6ab57875dc242ab114d7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d52f9e1fefea92df8011b6d169d3623", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a1bcf51f38121bb7f8459db3d5a130a7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98436701ed92f9459119273f33682be6b8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988430f58ce323bf88a006a06083524ad0", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b427ffb2de295c6fc7015fe4eb84e1ee", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98473ad15fd9587415b1da096e61057136", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e94c5e7136f70a7786f4fc531eb789c", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ba4f28e172b1d4d794bb592d1ae50067", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eca9c2c8bf77beb7f9f1a3a0fe2f16a0", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bdd5d67fdea71c26587ba56eaf61bb3", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bb03f8693f2a71ea02ae14e4e0139624", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980d31843dc25b2d83a7f869e654004ee1", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c6699e2ec6797a01025f0e077d15d560", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4c5d659c9cd3016ac47c8db2820aa07", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f8e1c21dedb42e2a7114b498de17432", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/smart_auth-1.1.1/ios/Classes/SmartAuthPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3c08bf853b4ffa3b7491c0ec8ee0288", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/smart_auth-1.1.1/ios/Classes/SmartAuthPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9879906a02ebc2a4f4c3a04711c1de2729", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/smart_auth-1.1.1/ios/Classes/SwiftSmartAuthPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9896427bbc1dc34287ae6cf3f3cd69912c", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985949d278c9f132b717fedec9addfa72c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98296b44b547ecf9974bcee483b2d1c13b", "name": "smart_auth", "path": "smart_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824bc84b7de8b9d8f602729e1eb30c03f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983bd7bad0e1b41ba392500ca7ae60b4d3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f906b2b05a4242181e6183c7414315a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cc7c7b83f60df6e6171405d3f4ecc36", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ad7380cfdbff7bee00ae99d0ddb931a", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866310fe52311fbd18ffa0d5edd6c0003", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98810c68997beb2ef0fbc6f489625cddd9", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98008294854f3b10a1ae4bd51b4840ac82", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f160d536251574ca9b688c77ce91835", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98294fca96da93b04fb523a06646475e0d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809b2dc73a2070a49aa0bb45fa4edfe52", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98819352376c412f66ce6204fd78e44475", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870aacd36726f064f23b69c078166a95e", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/smart_auth-1.1.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9893e769af5c4778d603a343d8e7eafc48", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/smart_auth-1.1.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98326dc1e04d903860a981cff66c84d657", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/smart_auth-1.1.1/ios/smart_auth.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98306f3be7ae07ac35d8bbb916c5a9c892", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986940e7f52189c2c8a971257ff6678b94", "path": "smart_auth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2746b7268a647eab46457ac92de0c74", "path": "smart_auth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981d5e6286677c4c55aaeafa5aa8c74f19", "path": "smart_auth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c67b9d9fdcc8dba4fa88e7c7e6c34577", "path": "smart_auth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bbfcdb729c622b801c34ee0a8b81231a", "path": "smart_auth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c3a506edb949b52665a64036d8c1890a", "path": "smart_auth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9852cb8bb48143840376775864135ebf62", "path": "smart_auth.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98765fa1b01dc21dcc1144f105846a80da", "name": "Support Files", "path": "../../../../Pods/Target Support Files/smart_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865158bb165e239271fdc39a484e6ea69", "name": "smart_auth", "path": "../.symlinks/plugins/smart_auth/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98257568f4f8a475011475b759774aeb7e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9818a01755051b3dacd8a24a9d803ff393", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abd2ca2110d5d2deb359667bf0331c9e", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a6251e32bc5f7b012388997f6074129", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed5afe46c39b927ccb99830d57f6f7a7", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd5a0ddf93d6493c41df5594053adc38", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989100b8903ae988557877878124241920", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6f591cc80aeab0490097597b2ce79ad", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f761f0ea901e3d1bc377c542eb0b296", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988441979506973da15f7a112f0901bb67", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afc91b515af5626500f3c80d3cc5ef57", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980abb7d5f5a80bcf46b4f0d2bbd7235e6", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98734f4c004f2afac8fc3b4bf1909dfdda", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dc2f86cc894671fc74cea749bb6a305", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cccb583c07e307eb7e32dcdb6b858e98", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9ed188c8bf9f568207459ee46afa978", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bce86583e7f9d74980e0a0e9b9d02248", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9810c9aff9ab36d2ca741bcade4619d0d2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980511e6dad03cd62715637f2c5c51f56b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988dc7a3f7832893e0c606db7ff33f1d03", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831f223d7a703366517613cd59783b38c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98900acd191d50b55bb840d864106f782b", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986aa52d55a5f5b2c99ebb9a53ac9aa667", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848e51edc7c7b74eec499d935adb0064c", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98760ce81e8a306d5d11e53d09fc492a7f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fffda28bb15d872222cc671836a01129", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b67c9ece6cf4de9513590326936c36e7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98184f8f78904b4c6aeef9d6b7f207373d", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982aa23309c54f16c866be7543eeb8718f", "name": "Bandiwala", "path": "Bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a134cdf05e29c82d98ba3f8a17fbfe3b", "name": "bandiwala", "path": "bandiwala", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2a41fea211a2627fa1d885d63ea0a78", "name": "intern", "path": "intern", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838d2b6c1e17d39e93d338cd287693567", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980114b70b1d4694692a1c34c4fa0c4af8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870d580a9d392defcfa0f8fda3b2b3f4d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e0fc414b664012792cf47f934dbbcb6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e427e9998e394ace967917cf0c92b52", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1f3a3a1051b714bc3799a085371a7f4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982648b1a5fa5799727ebff634424eee74", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b21714f6686eb1861bed95e83eb9bf10", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98984081f3e0c339c99138963195722027", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d9e0994b780d54e57c556cb9f40fa9bf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982cb9025af07c798a0722bb302bffdc47", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9885d25bb527c5d9785274cd830e2fb6d5", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d47da829615b08dd5c618150132f8185", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bdd8d088cb4b8a0b2c0de755a1d587d2", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981051e9b69a75960d4b8c43ec7352fafc", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ea7951c5a285255f7f89830aceb469a", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988cd4abaf1e3bb9ebc0ae2dee22fc2a27", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e70c7451033c07c9c087b809d7c9454d", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989ba06b28c6cc2d336fd7c426cd641590", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fb8becb13a86b5fe40886af34af52ecb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ca7143c291c235c20a64ae5de39ad0e", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98191a7186daedfae5c018a511191cef6d", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98942867ef9a9b82a20a4a7fc87d922201", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862d9c32904d63fdaba0f2aec66ff1562", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98244235236a15f59a3e1a18de184eaad1", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/CartesianPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a00293d80380e4049bea05ac6a4580ee", "path": "Sources/GoogleMapsUtils/GeometryUtils/CLLocationCoordinate2D+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98786fe59b0b2b56ae03dd450eebd32a2f", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fd3af3da697148ec185952c36e25232b", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eee2a2c8117793ea6b70fdb68dc09fc0", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPath+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9862898f4579a8eb2a70e88a550c66f583", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolygon+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b3383db05604d5a54f26827b25edd58c", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolyline+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be624edcd09863f9b2fd31f3e4814df3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ecde26bf1543eeeb6c860164108fd52b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864e3f499709649f61122c99b3a5b4bff", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c2dc7cc9dbff5ac57327083f659fb65", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98357b5ba75e42bb56d238bc7698cb40a7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a25b5742ffba058cd26d77d24733ea6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98115b6c3284d138f28b261c8383cba1a7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836a9c0cf281a75b4188f7a5c82fb5f21", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983dd49c3ae2a78b8e1d8211d521db31bb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98963941879385d8ed099edece76e0715d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893b09633f5075719f9f437a96b612d07", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980aa35645eba5223242a5e0fa640766c3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9847b3901a6520ea08fc8030d66e0ad94a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885989175d40690e8d9350ff30b6ef9db", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98699ecaa75c332c88b3f85d2ff3ce43ab", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a8e30444ad42344ef47eb6e323fb8f6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98193965582997b84ac14d5ed20de86dd2", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dff4d685e7e73d56dfda4102673f8f7d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981fe03eb81afabb7549f4238ced214365", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c631bc36dfaadd5ac3efd3c60db0183a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f33bf65e29bc1c89409743c95fa809b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c44c981d6b6743ed6ad66141054cfdaf", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818e6f8bb9ff5b1abd04392c79b1be516", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9873dad3ce7e03160f1a13569a82de459e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e07cb0d6596ee63bd0a6b06a9f6d85fc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98416823b081273d1ae213b8f71ad85c25", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98327a524a74cfa3265de202a94af2781c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b4eaaeae7a0218d5688755f6f3e084b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98acc113323d720217229cbde5b5fd36fc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cabd4a8b0ffc708b70fa111d55997857", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818df203c3378cc1855fb45a81ca64c82", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbdda91f0f50185e33ca54dad7e7ff0a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843298a134c3521c36ee37fe05808576c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c175aff9d459472c89ebeaa12af9cad", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a27845453aaba629b2e531bd64e447fa", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9852bfac22518c12529b419d5643576d6c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a09da2d40ee2814ffdd7317aa6889c17", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2a9c9e76780bfaff9f60fae707d28fd", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f78b5bc28bd24f5e2f44d29ee04741a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUMarkerClustering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ac67ffc384edbaa0945c9a92ae92af5", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863fda56cde23b6ba8259897bba6feee5", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f2f5db6eaf56bce042f860a8124bd07", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827a5a64405a3f2d0ab005dd5733782fe", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a0d0033095216eddaacdfebd4bb4e6a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827280615c358112135f9b3077a488eb5", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb20c9efe44f2553bf935e9f65428141", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809a078ea8ed21aa3268b36f5a8cf3bcd", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857a6693cd48ba63cbbb560ed715d3b2d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98587d48632fee13cc608bb214031c9f31", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7625ce81945892b8504b69259c0eca0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e38c4bf1dc0d3d5245afee0604a29afb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984335134958ba12b015171165802ddf04", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b76297f4c47c555e8fd74f8ea93c3f90", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2cd37c71dbbec5bffa709804cfa2deb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6ee7f917c3b4614758eb051d09a7087", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f96715a14cd7f6e6e3cc85072f4abe0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fc38c058f7db7642d527430d91abbfc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98acdfe9e67af9996a79e45ee396dbe53f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984d84fc08d16a5f3153bdb74f11c26bfc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ead133c804a6050a45820c820714d73", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9802a83012923fffc3bf41218c2d48bef2", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c4e7258f970f249ecabbe58e911c1ad8", "path": "Sources/GoogleMapsUtilsObjC/include/GoogleMapsUtils-Bridging-Header.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3182d95760ed831dd1a5907b5a829ee", "path": "Sources/GoogleMapsUtilsObjC/include/GQTBounds.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858d33c7bb882d9946a384510439242ea", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989abe88b4fa303af7a1f2bc6eae9ae7c6", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854a04d7930440e4712cb5eb1f940d9d3", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d1876c48e4810b12c207e7a4e4f7b10", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b667f1095cf6b670a037954488d5115d", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c561eabf8dabf6d10bf0c638d433b933", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f2af397c8a74a43c50b3fb15642da080", "path": "Sources/GoogleMapsUtils/Heatmap/HeatmapInterpolationPoints.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f17d7db5458f9add5cd7a86b2f88e58e", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/LatLngRadians.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9897f7be425376147b36002bcfb16b12de", "path": "Sources/GoogleMapsUtils/GeometryUtils/MapPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9807f22ef3711f0cb1bc0847a2aec74cd6", "path": "Sources/GoogleMapsUtils/GeometryUtils/Math.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857ffcf10f5d6d21c4f100350903e74c6", "path": "Sources/GoogleMapsUtils/Helper/MockMapView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d220bf5e77e3050dc02b9a75cc9043a1", "path": "Google-Maps-iOS-Utils.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef579b792fb215a7c5fa063bb65545bd", "path": "Google-Maps-iOS-Utils-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f925fd9143a726e28a5c971a31c87603", "path": "Google-Maps-iOS-Utils-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c867a4607f32962951831ac2700c7266", "path": "Google-Maps-iOS-Utils-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98979e77de8bcc9bec6d1118ebcf2f7b9c", "path": "Google-Maps-iOS-Utils-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e92b11dcdd3ac1bd1469104f52852b46", "path": "Google-Maps-iOS-Utils.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983830c98e36c0e7f61060f59ae7ffe02f", "path": "Google-Maps-iOS-Utils.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fb5d3d2b8d69be58436a89ee9944cfca", "name": "Support Files", "path": "../Target Support Files/Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cc19fc2c5e4d856935af39e9d1ac85d", "name": "Google-Maps-iOS-Utils", "path": "Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984feaa9e9cfffb196c770f70f684abe69", "path": "Base/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98277f3a857904fee21d83437c425d3c39", "path": "Base/Frameworks/GoogleMapsBase.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c6f1159aa0e3f71540e11b0a0d3fb5e3", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98605dc3711d2fb1e989c059f15c4676de", "name": "Base", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829c8b87f29a12b822cc525121095d003", "path": "Maps/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98fa258b00baeee850f1afe0ab71bea85c", "path": "Maps/Frameworks/GoogleMaps.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98567f6347d984f41cf741e736b1a6e04f", "path": "Maps/Frameworks/GoogleMapsCore.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9889f7c79f522832e9e6098d517b233bcf", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.plug-in", "guid": "bfdfe7dc352907fc980b868725387e9842f88c62b02970337349444ff92e2612", "path": "Maps/Resources/GoogleMapsResources/GoogleMaps.bundle", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988b2b29e9c1be8017fd9a97269de08164", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c147fe3baca599aa160f54370a388f9b", "name": "Maps", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98411f75b74d30ffa53c6e9d878d9dc7f9", "path": "GoogleMaps-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986467395957fcd8f16878f687660d9193", "path": "GoogleMaps.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d64a564f34583512c2b54504bf0984b8", "path": "GoogleMaps.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e80cea15de4b8693fc0a140851f1b1b9", "path": "ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987d33b59a2a120b47d26750d0cd2f79af", "name": "Support Files", "path": "../Target Support Files/GoogleMaps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c42ee1badfbaaaacdd3cf6e2d7fc1a1", "name": "GoogleMaps", "path": "GoogleMaps", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9843c5ed1a09760a3a3ebc48df1ed3be46", "path": "Pod/Razorpay.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f2dc1c9c5aec9c6a7ca655effa990e9f", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9872f92b30c9feb94f7da02a0093c5c8cc", "path": "razorpay-pod-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982a4468b0467c6d7eed51d7ddbb361d7a", "path": "razorpay-pod.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b383468be164716895a66f5cfcb674e2", "path": "razorpay-pod.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9860f98d80ef97943861c5b956fc049069", "name": "Support Files", "path": "../Target Support Files/razorpay-pod", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820717cdab0b1e6dbd7b6bdbb6352a515", "name": "razorpay-pod", "path": "razorpay-pod", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7cce27c6542f0b94ac14da87f53b39a", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e981ef159a029b89f14a31297aadc62bd8a", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/ios/Pods", "targets": ["TARGET@v11_hash=804d2a64a3c9cab5218490f110788c10", "TARGET@v11_hash=04420951a8b8658f2cd38fffbb618844", "TARGET@v11_hash=d32cdfb0919b10767f57ff6f72b9169b", "TARGET@v11_hash=30c96feae77d58b64bb83e2c05714ac0", "TARGET@v11_hash=60e0ce4b1c895e706d3e7f797864d4ef", "TARGET@v11_hash=b48a10f06a73e2e04b616b88f9764653", "TARGET@v11_hash=0c765a1ad519228958972c2959b41f30", "TARGET@v11_hash=d7763590cf5da9114024e987e31cff59", "TARGET@v11_hash=42f72ec83da4ed608235d2343a39ade2", "TARGET@v11_hash=bfdbd45e5bcc3179313aa1e0f4e39708", "TARGET@v11_hash=bebc0c0b3ddfa091df8bd561d1d3935f", "TARGET@v11_hash=cffea69e9e2e6433f10673ad75b4c3eb", "TARGET@v11_hash=1b629a9f0e75d6afd7f91a44a83ddef1", "TARGET@v11_hash=8f9a3475724545abbc3f80f7bee961ba", "TARGET@v11_hash=b63f78b79ef7ab93250c02abe338ab1e", "TARGET@v11_hash=035b619d19bfe422187da6ab2484d067", "TARGET@v11_hash=3c3f0a75c382b0de7c44193a78efa238", "TARGET@v11_hash=8966193a6f9f72cc31c5690f4c7217ea", "TARGET@v11_hash=43e65b5fdd573c6622d3ede272f852c8", "TARGET@v11_hash=19fb9a7894478af27dee21c177daa038", "TARGET@v11_hash=b33085ee9be443d8510bf3b5574f78bf", "TARGET@v11_hash=04007138481442c8eee5a59dd675726d", "TARGET@v11_hash=1da920153807aa77021df758545b2eca", "TARGET@v11_hash=a593561fef61e546f5cc8b4c53f62bea", "TARGET@v11_hash=6d794f0bba54b0819d6594dfaed4cef5", "TARGET@v11_hash=dca185de28824a77dd6da745518c84b3", "TARGET@v11_hash=2e64c692a46af02f18e2919b67281a2d", "TARGET@v11_hash=ba6d64e896a681b03bb5cdaac4d4f861", "TARGET@v11_hash=9f538b7980b56aea10bd9b17e436ed48", "TARGET@v11_hash=bd0806d24d3009d5e5c5a303252b5627"]}