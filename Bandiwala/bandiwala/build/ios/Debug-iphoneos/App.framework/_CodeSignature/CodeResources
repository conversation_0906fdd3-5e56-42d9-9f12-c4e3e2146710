<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>flutter_assets/AssetManifest.bin</key>
		<data>
		20ssJUICcWqUvdA38co1ywUs+3M=
		</data>
		<key>flutter_assets/AssetManifest.json</key>
		<data>
		zFab2umkYe2YoDxd2eJz2emcMFI=
		</data>
		<key>flutter_assets/FontManifest.json</key>
		<data>
		+D1xbIOooc3ypce1+jh+mmLy1J0=
		</data>
		<key>flutter_assets/NOTICES.Z</key>
		<data>
		A/Serqf9uzJDYgzPBKizW33PZC0=
		</data>
		<key>flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>flutter_assets/assets/HyderabadiBiryani.jpg</key>
		<data>
		aDa0h5UUTIhAPmCkfOVYuIspqI0=
		</data>
		<key>flutter_assets/assets/Idli.jpg</key>
		<data>
		Pdh/6tCID4/E5WjHeJTaN+UU2H4=
		</data>
		<key>flutter_assets/assets/Pani.jpeg</key>
		<data>
		j2pvr+dSlHKrinCk2B083AaNdXw=
		</data>
		<key>flutter_assets/assets/Samosa.jpg</key>
		<data>
		VQxDGhzVIUFPbXG3M0FkHxjhgso=
		</data>
		<key>flutter_assets/assets/bandiwala_logo.jpeg</key>
		<data>
		7qoypRPkbGL4mc0ZXaPM2XdvHWc=
		</data>
		<key>flutter_assets/assets/chicken_burger.jpg</key>
		<data>
		9wzDZY/A3nXlj5rTeWObgmEUok0=
		</data>
		<key>flutter_assets/assets/chicken_manchurian.jpeg</key>
		<data>
		J1bQKTR7qk7a2Wrrsw45/Ccr7G4=
		</data>
		<key>flutter_assets/assets/french_fries.webp</key>
		<data>
		osFQymIBB1xAg9YjDnDSZIUEceE=
		</data>
		<key>flutter_assets/assets/google_logo.png</key>
		<data>
		bxxoUJw6xTArlbB7c0RZIJ7GFgE=
		</data>
		<key>flutter_assets/assets/gulab_jamun.jpg</key>
		<data>
		ueqQv41ZBH3jJfKabUe+ZA2BbBI=
		</data>
		<key>flutter_assets/assets/idli.webp</key>
		<data>
		hPJF99EpBsTn6myHZwg9Q3Kds3s=
		</data>
		<key>flutter_assets/assets/masala_chai.jpg</key>
		<data>
		xxcGjEM+LBJLse/G8K9Umo4PPZA=
		</data>
		<key>flutter_assets/assets/masala_dosa.jpeg</key>
		<data>
		TiJ39BYUefOK6UOUOwdrI1R2Xfc=
		</data>
		<key>flutter_assets/assets/masala_dosa.jpg</key>
		<data>
		OL/sD42J5ZU7UI50BlpJFmDJ9/Q=
		</data>
		<key>flutter_assets/assets/non_veg_icon.png</key>
		<data>
		1YJS+U7aU6KPeEtZlPjMSoR/7Ts=
		</data>
		<key>flutter_assets/assets/paniPuri.jpg</key>
		<data>
		y0k5l11mSTvzwUuCKzUpKPzGXXA=
		</data>
		<key>flutter_assets/assets/pavbhaji.jpg</key>
		<data>
		rC3dghEDS9MkFgiu2Hk9MfjXBrs=
		</data>
		<key>flutter_assets/assets/uttapam.jpg</key>
		<data>
		bxxuFFz1kClCuk+A1Ewti4fGA/E=
		</data>
		<key>flutter_assets/assets/veg_fried_rice.jpg</key>
		<data>
		frt4/Lp+k+rSDHKLOm6KQfwhxXg=
		</data>
		<key>flutter_assets/assets/veg_icon.png</key>
		<data>
		WrOakWx8Te3fylxZJchB5yPEaQ4=
		</data>
		<key>flutter_assets/assets/welcome_image.jpeg</key>
		<data>
		22WZEhGOR8Blw00d7T5nUIbHdDQ=
		</data>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>flutter_assets/isolate_snapshot_data</key>
		<data>
		w9WcwIYVRl07EMLuLi7qukruhIs=
		</data>
		<key>flutter_assets/kernel_blob.bin</key>
		<data>
		I/v5KAI452DNZDU1Mb+2/YZpKaw=
		</data>
		<key>flutter_assets/packages/fluttertoast/assets/toastify.css</key>
		<data>
		HVkEhoid+n6bwOYCwZg4DdmGBHY=
		</data>
		<key>flutter_assets/packages/fluttertoast/assets/toastify.js</key>
		<data>
		v5LMft1C/Ps/+tw6D+zco3p3Urk=
		</data>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
		<key>flutter_assets/vm_snapshot_data</key>
		<data>
		SoHnDHqzNKozN7o/zhsvOoXVErY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			XhkARm67kx0Za5UTSpy8CqPtlZSXxrc271Aor6j3dog=
			</data>
		</dict>
		<key>flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hvXTwY8U8BnGOdiJ9h2zphrz7I4sRCTFr9pMHaD0a5U=
			</data>
		</dict>
		<key>flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KLHrKz0uGtYLjIsPkQCxzL9JL3+pf1vrtR6pfnOSbn0=
			</data>
		</dict>
		<key>flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			PBU9JvrJhW1R+eJuNwtQ5emGyNLKMmVHsrwZA/7E+I8=
			</data>
		</dict>
		<key>flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>flutter_assets/assets/HyderabadiBiryani.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			u8KVFhmiQAuUmrMyvX0XF6Dz1rgsNaiOZcz4jglV0S4=
			</data>
		</dict>
		<key>flutter_assets/assets/Idli.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			UBCWv0KgrDlF+nU91YyaQsmv/K0PONsPOB/9vHJxf3E=
			</data>
		</dict>
		<key>flutter_assets/assets/Pani.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			V0qyZ6ER5WCKR8C1C1jRxEFcmRE8ogsof1iyTBKWCWs=
			</data>
		</dict>
		<key>flutter_assets/assets/Samosa.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			tgMuz0di7teIIohWRCEgRzcOYLlXZ5d34iRN72fOYCY=
			</data>
		</dict>
		<key>flutter_assets/assets/bandiwala_logo.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			SdeRgaOT1A2aKMW6k9TJcqJd8WRCkqJksINLWKVrrPw=
			</data>
		</dict>
		<key>flutter_assets/assets/chicken_burger.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			IUHLdCJ04eOLQYc9B2/mp7TkuBPzenChhwcEbG8Mi64=
			</data>
		</dict>
		<key>flutter_assets/assets/chicken_manchurian.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			oSA4wvSrdI0S67mb4OI1lh/1SxdGgxC23CUWwrEJwTo=
			</data>
		</dict>
		<key>flutter_assets/assets/french_fries.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			UniiwYT3gpxztroF03TYW4BXhPOFdFvpdG17lnx7sPk=
			</data>
		</dict>
		<key>flutter_assets/assets/google_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KYKk5rTBI4NdrbQ2fnxEKwmQCAxZrpEHJvNVFiJBUJw=
			</data>
		</dict>
		<key>flutter_assets/assets/gulab_jamun.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			oconroH6jzVK5VSm0CXRnxSLNLJpg48IwX3NO5Xrcg4=
			</data>
		</dict>
		<key>flutter_assets/assets/idli.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			R9BWm/tiugrkPMzgLa3o0PtfFderdqg1cWg+1aXl4Us=
			</data>
		</dict>
		<key>flutter_assets/assets/masala_chai.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			CiEJk1bZSJhSo/iB6dcNOjItHd/NTUa8KxgIDs5zZYo=
			</data>
		</dict>
		<key>flutter_assets/assets/masala_dosa.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			Op+hM+Gi9TftG3MhRTNQgte/O2LlCkeXfDxltm0H6ng=
			</data>
		</dict>
		<key>flutter_assets/assets/masala_dosa.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			NMkLUoL85FNIySQadb7ZTe/kouRRhR4gYsL99ZjfEtg=
			</data>
		</dict>
		<key>flutter_assets/assets/non_veg_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+KYnzZCXDY50n7MGEJnKYzV9b/jLdK4bJ3pkJKORr4E=
			</data>
		</dict>
		<key>flutter_assets/assets/paniPuri.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			GmR+j+0EUT5z5CS5TgiKMOSZjWsWIC7cgS9qwW8IZAU=
			</data>
		</dict>
		<key>flutter_assets/assets/pavbhaji.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			k/RpeVlJvZz2GJBpL4uVt6hREVGPHzRRPiBNJGobFXs=
			</data>
		</dict>
		<key>flutter_assets/assets/uttapam.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			lOlLw0ljkk5aS4on+DTiwPkOXi1uoIY1CaiKVw8uRb8=
			</data>
		</dict>
		<key>flutter_assets/assets/veg_fried_rice.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			4Ry7VtPbi17Z802aFVBa1Q1JEXY5CIQ0oRw0LcufFPA=
			</data>
		</dict>
		<key>flutter_assets/assets/veg_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2gqDntuDPD+HHjLAp4AsqM9IwuGHOyWduoFfeTh3sKM=
			</data>
		</dict>
		<key>flutter_assets/assets/welcome_image.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			xGjbOLR4kVLq5NEIiTttcFiJNx63MRf8YhRp5+OjdLM=
			</data>
		</dict>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			PBOAqqWi5ujK3/k/uWcKrZyOam5QktQ5MqxZKes8MBY=
			</data>
		</dict>
		<key>flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			7pH1n16tHnEmNKpqwp8p9TY1sjvIpEd+bxlmV03HuoQ=
			</data>
		</dict>
		<key>flutter_assets/packages/fluttertoast/assets/toastify.css</key>
		<dict>
			<key>hash2</key>
			<data>
			abr0T+pbPv0zd7q8FU7xuDv4JjuRAgKox9bnRddRNJ8=
			</data>
		</dict>
		<key>flutter_assets/packages/fluttertoast/assets/toastify.js</key>
		<dict>
			<key>hash2</key>
			<data>
			CRyacJlKIATuArBPER1Aq+QMB7BmhWOZ9YxYcELsKZA=
			</data>
		</dict>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
		<key>flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			RTqty/gYjcrXxgR3QLjNukvL8gGpfV6MdaYktQJaIa8=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
