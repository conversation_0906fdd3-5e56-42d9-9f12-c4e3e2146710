/* Copyright (c) 2018 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Instances of this class represent a geometry Style. It is used to define the
 * stylings of any number of GMUGeometry objects.
 */
@interface GMUPair : NSObject

@property(nonatomic, readonly) NSString *key;
@property(nonatomic, readonly) NSString *styleUrl;

- (instancetype)initWithKey:(NSString *)styleID
                   styleUrl:(NSString *)strokeColor;

@end

NS_ASSUME_NONNULL_END

