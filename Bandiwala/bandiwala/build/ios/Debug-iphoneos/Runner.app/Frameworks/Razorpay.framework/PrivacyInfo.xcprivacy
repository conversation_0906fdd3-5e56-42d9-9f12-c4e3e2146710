<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<!--
   PrivacyInfo.xcprivacy
   RazorpayIOS

   Created by <PERSON><PERSON><PERSON> on 18/04/24.
   Copyright (c) 2024 Razorpay. All rights reserved.
-->
<plist version="1.0">
    <dict>
        <key>NSPrivacyAccessedAPITypes</key>
        <array>
            <dict>
                <key>NSPrivacyAccessedAPIType</key>
                <string>NSPrivacyAccessedAPICategoryUserDefaults</string>
                <key>NSPrivacyAccessedAPITypeReasons</key>
                <array>
                    <string>CA92.1</string>
                </array>
            </dict>
        </array>
        <key>NSPrivacyCollectedDataTypes</key>
        <array>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>NSPrivacyCollectedDataTypePhysicalAddress</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <true/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                    <string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
                </array>
            </dict>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>NSPrivacyCollectedDataTypeOtherDiagnosticData</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <false/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                </array>
            </dict>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>NSPrivacyCollectedDataTypeCrashData</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <true/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                </array>
            </dict>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>NSPrivacyCollectedDataTypeProductInteraction</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <true/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeProductPersonalization</string>
                    <string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                </array>
            </dict>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>NSPrivacyCollectedDataTypeDeviceID</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <true/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                    <string>NSPrivacyCollectedDataTypePurposeProductPersonalization</string>
                    <string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
                </array>
            </dict>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>NSPrivacyCollectedDataTypePaymentInfo</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <true/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                    <string>NSPrivacyCollectedDataTypePurposeProductPersonalization</string>
                </array>
            </dict>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>NSPrivacyCollectedDataTypePhoneNumber</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <true/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                    <string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
                    <string>NSPrivacyCollectedDataTypePurposeProductPersonalization</string>
                </array>
            </dict>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>NSPrivacyCollectedDataTypeName</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <true/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeProductPersonalization</string>
                    <string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                </array>
            </dict>
            <dict>
                <key>NSPrivacyCollectedDataType</key>
                <string>Email address</string>
                <key>NSPrivacyCollectedDataTypeLinked</key>
                <true/>
                <key>NSPrivacyCollectedDataTypeTracking</key>
                <false/>
                <key>NSPrivacyCollectedDataTypePurposes</key>
                <array>
                    <string>NSPrivacyCollectedDataTypePurposeProductPersonalization</string>
                    <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
                    <string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
                </array>
            </dict>
        </array>
        <key>NSPrivacyTracking</key>
        <false/>
    </dict>
</plist>
