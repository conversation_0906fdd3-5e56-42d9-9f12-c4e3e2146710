<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		mnLbgBhrpRwdlXh4UKzYj73lYuA=
		</data>
		<key><EMAIL></key>
		<data>
		W0gmQxmINA6vbi5M4Y5KotdMnVA=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		k2fBD/jokRFQCCj8xCDSOt3s78k=
		</data>
		<key>Assets.car</key>
		<data>
		uTcn5XlCPd3Fi0NTOUcyuFxGZ7Y=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		28xWMBQ91UzszfdXY91SqhC7ecg=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		ocgpzg3jAbZlN1XA2hv7ik5EZGo=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		1C8cvc8xXErLBicmci5PXO4ny/E=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		20ssJUICcWqUvdA38co1ywUs+3M=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		zFab2umkYe2YoDxd2eJz2emcMFI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		+D1xbIOooc3ypce1+jh+mmLy1J0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		A/Serqf9uzJDYgzPBKizW33PZC0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/HyderabadiBiryani.jpg</key>
		<data>
		aDa0h5UUTIhAPmCkfOVYuIspqI0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/Idli.jpg</key>
		<data>
		Pdh/6tCID4/E5WjHeJTaN+UU2H4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/Pani.jpeg</key>
		<data>
		j2pvr+dSlHKrinCk2B083AaNdXw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/Samosa.jpg</key>
		<data>
		VQxDGhzVIUFPbXG3M0FkHxjhgso=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/bandiwala_logo.jpeg</key>
		<data>
		7qoypRPkbGL4mc0ZXaPM2XdvHWc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/chicken_burger.jpg</key>
		<data>
		9wzDZY/A3nXlj5rTeWObgmEUok0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/chicken_manchurian.jpeg</key>
		<data>
		J1bQKTR7qk7a2Wrrsw45/Ccr7G4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/french_fries.webp</key>
		<data>
		osFQymIBB1xAg9YjDnDSZIUEceE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/google_logo.png</key>
		<data>
		bxxoUJw6xTArlbB7c0RZIJ7GFgE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/gulab_jamun.jpg</key>
		<data>
		ueqQv41ZBH3jJfKabUe+ZA2BbBI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/idli.webp</key>
		<data>
		hPJF99EpBsTn6myHZwg9Q3Kds3s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_chai.jpg</key>
		<data>
		xxcGjEM+LBJLse/G8K9Umo4PPZA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_dosa.jpeg</key>
		<data>
		TiJ39BYUefOK6UOUOwdrI1R2Xfc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_dosa.jpg</key>
		<data>
		OL/sD42J5ZU7UI50BlpJFmDJ9/Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/non_veg_icon.png</key>
		<data>
		1YJS+U7aU6KPeEtZlPjMSoR/7Ts=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/paniPuri.jpg</key>
		<data>
		y0k5l11mSTvzwUuCKzUpKPzGXXA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/pavbhaji.jpg</key>
		<data>
		rC3dghEDS9MkFgiu2Hk9MfjXBrs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/uttapam.jpg</key>
		<data>
		bxxuFFz1kClCuk+A1Ewti4fGA/E=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/veg_fried_rice.jpg</key>
		<data>
		frt4/Lp+k+rSDHKLOm6KQfwhxXg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/veg_icon.png</key>
		<data>
		WrOakWx8Te3fylxZJchB5yPEaQ4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/welcome_image.jpeg</key>
		<data>
		22WZEhGOR8Blw00d7T5nUIbHdDQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		w9WcwIYVRl07EMLuLi7qukruhIs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		dxLvhkpjrwUOxMbLUauPx5Rt/mA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.css</key>
		<data>
		HVkEhoid+n6bwOYCwZg4DdmGBHY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.js</key>
		<data>
		v5LMft1C/Ps/+tw6D+zco3p3Urk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		SoHnDHqzNKozN7o/zhsvOoXVErY=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		U89LBdK3KS9mL0WWBKPsV8lrQss=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		FSKj8+jea1tJz5Wmal2szkIiAGU=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		vBonAqoVJGE3y6WXKxWnl3w/QBg=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/Razorpay.framework/Assets.car</key>
		<data>
		Bju3rMGH/QCKRwqZN0W5qAnnnS4=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/Info.plist</key>
		<data>
		DFcy10+V49NKtjJ4RLpByN10P1k=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<data>
		c8N/593lOrAw9wwSo2JSseKACUI=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<data>
		GAbF07uihY0DE3w15+txJ0RlSNw=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<data>
		Ffh5EuOShB1cS/eITOW9HbEzxi4=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<data>
		6QJ3pwC0+qM8m2WjXuCS4IdsbG0=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<data>
		GuaLaOgcxzpvPP6jj7CE6UW1x54=
		</data>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<data>
		Ucg3HkU3DNFIfnKf6gQlzd/zqTg=
		</data>
		<key>Frameworks/Razorpay.framework/CommonAssets/Razorpay_Logo.png</key>
		<data>
		C/QPifs1kjcxzxgwUgDFDlLjpRw=
		</data>
		<key>Frameworks/Razorpay.framework/CommonAssets/check_mark.png</key>
		<data>
		6d4pPz33KoUobYRDPpGmnPiTVMs=
		</data>
		<key>Frameworks/Razorpay.framework/CommonAssets/warning.png</key>
		<data>
		gxArEMTCcu4a+ueYNB3oMoIh48o=
		</data>
		<key>Frameworks/Razorpay.framework/EncryptedOtpelf.js</key>
		<data>
		A893KbMpygzZy6/G1xrQkAudMxw=
		</data>
		<key>Frameworks/Razorpay.framework/Hash.txt</key>
		<data>
		gN8QKnsfFYPlPa6NstmuiJETxJ8=
		</data>
		<key>Frameworks/Razorpay.framework/Info.plist</key>
		<data>
		yTtAviMT5W1lEvAmyZ50ZTxnFXI=
		</data>
		<key>Frameworks/Razorpay.framework/PrivacyInfo.xcprivacy</key>
		<data>
		62HpNLqPh8tKsg+iNP/pSbF2S6M=
		</data>
		<key>Frameworks/Razorpay.framework/Razorpay</key>
		<data>
		SPCrfrN2z3cSZgDnmiQtqE7NAuU=
		</data>
		<key>Frameworks/Razorpay.framework/_CodeSignature/CodeResources</key>
		<data>
		Cw6nEYvZm4npeahjTfET03Ny2+Y=
		</data>
		<key>Frameworks/flutter_email_sender.framework/Info.plist</key>
		<data>
		lsf0QQpVqrjpA6OMXHm/RidZ2P4=
		</data>
		<key>Frameworks/flutter_email_sender.framework/_CodeSignature/CodeResources</key>
		<data>
		gNfmlonOa2pwu4RwRUTpEGSN0bM=
		</data>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender</key>
		<data>
		slKdJZHKggQNj/4+qmaQyXgeJVc=
		</data>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender.bundle/Info.plist</key>
		<data>
		dNau29NMZA6pLFb8iONbhYwuZjI=
		</data>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/Info.plist</key>
		<data>
		P8cufGsGOyUmG4JbQLBlaOVWskw=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/_CodeSignature/CodeResources</key>
		<data>
		IEBbJ617pwEFrvtDzh2ytCe5S0o=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage</key>
		<data>
		dTHiHTBXRNVL4afzUYcB+I7NMpU=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage.bundle/Info.plist</key>
		<data>
		7y2jkR2BkvTf3yng6n6VdNWe4WM=
		</data>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Frameworks/fluttertoast.framework/Info.plist</key>
		<data>
		czscRg92UDnTtupOiuyIURXJO6s=
		</data>
		<key>Frameworks/fluttertoast.framework/_CodeSignature/CodeResources</key>
		<data>
		0FJABAzrxwOWswlpT1TvqDKMLUc=
		</data>
		<key>Frameworks/fluttertoast.framework/fluttertoast</key>
		<data>
		uDs6JuvkON3AfqxdYv1g7W5xL5Y=
		</data>
		<key>Frameworks/fluttertoast.framework/fluttertoast_privacy.bundle/Info.plist</key>
		<data>
		QTDuc0jd3R+ykdEHQ7Zazf7VLcY=
		</data>
		<key>Frameworks/fluttertoast.framework/fluttertoast_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/geocoding_ios.framework/Info.plist</key>
		<data>
		5t9YHU/a5wAA9uVKWUbniaPPPKI=
		</data>
		<key>Frameworks/geocoding_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		hGKeGgIqBgzcxaMML6cx0Cl9JPo=
		</data>
		<key>Frameworks/geocoding_ios.framework/geocoding_ios</key>
		<data>
		RrxW0XirIGlDn6ZiTXTezQ9AcBE=
		</data>
		<key>Frameworks/geolocator_apple.framework/Info.plist</key>
		<data>
		XFe3fcPa+SNW5eTKZhiXCcm03Sc=
		</data>
		<key>Frameworks/geolocator_apple.framework/_CodeSignature/CodeResources</key>
		<data>
		AMziHxptAmjNlLTtG2n1gVJ/ypQ=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple</key>
		<data>
		3220gsd13WwwSpVDQXgitTrZlXE=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist</key>
		<data>
		penY5T9T8GhvQb0H6Br8K9Al7p4=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KFiVi4mWKmBFkTjfe3H4jsOvLNM=
		</data>
		<key>Frameworks/image_picker_ios.framework/Info.plist</key>
		<data>
		rUPFwxwAIGfEaC1T/+S3k+3rrrs=
		</data>
		<key>Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		yOi+ZbWHKGL4V7EAT7S7XEpK3VM=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios</key>
		<data>
		VB09Nts/2v6L8vKtD8YSwOxmI3E=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist</key>
		<data>
		C7Q7wLLXZ2yyCpvd2LDNc/ze9LY=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<data>
		bQtdo/dKXXPDcCs2lLCxf+jOWto=
		</data>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		JtDasFNFi81/TCXsWdxtMvW2r6Q=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<data>
		Z+4c3eWzXxbJrns+sJS2bVC0WCY=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		Ivj7jTwO1E+PtYf0b2MScU9PHFY=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/razorpay_flutter.framework/Info.plist</key>
		<data>
		k9CC5hxO9WOs1Vp6Sev4APVudUI=
		</data>
		<key>Frameworks/razorpay_flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		N0EMAhV8aOTH30anYBbB8FTF+EI=
		</data>
		<key>Frameworks/razorpay_flutter.framework/razorpay_flutter</key>
		<data>
		1Fxc1Ok+H/PQvlPB9TdQUpc/+/c=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<data>
		SsAip54yv8EXPaqYihcYuQynGTc=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		TC+D9ae3xeVozjnm/CsyZDcoaWM=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<data>
		mD2I5+OCVw98wL61TpnRk6OKjiU=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		8MHeEKMUQ0EPqMs+h2r55Q1aeyc=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
		<key>Frameworks/smart_auth.framework/Info.plist</key>
		<data>
		n8R6bJl7Gf7FcBC01EMFm+kvKOU=
		</data>
		<key>Frameworks/smart_auth.framework/_CodeSignature/CodeResources</key>
		<data>
		09YLtARcGaEOYBoKkdvkEtnwrhg=
		</data>
		<key>Frameworks/smart_auth.framework/smart_auth</key>
		<data>
		26ChVcNo6+Y076c/+CaGXUjLt5Y=
		</data>
		<key>Frameworks/url_launcher_ios.framework/Info.plist</key>
		<data>
		E6OWRs+vP6Qdl/jGiVakV1fRtpU=
		</data>
		<key>Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		YsJh3h+ousKSA/0nGWWkW09gtxI=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios</key>
		<data>
		fEK61vm7pXXArRPpX4rHI+ZOdc0=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist</key>
		<data>
		lzgvWiTXC0fcAg5uk0fVXHat11w=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car</key>
		<data>
		gBy8o+lU7fq7hVq+9H4dj3a7CkE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom</key>
		<data>
		Oon9nPw9yDxNK5I2dkjliQ+q9CQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom</key>
		<data>
		FFqyrwkOXS4RV4JcHko8WlrvxH4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom</key>
		<data>
		f6iV64AmgVo/8iRBMSg6SVkz2TA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist</key>
		<data>
		HTYqw3m8dIaBrYtkwin0OHV6t04=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car</key>
		<data>
		E8Ck3L4tjw/ILpwiFCEz4Y8szFo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf</key>
		<data>
		IM7pUc3dgbBfKBC7eTUgbJOheGI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png</key>
		<data>
		KBiuGmPvZm6y3heWHurqjBMitJw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png</key>
		<data>
		/ZC7kLA33LLUaGoy4elYV+v1Pss=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png</key>
		<data>
		UPqwKRHKhiKSYvXhNjoHx17Nuzw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png</key>
		<data>
		mfB/MyxUmXFg9Vpp/U5VsQY7S1g=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png</key>
		<data>
		jjx4hEkp/WAC6uAe/KXdwGjm6CI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png</key>
		<data>
		71kfAzHiEMvBEzOv7EHvlQHjfuA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib</key>
		<data>
		cQgvL+PScbS4JqappS/31IdzRGw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib</key>
		<data>
		doo26zSGrtADDZtuyBYSBQqBGBE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png</key>
		<data>
		ey+dHScVUXPkJIjjHuWIHqMc+x4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png</key>
		<data>
		PiTZA77HRySkmvo2YD7uXS0sYS8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png</key>
		<data>
		P7DEu6rMJYoBQSGW31uStrq81F0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist</key>
		<data>
		kBcvy5EM0LZteAaG+mrPH06vsBI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf</key>
		<data>
		QKmhT0236O/x77A3aFViLI3U0RA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JbmKQ7Yi9nb9O+v/B5QrGMl5qbc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			09EM2+7axQpHwCjPWI4QcQo3dq8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png</key>
		<data>
		58HUtPew0VfX28iT0hoZoqqV8R0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		6gBjAkHF3kLwzqmfNBlRZY9e/kA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		wI+5cU+Y/voatJ+IHCVJWAv6jQQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png</key>
		<data>
		DSedvNbNTixsQO+c0IlL1PkNCdY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		euWXSDkE7B75wvPAU1ssfZA9NaM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png</key>
		<data>
		d+Mu/JBZVfGIW30YjWEaWiC/sMA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		lE3Jc6hIhQEfpuGBhw6EKFWV+Q8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png</key>
		<data>
		1h5giTFrQI++3fVXazIMR/7gzoY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		xT8ttVKmlQQ7RypGMAzTOIjuhLs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yjTzffTeb5fDlSkw3rG/aYIOdgQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bzTpYpYOsxUvdELEsNav7tg8Uqg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1WXJdseg4LVPpUrAIbaFXL0iRU4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png</key>
		<data>
		6ZZOqO9aCr59xWXdfdxHYMMTkEM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kXIJA6+wX/K9jf2Hujfjog3tcNo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ue+ybnKPpYbbRYlnUjLCJ23NmIA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IHYnabnDXvmCRBqRyQB2tV7WS6I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5lJIfvDH2f7QNivaY24C1v3IMSA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5lJIfvDH2f7QNivaY24C1v3IMSA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5lJIfvDH2f7QNivaY24C1v3IMSA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aTt+26dsWZeYDS+XJp91dJW6XPs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6j8AZkLqElUzbBSleBGbbzJVv3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6j8AZkLqElUzbBSleBGbbzJVv3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QshaVMsla0C4pBe5s1r3/je8G6U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gxOrQTrBzVCR0KLf/2DjcghR/38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8OFgRokj2aQkz5PAVj6yhWCkrb0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8zYc83A9d6OqMODdRjv+FTOB3qM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EkVuUv6RtviRuGZAFmgCZQXC4Hc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y4gd7MUWlUxVrC0S4jxqZw5Qhuo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3TKcyay/Qz4LthmS3cN81pR2yeY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			e9+43XmfNEHP9WJDxSc0geaYSac=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png</key>
		<data>
		bFfwK83wBj2pyvoGrW0gsjjayaw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		hQkZK9WIJ30yrzBkesaTSfM5C1k=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		FCUpCQIg8t8Kle0t7ba+v99x9x8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png</key>
		<data>
		//Qx3mc4+ewO3CjLc2Y1//23DEY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		KrTZRKDI7Kduyxjzb3s8GUaEn68=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		jAhA6BSLXPLryZQnXa1GUxjVKi4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png</key>
		<data>
		FczO+2H9ZOJL3KtoFkr7ZEbUEo0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		ik1Nng6iqN/+ruu7baKmTu3Cy/M=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png</key>
		<data>
		IgSXn2hc/27HC0evGvhDhTXKIE4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		byDfU2A4NkxQbzsuboUTK7iuvnI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		tHnPy6KqF69QBs6+b1B9h0FqGRU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png</key>
		<data>
		/WPXm9lH4rlc3x4vdnr7k2RHkEg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		zbZAM7mPNNvouB2QcXsTxOCJtxQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		P08jpJQZdro2N5VhzgQnu+jsSZk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png</key>
		<data>
		b4HAoXXQ9MHUkvo2sAUYv1olAFk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		aRQYYYNELohm737W3DQtx10B1Sw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		cuJK6gmSOjEhVI30xIeEIONVSTQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png</key>
		<data>
		jNNswlCF0SH6V+5se0OWriTR7uQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		lUI1aCanVG7Yzr2hHEiHEha2VNQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		JWzMUhmhmba6Wxo9dCHPf5IQb8s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png</key>
		<data>
		8O78ScAfBRbkyJp9FhiadAejM50=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		ZHFlVPKPc/Zwb3ySc9nBC689uOU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		B+VEFeaeFm2C6uIBDXzUldmMLZM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png</key>
		<data>
		KhSMRxKx1AkoUpWWV+VBZqgpDsw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		nwdneM0KoilU07pSKqKyw4UeWxI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		GIkw+XPhxJ6mPgnW6nZLmostL4s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png</key>
		<data>
		yxcShW6PUH4L7SvAnPK6znnipCk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		YEJpYFlKpPtva6iPBIofJQGmrF8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		mpZTRyQo0MeDf8tFP5XVApaPBB0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png</key>
		<data>
		xh3gGmRY86tJc5Lh3Nd6zBYqSbs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		NTF65BOR+sJY4SRV1e2mgkz9kmc=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		rQpisYk5GDcvEAM1KX1zN7Tk0UE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png</key>
		<data>
		v210WlF/V/X0MFX7rrbTkt6/gKg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		+WqWJySO0WVHmdN9btErTRncON0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		Nda7w6LKUWLwuNgEGSa3UV+Gu4c=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Id2d1TYMOC3GPpiy92XGj//yFgI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			D4rPH9dJz1fTBQeKsIB6AYKE2oo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ak1jUzKd12Du7Cn/KaVBeSviYI4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LNvs+CzgutdzKwN0XZXq9t5F30k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vFpe6HKmx3Wty7ZISQtlzkcxpAc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0tSKlBt3yuxrW05GsNzqdnrT+h0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RCKBIodaq/BPm8R2wxxZI+BN2sc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DWV28d5M08cuv9DtzUysDXFbO+A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			MYlyD/zOjrnV2n2U91CPt24WJ0E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wQ4U/Q+p5/zoTHZwv8VBf3uhAk0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			O5DfgysQJzzCd7j2eGrgqbwT+hk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pruyhhc1rJKou3MKILqUZ4Lrc70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png</key>
		<data>
		z1kf4/sWpoPOzvXlf8K5f337H7o=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png</key>
		<data>
		YoDHbGi2Kzr6KzaLWpIC+iHNW24=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fLIf4JvrU9JH3q7AGeGEUnwBnmo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fLIf4JvrU9JH3q7AGeGEUnwBnmo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nmztYu9MrcyhylrohbORXSff/NY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			UbpquNUw8Pb3AjWYytrJKXwBwmQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png</key>
		<data>
		HG4lAzW99jq1X860ruCadRTP1JI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png</key>
		<data>
		Y9Cc3P9fnqQlvUq6hljpmps6Rfg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png</key>
		<data>
		RnyN/LbKvVpEJtWMNkWwn1v0jnY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png</key>
		<data>
		liaTlaCHAiLcpdhS15/FoNQuex8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png</key>
		<data>
		TgH7/QavDGQI/skL1YE/6rzYH6I=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png</key>
		<data>
		5lTmhJEnxGk7JCSt1o7hQvsaZE0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png</key>
		<data>
		RMtreAjsSE5oStFTdN9QLiWrM14=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png</key>
		<data>
		solZa4fFXHU4p61KSegryhqGfMM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png</key>
		<data>
		IGEsKFv6icS08/TMkp+MormykLo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZQxxbOa9SqerIFcZxVCb7hwnVPc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nSlgSiTUs/Q8ntegpWOmKpS6OkI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			k5p4vwv3FB0n44izHXvxmLEu6hc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rF14tC1rzneYivKZXDGt+aRKOkQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			sJHCPhWQxAWGu4lVlTzB+DoIegc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3OrZzSbV0A8W1luKhYLPVH7i9ZQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NhXIJxUR+Rn3xqHWJfwZSswdydk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Un2Wg0+U5Up8KdhDqnHZOZA0W0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xVreJku1CfVa61YyrPDj4xCzYAo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GpZL39kYI8efwM11Q2euuZPc9lY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9e+3O4rjHEfIQylawQkOstDGWFE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mkxZfPLm1uPPz6PPyn9RctnE+0Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0W3/Rg1FHTleO/XRiX2MgMmLgW0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4KI0ZC3ZrAiaNpBlwgjmB9NaZnY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist</key>
		<data>
		utU6qxazBradXD3t8ogCN0Q74wY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png</key>
		<data>
		wEgufayRRGE/XJ0qEoTBxkKrlHk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		Nk+SqgFNjGYaDU/37+ZTq+lPIrk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		PeAkYTqZ90NC+h3Mi5El7hzRBgw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png</key>
		<data>
		y8j2sPykvEUgCCWbqkMQajBUA70=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		szkKZfIOIr+5aD8GPzuDEHHmsBY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		YSf405gjD1uaF7YDsam6q4yvNso=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png</key>
		<data>
		0CVIeN+d0Z/e6FHcEzk0GpRz9KM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		BYvkcO3ien1dBURGTX7/iQ61684=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		4z+GVXIKMXonei9wATmOpaG2Yp0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz</key>
		<data>
		S51HodwJD+rwAAsP///g/nP7YYA=
		</data>
		<key>GoogleMapsResources.bundle/Info.plist</key>
		<data>
		kssFr3sSNEwlN6iZf4bZfGllJqY=
		</data>
		<key>Info.plist</key>
		<data>
		cyFQJGqRj0pMYgkpHZqgkrOk24g=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		sH3ZnA0tXKs2EELbnZo16e4dr20=
		</data>
		<key>__preview.dylib</key>
		<data>
		50FFoRAB6HZ6bAOIdHY4aYTbsjE=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		dW9p2HILcQ6OeXEMDkj6ujYKO2g=
		</data>
		<key>google_maps_flutter_ios_privacy.bundle/Info.plist</key>
		<data>
		We20CGBiRzaBNf7ViqTYG7E28eo=
		</data>
		<key>google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		2883mGk+25yVMT1GYe05XI1vTMQ=
		</data>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<data>
		OrQUXE/ibB7qJHJdkQMLUC7rFL4=
		</data>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PgAJpgZlblxKbgx9eihlgflAQU8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9VR5aeuJMnm/RgXM3cr4LUNi9UZgxKD7xAgkid0NI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			nTKNHUXzhjdeKHNhWbT/pTxJMOOWIBbb+YsCmk6AEhw=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KaU6BpCNLefiwNS1DZZ8xhrLM/dmicIH5D8o6XJMOZY=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			3j9MRsso0SGhrNazDcIy61NcxiEdjPUJYglH7fKrq74=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			by6WshwXWgbEYiAy2bvh0UtjSVa3EwySkNFc1FazGdY=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			Agc6PqR/OeBGwkxJY6LhdyMtbnpJR3WJdUdTPRN8mQA=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SES+IrctFtb2ATzloiQHKMcw7x/vnPQ6XFUZixhhSAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Dxy7Kw7nz91OZFZWf5QT3XpOhSn4vCzNMJJiUZxpmW8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			XhkARm67kx0Za5UTSpy8CqPtlZSXxrc271Aor6j3dog=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hvXTwY8U8BnGOdiJ9h2zphrz7I4sRCTFr9pMHaD0a5U=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KLHrKz0uGtYLjIsPkQCxzL9JL3+pf1vrtR6pfnOSbn0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			PBU9JvrJhW1R+eJuNwtQ5emGyNLKMmVHsrwZA/7E+I8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/HyderabadiBiryani.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			u8KVFhmiQAuUmrMyvX0XF6Dz1rgsNaiOZcz4jglV0S4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/Idli.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			UBCWv0KgrDlF+nU91YyaQsmv/K0PONsPOB/9vHJxf3E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/Pani.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			V0qyZ6ER5WCKR8C1C1jRxEFcmRE8ogsof1iyTBKWCWs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/Samosa.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			tgMuz0di7teIIohWRCEgRzcOYLlXZ5d34iRN72fOYCY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/bandiwala_logo.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			SdeRgaOT1A2aKMW6k9TJcqJd8WRCkqJksINLWKVrrPw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/chicken_burger.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			IUHLdCJ04eOLQYc9B2/mp7TkuBPzenChhwcEbG8Mi64=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/chicken_manchurian.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			oSA4wvSrdI0S67mb4OI1lh/1SxdGgxC23CUWwrEJwTo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/french_fries.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			UniiwYT3gpxztroF03TYW4BXhPOFdFvpdG17lnx7sPk=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/google_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KYKk5rTBI4NdrbQ2fnxEKwmQCAxZrpEHJvNVFiJBUJw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/gulab_jamun.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			oconroH6jzVK5VSm0CXRnxSLNLJpg48IwX3NO5Xrcg4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/idli.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			R9BWm/tiugrkPMzgLa3o0PtfFderdqg1cWg+1aXl4Us=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_chai.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			CiEJk1bZSJhSo/iB6dcNOjItHd/NTUa8KxgIDs5zZYo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_dosa.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			Op+hM+Gi9TftG3MhRTNQgte/O2LlCkeXfDxltm0H6ng=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/masala_dosa.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			NMkLUoL85FNIySQadb7ZTe/kouRRhR4gYsL99ZjfEtg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/non_veg_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+KYnzZCXDY50n7MGEJnKYzV9b/jLdK4bJ3pkJKORr4E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/paniPuri.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			GmR+j+0EUT5z5CS5TgiKMOSZjWsWIC7cgS9qwW8IZAU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/pavbhaji.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			k/RpeVlJvZz2GJBpL4uVt6hREVGPHzRRPiBNJGobFXs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/uttapam.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			lOlLw0ljkk5aS4on+DTiwPkOXi1uoIY1CaiKVw8uRb8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/veg_fried_rice.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			4Ry7VtPbi17Z802aFVBa1Q1JEXY5CIQ0oRw0LcufFPA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/veg_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2gqDntuDPD+HHjLAp4AsqM9IwuGHOyWduoFfeTh3sKM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/welcome_image.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			xGjbOLR4kVLq5NEIiTttcFiJNx63MRf8YhRp5+OjdLM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			PBOAqqWi5ujK3/k/uWcKrZyOam5QktQ5MqxZKes8MBY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			LdIdCfmIAyBP3zrYCim71gHXDJT9YYd6xi3k/i9ljoo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.css</key>
		<dict>
			<key>hash2</key>
			<data>
			abr0T+pbPv0zd7q8FU7xuDv4JjuRAgKox9bnRddRNJ8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.js</key>
		<dict>
			<key>hash2</key>
			<data>
			CRyacJlKIATuArBPER1Aq+QMB7BmhWOZ9YxYcELsKZA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			RTqty/gYjcrXxgR3QLjNukvL8gGpfV6MdaYktQJaIa8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			ymTB8szAw5H+mJmPqSkFRJ3m4rf6vhfGK6Jl4kJ65aU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+33F9+1+BOcx8Bl4XSagM9ATcVmw1/qzCNe1OPmnD0k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			1dtlVSYHX7a7266RT1WFpkgXa70YMAhMCBG9cOrS61c=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			PHb2ZuQOlqYYHw0P0haHf/aFuDNeeCbe+axDReTr7h0=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XV+Km0uI0aCCx6b8FFBL8ctnAUqg/+iH2HKwpDJJDns=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			9gbR1Bca1fy1VmXM6YTr9iMj/H9FiV5FH80kOSCS5cA=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0aP4/XYhE9pTrC1von5mej2B5hZzviQdM4PQj/nojlY=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			kuyVflk39J/m/Tqjb+YY8Y38/6KfwNjCkTXASxoCEXQ=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			cAbddAE/I0qU6/oKTC87iT4nJ2fd0p9/BUaCe80QOek=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			I8p2+VocnQu/7nTAHxHwPLoZ9J+et+y957CgfHi+MQo=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			h05YMT+1oAA6um16sH5KVB2c6toLm935YPmV2d/ZDU8=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/CommonAssets/Razorpay_Logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			udRErjoaEwN536HIEl+2sH6KQ0Q2KzlKwLYCkQlBGKE=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/CommonAssets/check_mark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			s+l4gXoMSGUj0xR2eSdXwQTHoyRU5F4+aTSVjO+I8wU=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/CommonAssets/warning.png</key>
		<dict>
			<key>hash2</key>
			<data>
			S7OOo4xdlAEiEgfkEuic4ap4JZlhvtYIwFSHWu034SA=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/EncryptedOtpelf.js</key>
		<dict>
			<key>hash2</key>
			<data>
			85Tocsg1aPekBFPeMKMV2IruNGVkOyyV6xlbQkQOH3A=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Hash.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			B20CsAEe8H45a3Ivw23aBTWNU7qGh1JYKb/iAwwaIEQ=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			AlWFfv5ry6kLx/FfdtyOpELwPU6xdhwo1mXH4xfNj8M=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			0GK4q+J5XVD2O8agumlONknk2PSlkzr97y/P84XeCyg=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/Razorpay</key>
		<dict>
			<key>hash2</key>
			<data>
			c07H15tdJ7d3ApZsaIu6qK7+oKEHWFtuiUXhVX4HsQI=
			</data>
		</dict>
		<key>Frameworks/Razorpay.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			x0cNiRuScK9jBUBohzwHzFbRcapNXCm9spU8+s+vJt0=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			5Rrh44COIg6kUhxqYoh07aOwaddzxMNgopfOzZAgtE0=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			L3Gd+Hd3rVdakZnVf0+vORLU+DeHuhL0mteqJ+RPzwQ=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender</key>
		<dict>
			<key>hash2</key>
			<data>
			FKEAaGpUKgnNpyi9Avn2BhOg+PEPe55yzEsLpfBbkDA=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			LkzzthXcsZDo9VkePPK5kBLxJl1RQ1uIBnDQtVz5Ijg=
			</data>
		</dict>
		<key>Frameworks/flutter_email_sender.framework/flutter_email_sender.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Wm6uXxr/gqyEEkPeX3K/v48oimbBKLcN1ObGV8e1CL8=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			msMpJFxbVKxeT2oUk0f6vwBIzwTTlE2kvdOzK61yYfw=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage</key>
		<dict>
			<key>hash2</key>
			<data>
			hG5UL3GdbdWkac/G7/inN3TMc5QLwpr5SisiguZFtKs=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/9CkrRRl5MmlJE+ErcN4coYA0BAStCXRJeDdA5U6R1E=
			</data>
		</dict>
		<key>Frameworks/flutter_secure_storage.framework/flutter_secure_storage.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/2XVzhppdcN/LEDMyC4b1PmzIkT2UT3EfHmw3/g2uOI=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			s8+cjO1Ggnw1NkSXe+PHWa9AApKFWWeBVVW2Dk9iy8A=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/fluttertoast</key>
		<dict>
			<key>hash2</key>
			<data>
			8LK+SCKUhw0oO81cwVSr9AIye9s47HKDxgjBMt0TTMY=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/fluttertoast_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			IQB2Uv4kxKmM58P75vBUYbcRIAV+6ixFowoPLupj2Cs=
			</data>
		</dict>
		<key>Frameworks/fluttertoast.framework/fluttertoast_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			nOFUfe2g0oF6sW7ono3ZDEW9bc/tKrRAbALY7iMP+X0=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			o70E494J/UbdH0U0w38FH/qtYVlOQzF1e/Wl5BSR81Y=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/geocoding_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			4p1aTbw0BcetCYq5cDZDloMtaKKFaymTt+LNhucZt0s=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			L/2nGlwkzI01xJmRc1aLXAuwZClWruF+z87dPL28D1w=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			NeOYmOvsBKaCQzXvrTUBMSHAm2bPctv1HpaGR3uYlqE=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple</key>
		<dict>
			<key>hash2</key>
			<data>
			P8TskbsTPiPxgYT8oc1rtI3Y74PHrggKsRcQZdfQAbo=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			xB/8KKKFMV9roXRZkNGhn1TQKI7yApvx+8e53HPemP4=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			p+c+xOFN/pYr5bFknUH/y01YGEb8+JJchzbtJ60mdTI=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			kPVAyr4igH7kttjhvGmz8Pjxspl9/sGeaN78v1BWIG4=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			BvMsaYvyzcCV0bQZiTX4M36opeYYbPC8RJ99a5PJHOI=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			JGtsXgJ1PUji5bN2Ae1TIuK0yEh7PqvhwZgBTAWmInI=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7zwbvOtg12AAcuQkZ+YGcHai+Vw7vDCh1emxDnF3yok=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			mengm+plKX84JzZyHng7jtTSpfU2z+5BGek9PLM4yVk=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			E7ZaY6IBf20LGYHC55x1+d0fG+5EhNtbT1Xn32eWEHI=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			ooShwOjUEg3Z5S2/PjDpC+w3J0Nei5d0Uy1lv51VTSQ=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7+qH1w+o2MD8y1ECUSZHGjF0x36XFn2BK5dWRhcc/F8=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/razorpay_flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			fH4RU6FqAQRJC2Tg79I1wQgiT9qHcsOEcy0ps/RhJL4=
			</data>
		</dict>
		<key>Frameworks/razorpay_flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			4P9DDqB8qeHXDNr8eL57iZMEnS2Lm/XRpYm7NjQyrMQ=
			</data>
		</dict>
		<key>Frameworks/razorpay_flutter.framework/razorpay_flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			lt3HHSuDLqGkzs+uczhH/1npKHnwTpKiiF1R1wO06zE=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ghpdpv+1FjbcAVA/70MJYL/3Goh3ROD6DwSmOSeqhp4=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			v6Qr2GgiNHQu1F1lu+NqbKk+1LXuR3FV49eKSxs9gkc=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			1qrdfCpBxafNGYMtDrKeFPJ3mw/esURlXfqMjbcx7Vs=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			NdrRz9nrDfceQ79UOqblVpLmG3eG3Pkzz553JkBrLCA=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
		<key>Frameworks/smart_auth.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			pBdfw5tKD0kIt7PKMPbiJZwkvj5Auf+kFDtfalmE+A4=
			</data>
		</dict>
		<key>Frameworks/smart_auth.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Lld3Fg54GlIBpHYczPiJiB8K2JAveC27/ljvZQY60Lc=
			</data>
		</dict>
		<key>Frameworks/smart_auth.framework/smart_auth</key>
		<dict>
			<key>hash2</key>
			<data>
			CCSrjEwy89R1cCRU7nQro6m/DcZ9SlPBh1IXJfqf9fM=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			UI/XlKsEQlVBEZda1OamkFqDFAb2VOsPog1An7H7C/s=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ui4tGRbuGrZSxjOERRTcgc7ZE1rNI0IiqSsuL/i+iN0=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			JbGwsz4B0MLHaPQayr/Pph8QGfd6CJM7lRcssquJ6NI=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PPRYT7DKZu4cenyIugMEigcoPAP+BRAF+WdQ+s9/ywg=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			u/ze0fYPdR/4VYu0J+vM7e/U0Nlitf1uLLSrfYM0sVA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			Lx1844fBORK+6WxlCd5hy8b8Hx78DeEXncpstshaFcg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			qUKKq8xAyT9yMLZPuZvJMLiK4Fm1awhmv3Ur6mn+dsw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			4lEEfqa7KO9CdF4ONbnJpgJuHioXgKm80JgDPubEDO4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SdCHFcwUWcOn+6S2pVcKPWo5J1cOtjjALFOZ8D4dur0=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			cYagK+GGV27etFOXEItpW7uNaKAl2gQd59kxZrHuamU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			oRgIxXbtc2TA+CQC4IYwuDWJiwC79rQTLdnyhCHh0Pg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6I9AZoWd/JQ8PV9b4FcAqf5GJT6iUEe7YeuyNGEkwfc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			35EHfeztT/7K9NKjZJGCUlauQ2HOF3PBQxkLgM2OGUw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yGhIyrKtKLoQ2oJfnH4RrIxHQUQYODC5SL+bLjiNbxo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jRfU5I+eBrG4NBhLV9IBsXXb0DJ9cXoHbCE4CN0QiUc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			S3sLYbDleAUGAjWIVe8mvnmB6ONkTxlmkd9BPlylOWs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0SyDaAgdNWt5XirK8fh9r/KPbbBqlWeaa1+p4aMYRvw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			OHvYEVcftEvIaZzqtCyV0GTSm3rfeL6Nh7/2yNafnTQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			xyuB0Ol44wtr43GIWv25F95LCrSMvCKPLdXm/iLUK/g=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			reMA3HPo0ZkLCelsd0yLOeO/Cq8A543Gc5rDKt348Rg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GaMGqwoZ17o4/oQ+E5o3mcf4nZdU+fY2c6zouc+aHQw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			sg1gtlyHg36nwhRj66w/5buuQmGAtaveap9wGP1kQ98=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			LaWmbBXL6EOELT1JTPDqP5a3Jcj/zaZ/M4f7X/IJcJU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			4HMV9v4N9IH0373fJ215YuoHOKrnkWwgbsN63C7LlNc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DiOX80dqLJHNOFs8pK7D0mrw5UC1SlY+YVruxgh51u4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5COu6rVzdMf7p7ab/pKliPYpZWshPzAQdPaeT+pDHyA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VUfuf1zHkFbUsHfs0W2XgKZU1+OccZ1EFTBKqUPRvpA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			L3cYb4rSz5yv0DjIESC1J3/cb8P0Js0Ff9mVn459XmA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			UJlmp7f16dQ1TqC/a0uyZ8RGy7o/exUVKOFU8OsbbFU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fFQ2JJGO00f80s6uU4uleAHJ+R9BTG1nUEYfsfZY9Fo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			KsU6yUyahIrTFyLyw00wG+zkDkxGGocVZMhX46e19Gs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyxSDpdORHSyv61cmUzZqM8ndta0mAzjXNz1I0LMonI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CriYgwzkN/I/cVy1eJf4HI6kfJaC2PT7qk3sqSPG60g=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qHyHEkW8JdVyAqojiLMeEvdbdu29vAMj/80KkzOxCoI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Lit0EakIiwZVnj6fbN0D3wgszTkSE9/rnfzmpGBwUps=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			M+yc0vsi2SqZThuot28Ol92alz0wj+256BMkt2i5mKs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oUajzNZqfSA69lkv+CsikGfg64oxk1xqfdZ+kLtraJk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0akfBfrtBwmJYhF6g/WlRnYJE+CfUj8SuRDor4qsfwg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7/vwjVbNju+XCu1xevYaVQ4/JUsqO/RCsRDlPNq7kaY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DnRVfvU+UHFrNaIHAkx2603D8XNia2JPQ65WEYrT19I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1OWW2r+TcI04GAzLLa7dKGBUCWYyguNu5uwT0YYLKOA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fPpXDX73lW/RnZTi5eGVKOyMVlJ7aNEDXRbk9JbLlJ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3dInCJ6Vxw6N9uReExxeVg5ZETji151akMx7S7eVpzI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3dInCJ6Vxw6N9uReExxeVg5ZETji151akMx7S7eVpzI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3dInCJ6Vxw6N9uReExxeVg5ZETji151akMx7S7eVpzI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bNEqmxtX/c6TwMSQgglT0qxaaubbOsKvp6wRJZ+bwXM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CsEz4d4OQ3tyiePZw5djebxIUHOwxb3ylz7i+9+Bme4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CsEz4d4OQ3tyiePZw5djebxIUHOwxb3ylz7i+9+Bme4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1VGyN5Sd2ufWo8WeIMlDBRKy04yW8ilMgDuFeomMRT8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Yf5Ylm4f57mHp+5F5hrpG++KQjhdLhw+uEI0m5pigbg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HP9Xs4hGscjnhKPQ5HkIYVYpoQNjnDz1Kg6thffHWNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			R0mdqjWgHHVgSKu4xgu0z4U76Tiev9tHPyjO1Kf5WM0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			b53o9kQV4v0Fi7QrM96aCO0VC2+64dmsuGOxJBcW8Qw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			YOhL4Omy9LbQ178xmODPhZuGaE6d5QfkdYlhoiKN1c0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			51q+9WQFoOZ7mTbzQ5SbbBPVwUKi9upgAvK+mamOroI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			usfYnEtkJd+Wm0ov86GfzWOHNlFbjbTpUJwI11DYBLI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			XB10YfoFMMA/1hJ7RcpPwRn4FPISn4/GfcLt1yacpbk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6Zoc/OxM+MGLTiTWiXfGiG/vQbaZu+4BpYdjwEWmO+k=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			32rNSQbMm/k11a9saRft5TUhSAEwkY3nI3izgMIVsWc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			tA6fq0SW033/kwgkIJ6B6sQ3DSvEwrztHLfbLzPMZJ8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			7w1goskgoHENDngEcZmMetZnbBY202XkAvwpkFUUGmc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			9NGwfkNI8H2ANbn1RB4cLWXlA/Nj4yeB5459a6Vr9Sc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png</key>
		<dict>
			<key>hash2</key>
			<data>
			V2HcZbiTq9tAJFbLYxUnzbb0utxxO6kEVKiZsw0cr9c=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			l7DXZC/yFWAiJnn1y7b8MOf2wCeJgHDmWYdKB5lMTS8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1TKLueEVV6y48hWYWBESJz23RavEBopB2CMUaHlutSg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			HcFbnY64QuHwGzcNE6SaFXzwaKhtLHJf4uQ9wDMEgGA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			GaTms7wugZAVzLWzjmz62i/7XEjgP6usP/nU20AYYP4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0TDx02zeOyLukli+xhvRlGoyfDPfmHWEXjHT3uzpDk8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			H8OXFdacv5KzG+qLR5RrChts7GKxClFlKqHyXNg/6qA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			z9EXEDKpCzvyZ7kGENSEXZznpcbyINaD1V8HSi9sgSs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yBxDOtAyeWqzpC63ugLpJhxYcAscliU1BgZnNdEcxmI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			0RtBxUvJfAOQCu0YN4P+NXKIgVGXshxUEVhfsd5RBek=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ccFj0dMmtprxMVMeVoKpD1/Kb4WJxACoK30HziyMr+E=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kE33BkYF1sZUQlzcuqab1WvHw2bKowuYmlzrbJpShF8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			1VhiJ1I7+yEdNt0CQMVPQt3sqbXWCS4N+l+OU4f+Wik=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			hl7Q3DhOytiS5PxACeNjtBEwRxXf7l8c3w6kbzMZtLY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jDxAgphhHwy2VvYUHX82RqRNnQDGP80tsgbIm4TM/Ik=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			OflLl1p4nwACrp977dRsNpKM9fZyFj0Yckli+0LJ7kE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			OiZcsS9CGROol2GoyCRvbD/1OnnO5Cr9tMmymx+gJuE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OW3vSyC7N+/XTypt4QcbljeyrObk1gU5n9aHHmZk5oA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			4JsF2Fuy38x5T4fSQJsUzzGbcTca7TicSUHZ04JfcyE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			PfQ+g1OVrp2reKoxUY29V6sSev0XbPSVORza8Pa4s+4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			HgTUWKNNKDODh4K+sGwW4Ydlmqb22QS0xe1R27RpXcY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			9i3bLmY8j1wPb8oYLkoXlfbtaxJSCwouObY+wUi4aUA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			RyZlVNbjaAqlDgKugE8FWdRwJhKx7ew7iQzSCsccclk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JtnHExDZopxjHDdkxKvEUTYb16WTqGyOKzA5Al5e17k=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			yRv8itmfa+f7QkgZ03TsPrjc+YcMHHZz9Oxeuc5C+I4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Kswz6NBndW1QRhKZxc87C8BWIjBoKm5mPxHp6tqgvug=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JtJ9rKoWknZ/eIujTeaSz/i9MKoY20wUUxxgY64wMz4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6PWtTGpNSDY/JmLM2KVXntVhoQhJKZUP/moGRbY3NMU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ILRnt9YuI4SFIzANE21lIdVzBhRC5hfsfl+AshH7LAg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WWuYhcGRPSEkS3jEe2ROs3IrNb25OFbcRQn2LuePONg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rkCfvrRgS8xYZnlpwfzv1sXNTJokABiWUBd0xirTHLE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KZNFQNC20A2RPP90HaN7azncoRp8QY1jPsH4epCAjdE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0AZlUe3I8Y9apquafluTQQuMl9rZQaABLCKi+iTsni4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			p0cQEikCmDDs8hdnPxIq24zJhIi+27Cd81errvm+3FI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			JXukFf88Wdzwa5tcV9HraZsGfM/F/70ZM1kltn6ihUU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UGS+ER4ARYA7fjUPNlaOVKTsV0sTtmPSqnimetvKkWM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			R4DZjtyNufPl2/2KaI7QurSbK02iITj1Rt/8UiEXGqM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vqFe1I+ykIJ6doyGy3Yuw0wrLvQoV3vNbO4YRXktYEA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lhCLxiYfmP3IyTSA4DA/srdffj8SZNGQlu3iq5miA2g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xOM7UCAMDBnqYRGp7f+gdgw/C8gRFChnyoIXN6JLczc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CGCb9/AxCbiU3Cl38M8SKof+EVcE3ocGs0gw5tKewUo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+dg+/X/HQz7XU5A0YjxVVb409jFOvCba/h/Dk0xwgJg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png</key>
		<dict>
			<key>hash2</key>
			<data>
			utLkKWB1FOz3jZ0C25RC8IQUZKhnBiX4Zhqy+V9G1sM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Ow721PuesGjAAoA8TA/11xralYhO3PlQYUK5+xrByfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Ow721PuesGjAAoA8TA/11xralYhO3PlQYUK5+xrByfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k71LApZTJgdqcCCsIkJVYp3/UaN0UugujINtnjw1bAw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RmFE28UikTR3rPOs9e3hWiozh14McNOSuHrohzpDwFE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lzM7SlPvx18FzuOhp0AfctCjH872VUYhotrEmV/bjj8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png</key>
		<dict>
			<key>hash2</key>
			<data>
			nxJMmc39cm6sjewdRFv7mVHN62HynJzISegXtemahqE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			gBiXF2YDIq+dMhZNzmXGIviFDDKW65Wwd5MGxawE07s=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			locpte2xHJxxYikj3vpYPAO4Fm0BJbKH3qAMUEHC6AQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png</key>
		<dict>
			<key>hash2</key>
			<data>
			xWNHfxSxFwcTcbRx6ksenSsdQax+gxZj5zqKa4P5bWE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iBDn2L41GYIiiENv/p0efRgu7EM6L1eut92OA3dwsiM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ucj2pK8WExUWdTnnnvWIu9iE9PEOo0rSvhyP+qAhUe0=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7X62FR3qKLKgCi3mkwhoJfSYdhXVzy/pTp6j1wtdE1s=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pvfCkomVwJ0KZwclEtmUR11mSagaN9LrXrn58aC8Afw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8F3iIfeZfkt3kc7zw4d4l5s9m2b9vFCn+p7G/68yjac=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			feymRx/Bi+chAP3Ey5Agr9Ft2cE7nqIhoQRcSalOf18=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xdPaO4Y0CzvRMTCC1q1H1VfThTHn2MaId+Q5GxGa3LA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xjWVk9Xxf4EPOS7zyMUmjLnnFVlTCeLpiM54ewmBeKQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1ORDxXYkUdGJcnE9Tv+nQG5zZQINeEtjvjr1fTJITGw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jTgCu5jFsQzYN22/wifVsX/RArJeiIrn85W+yYfIYW8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RC/EzqptnmbJr3KK7eF7JYvn5YMqHCwoT6JVZuSHC6g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k6qQBFkz41GpPIpKN8nbu0HCLy7uhiXBqPPKpgXMjNw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2aiHx4UseFbXgwNRIMVD2sRio9L4fh13g8peWZiSKy4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			C0t80wQwgnkrQq5eb1UOQWiu6bG6l1375PCYAm8y6Ok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XDRpaLGcAXpWlyiHwm48D8P53TDZavyuwZxOqf+NyNM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			sgjNca8LpMcjMVVQDTUR0Yb/VgUcg6dw+ALrOMRvuO8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ieYiGeYETunrujyWGcmRClgFR/nkeEW6hpip167/vjY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			eymXX+mzJGCbSuahiQXvbJtVXL/5XjaV+T5s0lYYFLc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			kMVj+72qX4tWZrrK+HdkNteEJQcI67CuY8B4J3VdxhM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png</key>
		<dict>
			<key>hash2</key>
			<data>
			esJZo0DQ/iA9fGTeZgnhHBaaXjKiwF0Q+D77afKUdtQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			85j4wRMjUhbJzw2Cj/dcdiSaIcvVJMiwY8pyr9uAvoQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CC5oWJdSl7qbV3Nhcm9VlpgXL0MZNtSwnDkXPFFjOAs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png</key>
		<dict>
			<key>hash2</key>
			<data>
			af0tM2SZMSGEXPSOUwN7JZ7as/APH644Mlk2l4IWqms=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			2tW3XA24iExRY3gHrbutr+hAVYLNOk3LrSXZmHRjUIA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			02zlKov8Ubrnz7aJ/JXa+qQ073+mO2gy2CgeYrDNF18=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZELicqEYSjWiwLH5Bpm+L59wifWIeso6rvF1uk5JPN8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			g+br70qT4Wo8OHYO9GEnd5pkjYLVQsyw1W8T8+pd7Xo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			l/lLCEplaZnTitN6/IXGuLZM5QXaxaSim9fc8XsRb3c=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz</key>
		<dict>
			<key>hash2</key>
			<data>
			9EipuF8wrGWtoayZvMCl2L2s2vseMUYkupC8WgxxM5A=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9ovZD6T5skwKkUB6hk4Ioub2v8DEEEyRub8grJQmgcQ=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			xlyvQPloo9jydvtVndrJhGsHXzp6NIMTfig2kXoxZZQ=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			nAz10c5dcVeV7LDekgXDe88MaN5gVPnYle30wng96Bk=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			iIp24BkfVXA7+2Obi9t+YK8RluhX41lpQ9vbdITfhLk=
			</data>
		</dict>
		<key>google_maps_flutter_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			X5pt+wImyBlF/rHpnlpequzQYIKLmK4OeRThIZaOGx8=
			</data>
		</dict>
		<key>google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			puoWk/6MP69ytJdvlbT3CeVa2vnstAliuu/Zl9LBVPc=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9Vtn6I35nWnTwabzNV2YTQGyrQ/EMpJnCu4YmphI5W0=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ETZWiZY6EZHpaiLgs59i8FuG0NJKvoBAXBpc7vCamxs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
