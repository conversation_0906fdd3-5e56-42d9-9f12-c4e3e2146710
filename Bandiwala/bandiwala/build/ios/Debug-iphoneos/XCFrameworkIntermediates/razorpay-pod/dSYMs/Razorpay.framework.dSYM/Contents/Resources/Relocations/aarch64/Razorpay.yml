---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/DerivedData/iphoneos/Build/Products/Release-iphoneos/Razorpay.framework/Razorpay'
relocations:
  - { offset: 0xBB928, size: 0x8, addend: 0x0, symName: _RazorpayVersionString, symObjAddr: 0x0, symBinAddr: 0x644D0, symSize: 0x0 }
  - { offset: 0xBB95D, size: 0x8, addend: 0x0, symName: _RazorpayVersionNumber, symObjAddr: 0x30, symBinAddr: 0x64500, symSize: 0x0 }
  - { offset: 0xBB9C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC14sharedInstanceACvpZ', symObjAddr: 0x10BF0, symBinAddr: 0xB9AE8, symSize: 0x0 }
  - { offset: 0xBBADB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC14sharedInstance_WZ', symObjAddr: 0x1B4, symBinAddr: 0x81B4, symSize: 0x38 }
  - { offset: 0xBBB72, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCMa', symObjAddr: 0x488, symBinAddr: 0x8488, symSize: 0x20 }
  - { offset: 0xBBC43, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCSQWb', symObjAddr: 0x94C, symBinAddr: 0x8930, symSize: 0x2C }
  - { offset: 0xBBC57, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCs25ExpressibleByArrayLiteralPWb', symObjAddr: 0x978, symBinAddr: 0x895C, symSize: 0x2C }
  - { offset: 0xBBC6B, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCSYWb', symObjAddr: 0x9E4, symBinAddr: 0x89C8, symSize: 0x2C }
  - { offset: 0xBBC7F, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCs0E7AlgebraPWb', symObjAddr: 0xA10, symBinAddr: 0x89F4, symSize: 0x2C }
  - { offset: 0xBBD0C, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0xBF8, symBinAddr: 0x8BD0, symSize: 0x48 }
  - { offset: 0xBBD20, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xC40, symBinAddr: 0x8C18, symSize: 0x40 }
  - { offset: 0xBBD34, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0xC80, symBinAddr: 0x8C58, symSize: 0x40 }
  - { offset: 0xBBD48, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0xCD4, symBinAddr: 0x8CAC, symSize: 0x2C }
  - { offset: 0xBBD5C, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0xD00, symBinAddr: 0x8CD8, symSize: 0x2C }
  - { offset: 0xBBD70, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSQWb', symObjAddr: 0xD2C, symBinAddr: 0x8D04, symSize: 0x2C }
  - { offset: 0xBBD84, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_4, symObjAddr: 0xD58, symBinAddr: 0x8D30, symSize: 0xC }
  - { offset: 0xBBD98, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0xD64, symBinAddr: 0x8D3C, symSize: 0x4 }
  - { offset: 0xBBDAC, size: 0x8, addend: 0x0, symName: '_$sSo11sockaddr_inVwet', symObjAddr: 0xD68, symBinAddr: 0x8D40, symSize: 0x20 }
  - { offset: 0xBBDC0, size: 0x8, addend: 0x0, symName: '_$sSo11sockaddr_inVwst', symObjAddr: 0xD88, symBinAddr: 0x8D60, symSize: 0x28 }
  - { offset: 0xBBE5B, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo38UIApplicationOpenExternalURLOptionsKeya_Tgmq5', symObjAddr: 0x4D0, symBinAddr: 0x84D0, symSize: 0x84 }
  - { offset: 0xBBEF2, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo38UIApplicationOpenExternalURLOptionsKeya_Tgmq5', symObjAddr: 0x554, symBinAddr: 0x8554, symSize: 0x8C }
  - { offset: 0xBBF98, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x5F4, symBinAddr: 0x85EC, symSize: 0x14 }
  - { offset: 0xBBFDC, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACPxycfCTW', symObjAddr: 0x614, symBinAddr: 0x860C, symSize: 0x8 }
  - { offset: 0xBC025, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP5unionyxxnFTW', symObjAddr: 0x61C, symBinAddr: 0x8614, symSize: 0x14 }
  - { offset: 0xBC0C2, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP12intersectionyxxFTW', symObjAddr: 0x630, symBinAddr: 0x8628, symSize: 0x14 }
  - { offset: 0xBC157, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP19symmetricDifferenceyxxnFTW', symObjAddr: 0x644, symBinAddr: 0x863C, symSize: 0x14 }
  - { offset: 0xBC1F4, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6insertySb8inserted_7ElementQz17memberAfterInserttAHnFTW', symObjAddr: 0x658, symBinAddr: 0x8650, symSize: 0x30 }
  - { offset: 0xBC367, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6removey7ElementQzSgAGFTW', symObjAddr: 0x688, symBinAddr: 0x8680, symSize: 0x2C }
  - { offset: 0xBC4F4, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6update4with7ElementQzSgAHn_tFTW', symObjAddr: 0x6B4, symBinAddr: 0x86AC, symSize: 0x24 }
  - { offset: 0xBC5E0, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP9formUnionyyxnFTW', symObjAddr: 0x6D8, symBinAddr: 0x86D0, symSize: 0x14 }
  - { offset: 0xBC646, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP16formIntersectionyyxFTW', symObjAddr: 0x6EC, symBinAddr: 0x86E4, symSize: 0x14 }
  - { offset: 0xBC6AC, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP23formSymmetricDifferenceyyxnFTW', symObjAddr: 0x700, symBinAddr: 0x86F8, symSize: 0x14 }
  - { offset: 0xBC721, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP11subtractingyxxFTW', symObjAddr: 0x714, symBinAddr: 0x870C, symSize: 0x14 }
  - { offset: 0xBC7CA, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP8isSubset2ofSbx_tFTW', symObjAddr: 0x728, symBinAddr: 0x8720, symSize: 0x14 }
  - { offset: 0xBC841, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP10isDisjoint4withSbx_tFTW', symObjAddr: 0x73C, symBinAddr: 0x8734, symSize: 0x14 }
  - { offset: 0xBC8F9, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP10isSuperset2ofSbx_tFTW', symObjAddr: 0x750, symBinAddr: 0x8748, symSize: 0x14 }
  - { offset: 0xBC993, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP7isEmptySbvgTW', symObjAddr: 0x764, symBinAddr: 0x875C, symSize: 0x10 }
  - { offset: 0xBC9FB, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACPyxqd__ncSTRd__7ElementQyd__AERtzlufCTW', symObjAddr: 0x774, symBinAddr: 0x876C, symSize: 0x18 }
  - { offset: 0xBCA17, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP8subtractyyxFTW', symObjAddr: 0x78C, symBinAddr: 0x8784, symSize: 0x14 }
  - { offset: 0xBCA93, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromF1C_6resulty01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x7BC, symBinAddr: 0x87B4, symSize: 0x4 }
  - { offset: 0xBCAAF, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromF1C_6resultSb01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x7C0, symBinAddr: 0x87B8, symSize: 0x4 }
  - { offset: 0xBCADA, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromF1Cyx01_F5CTypeQzSgFZTW', symObjAddr: 0x7C4, symBinAddr: 0x87BC, symSize: 0x40 }
  - { offset: 0xBCB58, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x84C, symBinAddr: 0x8844, symSize: 0x40 }
  - { offset: 0xBCBDC, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x88C, symBinAddr: 0x8884, symSize: 0x70 }
  - { offset: 0xBCC5A, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs25ExpressibleByArrayLiteralSCsACP05arrayG0x0fG7ElementQzd_tcfCTW', symObjAddr: 0x910, symBinAddr: 0x88F4, symSize: 0x3C }
  - { offset: 0xBCCA9, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0xA3C, symBinAddr: 0x8A20, symSize: 0x88 }
  - { offset: 0xBCD53, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0xB3C, symBinAddr: 0x8B14, symSize: 0x84 }
  - { offset: 0xBCDAB, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBC0, symBinAddr: 0x8B98, symSize: 0x38 }
  - { offset: 0xBCDCB, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBC0, symBinAddr: 0x8B98, symSize: 0x38 }
  - { offset: 0xBCDDF, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBC0, symBinAddr: 0x8B98, symSize: 0x38 }
  - { offset: 0xBCDFF, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBC0, symBinAddr: 0x8B98, symSize: 0x38 }
  - { offset: 0xBCE13, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBC0, symBinAddr: 0x8B98, symSize: 0x38 }
  - { offset: 0xBCE27, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBC0, symBinAddr: 0x8B98, symSize: 0x38 }
  - { offset: 0xBCE3B, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBC0, symBinAddr: 0x8B98, symSize: 0x38 }
  - { offset: 0xBCF38, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCACyc33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7Llfc', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0x1B4 }
  - { offset: 0xBD039, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC013latestFetchedC033_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LLSDySSypGvW', symObjAddr: 0x1EC, symBinAddr: 0x81EC, symSize: 0x1EC }
  - { offset: 0xBD0FC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC05fetchC0yyySDySSypGSgcFyAA0bC13FetchResponseO_AFtcfU_', symObjAddr: 0x3D8, symBinAddr: 0x83D8, symSize: 0x84 }
  - { offset: 0xBD198, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCfD', symObjAddr: 0x45C, symBinAddr: 0x845C, symSize: 0x2C }
  - { offset: 0xBD29E, size: 0x8, addend: 0x0, symName: '_$sSo21CLAuthorizationStatusVSYSCSY8rawValue03RawD0QzvgTW', symObjAddr: 0x5E8, symBinAddr: 0x85E0, symSize: 0xC }
  - { offset: 0xBD31E, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCsACP8rawValuex03RawG0Qz_tcfCTW', symObjAddr: 0x608, symBinAddr: 0x8600, symSize: 0xC }
  - { offset: 0xBD346, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVSYSCSY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x7A0, symBinAddr: 0x8798, symSize: 0x10 }
  - { offset: 0xBD368, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSYSCSY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0xAD0, symBinAddr: 0x8AA8, symSize: 0x44 }
  - { offset: 0xBD391, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSYSCSY8rawValue03RawG0QzvgTW', symObjAddr: 0xB14, symBinAddr: 0x8AEC, symSize: 0x28 }
  - { offset: 0xBD530, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LL_WZ', symObjAddr: 0x0, symBinAddr: 0x8DFC, symSize: 0x38 }
  - { offset: 0xBD554, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LLACvpZ', symObjAddr: 0x8D0, symBinAddr: 0xA2168, symSize: 0x0 }
  - { offset: 0xBD622, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LL_WZ', symObjAddr: 0x0, symBinAddr: 0x8DFC, symSize: 0x38 }
  - { offset: 0xBD669, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIeghggg_So6NSDataCSgAGSo7NSErrorCSgIeyBhyyy_TR', symObjAddr: 0x2D4, symBinAddr: 0x90D0, symSize: 0xC8 }
  - { offset: 0xBD681, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsCMa', symObjAddr: 0x3C0, symBinAddr: 0x91BC, symSize: 0x20 }
  - { offset: 0xBD6A6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFyAKSg_ANs5Error_pSgtYbcfU_TA', symObjAddr: 0x774, symBinAddr: 0x9570, symSize: 0x90 }
  - { offset: 0xBD6BA, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x804, symBinAddr: 0x9600, symSize: 0x10 }
  - { offset: 0xBD6CE, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x814, symBinAddr: 0x9610, symSize: 0x8 }
  - { offset: 0xBD6E2, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x81C, symBinAddr: 0x9618, symSize: 0x14 }
  - { offset: 0xBD6F6, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x830, symBinAddr: 0x962C, symSize: 0x44 }
  - { offset: 0xBD70A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x874, symBinAddr: 0x9670, symSize: 0x44 }
  - { offset: 0xBD82B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFyAKSg_ANs5Error_pSgtYbcfU_', symObjAddr: 0x38, symBinAddr: 0x8E34, symSize: 0x29C }
  - { offset: 0xBD922, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsCfD', symObjAddr: 0x39C, symBinAddr: 0x9198, symSize: 0x24 }
  - { offset: 0xBD9F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC14sharedInstanceACyFZTf4d_g', symObjAddr: 0x3E0, symBinAddr: 0x91DC, symSize: 0x128 }
  - { offset: 0xBDA6E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFTf4nnnd_n', symObjAddr: 0x508, symBinAddr: 0x9304, symSize: 0x1D8 }
  - { offset: 0xBDC74, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11isSetupDone33_97E3ACE2739C3B14C03A312B86CD9A70LLSbvpZ', symObjAddr: 0x1B70, symBinAddr: 0xA2240, symSize: 0x0 }
  - { offset: 0xBDC8E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC14sharedInstance33_97E3ACE2739C3B14C03A312B86CD9A70LLACvpZ', symObjAddr: 0x1B78, symBinAddr: 0xA2248, symSize: 0x0 }
  - { offset: 0xBDE0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19networkConnectivityAA08InternetE0OvpZ', symObjAddr: 0x1B80, symBinAddr: 0xA2250, symSize: 0x0 }
  - { offset: 0xBDE27, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC14sharedInstance33_97E3ACE2739C3B14C03A312B86CD9A70LL_WZ', symObjAddr: 0x0, symBinAddr: 0x96B4, symSize: 0x38 }
  - { offset: 0xBE173, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCMa', symObjAddr: 0x1338, symBinAddr: 0xA9EC, symSize: 0x20 }
  - { offset: 0xBE187, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x1358, symBinAddr: 0xAA0C, symSize: 0x10 }
  - { offset: 0xBE1F3, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x1A24, symBinAddr: 0xB098, symSize: 0x24 }
  - { offset: 0xBE207, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVWOr', symObjAddr: 0x1A48, symBinAddr: 0xB0BC, symSize: 0x44 }
  - { offset: 0xBE21B, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x1AEC, symBinAddr: 0xB120, symSize: 0x3C }
  - { offset: 0xBE4A1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCACyc33_97E3ACE2739C3B14C03A312B86CD9A70Llfc', symObjAddr: 0x38, symBinAddr: 0x96EC, symSize: 0x10C }
  - { offset: 0xBE574, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19resetPaymentSessionyyF', symObjAddr: 0x144, symBinAddr: 0x97F8, symSize: 0x140 }
  - { offset: 0xBE601, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC10trackEvent_16havingProperties02cxG0yAA0bE0O_SDys11AnyHashableVypGAKSgtF', symObjAddr: 0x284, symBinAddr: 0x9938, symSize: 0x140 }
  - { offset: 0xBE6F5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11addProperty9havingKey03andE0ySS_AA0bE0CtF', symObjAddr: 0x3C4, symBinAddr: 0x9A78, symSize: 0xCC }
  - { offset: 0xBE788, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11reportError_11havingLevel16andCustomMessage11errorSourceySo7NSErrorCSg_AA0aeG0OS2SSgtF', symObjAddr: 0x490, symBinAddr: 0x9B44, symSize: 0x8C0 }
  - { offset: 0xBEBA6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC34removeReachabilityNotifierObserveryyF', symObjAddr: 0xD50, symBinAddr: 0xA404, symSize: 0x1B8 }
  - { offset: 0xBEC50, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC31setReachabilityNotifierObserveryyF', symObjAddr: 0xF08, symBinAddr: 0xA5BC, symSize: 0x27C }
  - { offset: 0xBED20, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19reachabilityChanged4notey10Foundation12NotificationV_tF', symObjAddr: 0x1184, symBinAddr: 0xA838, symSize: 0xF8 }
  - { offset: 0xBEDB5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19reachabilityChanged4notey10Foundation12NotificationV_tFTo', symObjAddr: 0x127C, symBinAddr: 0xA930, symSize: 0x90 }
  - { offset: 0xBEDDD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCfD', symObjAddr: 0x130C, symBinAddr: 0xA9C0, symSize: 0x2C }
  - { offset: 0xBEE5A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC21generateCXErrorObject7cxErrorSDySSypGAA0dE8Protocol_p_tFTf4en_nAA0D0V_TB5Tf4nd_n', symObjAddr: 0x13A8, symBinAddr: 0xAA1C, symSize: 0x648 }
  - { offset: 0xBF48A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0xB15C, symSize: 0xC }
  - { offset: 0xBF4A5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0xB15C, symSize: 0xC }
  - { offset: 0xBF518, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH9hashValueSivgTW', symObjAddr: 0xC, symBinAddr: 0xB168, symSize: 0x80 }
  - { offset: 0xBF5CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x8C, symBinAddr: 0xB1E8, symSize: 0x5C }
  - { offset: 0xBF63E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE8, symBinAddr: 0xB244, symSize: 0x7C }
  - { offset: 0xBF6EB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs28CustomDebugStringConvertibleAAsAHP16debugDescriptionSSvgTW', symObjAddr: 0x2FC, symBinAddr: 0xB458, symSize: 0x28 }
  - { offset: 0xBF707, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs23CustomStringConvertibleAAsAHP11descriptionSSvgTW', symObjAddr: 0x324, symBinAddr: 0xB480, symSize: 0x28 }
  - { offset: 0xBF794, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x64C, symBinAddr: 0xB7A8, symSize: 0x3C }
  - { offset: 0xBF830, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0x740, symBinAddr: 0xB890, symSize: 0x28 }
  - { offset: 0xBF84C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0x768, symBinAddr: 0xB8B8, symSize: 0x28 }
  - { offset: 0xBFC31, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCMa', symObjAddr: 0x7C8, symBinAddr: 0xB918, symSize: 0x20 }
  - { offset: 0xBFC45, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VwCP', symObjAddr: 0x8DC, symBinAddr: 0xBA2C, symSize: 0x30 }
  - { offset: 0xBFC59, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwxx', symObjAddr: 0x90C, symBinAddr: 0xBA5C, symSize: 0x28 }
  - { offset: 0xBFC6D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwcp', symObjAddr: 0x934, symBinAddr: 0xBA84, symSize: 0x3C }
  - { offset: 0xBFC81, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwca', symObjAddr: 0x970, symBinAddr: 0xBAC0, symSize: 0x6C }
  - { offset: 0xBFC95, size: 0x8, addend: 0x0, symName: ___swift_memcpy32_8, symObjAddr: 0x9DC, symBinAddr: 0xBB2C, symSize: 0xC }
  - { offset: 0xBFCA9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwta', symObjAddr: 0x9E8, symBinAddr: 0xBB38, symSize: 0x44 }
  - { offset: 0xBFCBD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwet', symObjAddr: 0xA2C, symBinAddr: 0xBB7C, symSize: 0x48 }
  - { offset: 0xBFCD1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwst', symObjAddr: 0xA74, symBinAddr: 0xBBC4, symSize: 0x40 }
  - { offset: 0xBFCE5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VMa', symObjAddr: 0xAB4, symBinAddr: 0xBC04, symSize: 0x10 }
  - { offset: 0xBFCF9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs0F3KeyAAWl', symObjAddr: 0xCE4, symBinAddr: 0xBDD0, symSize: 0x44 }
  - { offset: 0xBFD0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VAESeAAWl', symObjAddr: 0xD48, symBinAddr: 0xBE14, symSize: 0x44 }
  - { offset: 0xBFD21, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs0F3KeyAAWl', symObjAddr: 0xF28, symBinAddr: 0xBFF4, symSize: 0x44 }
  - { offset: 0xBFD35, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VAESEAAWl', symObjAddr: 0xF6C, symBinAddr: 0xC038, symSize: 0x44 }
  - { offset: 0xBFD49, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCACSEAAWl', symObjAddr: 0xFB0, symBinAddr: 0xC07C, symSize: 0x44 }
  - { offset: 0xBFD5D, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x1038, symBinAddr: 0xC0C0, symSize: 0xC }
  - { offset: 0xBFD71, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwet', symObjAddr: 0x1048, symBinAddr: 0xC0CC, symSize: 0x90 }
  - { offset: 0xBFD85, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwst', symObjAddr: 0x10D8, symBinAddr: 0xC15C, symSize: 0xBC }
  - { offset: 0xBFD99, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwug', symObjAddr: 0x1194, symBinAddr: 0xC218, symSize: 0x8 }
  - { offset: 0xBFDAD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwup', symObjAddr: 0x119C, symBinAddr: 0xC220, symSize: 0x4 }
  - { offset: 0xBFDC1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwui', symObjAddr: 0x11A0, symBinAddr: 0xC224, symSize: 0xC }
  - { offset: 0xBFDD5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOMa', symObjAddr: 0x11AC, symBinAddr: 0xC230, symSize: 0x10 }
  - { offset: 0xBFDE9, size: 0x8, addend: 0x0, symName: ___swift_memcpy0_1, symObjAddr: 0x11BC, symBinAddr: 0xC240, symSize: 0x4 }
  - { offset: 0xBFDFD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwet', symObjAddr: 0x11C0, symBinAddr: 0xC244, symSize: 0x50 }
  - { offset: 0xBFE11, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwst', symObjAddr: 0x1210, symBinAddr: 0xC294, symSize: 0x8C }
  - { offset: 0xBFE25, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwug', symObjAddr: 0x129C, symBinAddr: 0xC320, symSize: 0x8 }
  - { offset: 0xBFE39, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOMa', symObjAddr: 0x12AC, symBinAddr: 0xC328, symSize: 0x10 }
  - { offset: 0xBFE4D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASQWb', symObjAddr: 0x12BC, symBinAddr: 0xC338, symSize: 0x4 }
  - { offset: 0xBFE61, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFSQAAWl', symObjAddr: 0x12C0, symBinAddr: 0xC33C, symSize: 0x44 }
  - { offset: 0xBFE75, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASQWb', symObjAddr: 0x1304, symBinAddr: 0xC380, symSize: 0x4 }
  - { offset: 0xBFE89, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGSQAAWl', symObjAddr: 0x1308, symBinAddr: 0xC384, symSize: 0x44 }
  - { offset: 0xBFE9D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x134C, symBinAddr: 0xC3C8, symSize: 0x4 }
  - { offset: 0xBFEB1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x1350, symBinAddr: 0xC3CC, symSize: 0x44 }
  - { offset: 0xBFEC5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x1394, symBinAddr: 0xC410, symSize: 0x4 }
  - { offset: 0xBFED9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs23CustomStringConvertibleAAWl', symObjAddr: 0x1398, symBinAddr: 0xC414, symSize: 0x44 }
  - { offset: 0xBFEED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x13DC, symBinAddr: 0xC458, symSize: 0x4 }
  - { offset: 0xBFF01, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x13E0, symBinAddr: 0xC45C, symSize: 0x44 }
  - { offset: 0xBFF15, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x1424, symBinAddr: 0xC4A0, symSize: 0x4 }
  - { offset: 0xBFF29, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x1428, symBinAddr: 0xC4A4, symSize: 0x44 }
  - { offset: 0xBFF92, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSYAASY8rawValuexSg03RawI0Qz_tcfCTW', symObjAddr: 0x164, symBinAddr: 0xB2C0, symSize: 0x80 }
  - { offset: 0xBFFDF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSYAASY8rawValue03RawI0QzvgTW', symObjAddr: 0x1E4, symBinAddr: 0xB340, symSize: 0x40 }
  - { offset: 0xC0025, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP11stringValueSSvgTW', symObjAddr: 0x224, symBinAddr: 0xB380, symSize: 0x3C }
  - { offset: 0xC008A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP11stringValuexSgSS_tcfCTW', symObjAddr: 0x260, symBinAddr: 0xB3BC, symSize: 0x84 }
  - { offset: 0xC00EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP8intValueSiSgvgTW', symObjAddr: 0x2E4, symBinAddr: 0xB440, symSize: 0xC }
  - { offset: 0xC0102, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP8intValuexSgSi_tcfCTW', symObjAddr: 0x2F0, symBinAddr: 0xB44C, symSize: 0xC }
  - { offset: 0xC0116, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V6encode2toys7Encoder_p_tKF', symObjAddr: 0x34C, symBinAddr: 0xB4A8, symSize: 0x118 }
  - { offset: 0xC015A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x464, symBinAddr: 0xB5C0, symSize: 0x2C }
  - { offset: 0xC0183, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x490, symBinAddr: 0xB5EC, symSize: 0x1C }
  - { offset: 0xC01CE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10jsonStringSSSgyF', symObjAddr: 0x4AC, symBinAddr: 0xB608, symSize: 0x134 }
  - { offset: 0xC0225, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x5E0, symBinAddr: 0xB73C, symSize: 0x8 }
  - { offset: 0xC0260, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH9hashValueSivgTW', symObjAddr: 0x5E8, symBinAddr: 0xB744, symSize: 0x40 }
  - { offset: 0xC0326, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x628, symBinAddr: 0xB784, symSize: 0x24 }
  - { offset: 0xC0382, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0x688, symBinAddr: 0xB7E4, symSize: 0x14 }
  - { offset: 0xC03B5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0x69C, symBinAddr: 0xB7F8, symSize: 0x8C }
  - { offset: 0xC0417, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0x734, symBinAddr: 0xB884, symSize: 0xC }
  - { offset: 0xC0454, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCfD', symObjAddr: 0x790, symBinAddr: 0xB8E0, symSize: 0x38 }
  - { offset: 0xC0497, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC6encode2toys7Encoder_p_tKF', symObjAddr: 0x7E8, symBinAddr: 0xB938, symSize: 0xF4 }
  - { offset: 0xC04C8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC4fromACs7Decoder_p_tKcfc', symObjAddr: 0xAC4, symBinAddr: 0xBC14, symSize: 0x148 }
  - { offset: 0xC0506, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0xC0C, symBinAddr: 0xBD5C, symSize: 0x54 }
  - { offset: 0xC052F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0xC60, symBinAddr: 0xBDB0, symSize: 0x20 }
  - { offset: 0xC0543, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V4fromAEs7Decoder_p_tKcfCTf4nd_n', symObjAddr: 0xD8C, symBinAddr: 0xBE58, symSize: 0x19C }
  - { offset: 0xC07A4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCMa', symObjAddr: 0xF4, symBinAddr: 0xC5DC, symSize: 0x20 }
  - { offset: 0xC07CE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCfETo', symObjAddr: 0x1BC, symBinAddr: 0xC6A4, symSize: 0x14 }
  - { offset: 0xC07FD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24UPITurboPrefetchProtocolPAAE26prefetchAndLinkUpiAccounts27linkAccountWithUPIPinNotSet0J14ActionDelegateySb_yptF', symObjAddr: 0x1D0, symBinAddr: 0xC6B8, symSize: 0x6C }
  - { offset: 0xC0856, size: 0x8, addend: 0x0, symName: '_$s8Razorpay30UPITurboPrefetchWithUIProtocolPAAE026prefetchAndLinkUpiAccountsD2UI17completionHandleryyypSg_AFtc_tF', symObjAddr: 0x260, symBinAddr: 0xC724, symSize: 0xA8 }
  - { offset: 0xC089F, size: 0x8, addend: 0x0, symName: '_$sypSgAAIegnn_yXlSgABIeyByy_TR', symObjAddr: 0x308, symBinAddr: 0xC7CC, symSize: 0xB0 }
  - { offset: 0xC08B7, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3B8, symBinAddr: 0xC87C, symSize: 0x10 }
  - { offset: 0xC08CB, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3C8, symBinAddr: 0xC88C, symSize: 0x8 }
  - { offset: 0xC08E0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenSSvg', symObjAddr: 0x0, symBinAddr: 0xC4E8, symSize: 0x38 }
  - { offset: 0xC0935, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenACSS_tcfC', symObjAddr: 0x38, symBinAddr: 0xC520, symSize: 0x6C }
  - { offset: 0xC0974, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenACSS_tcfc', symObjAddr: 0xA4, symBinAddr: 0xC58C, symSize: 0x50 }
  - { offset: 0xC099D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfC', symObjAddr: 0x114, symBinAddr: 0xC5FC, symSize: 0x20 }
  - { offset: 0xC09B7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfc', symObjAddr: 0x134, symBinAddr: 0xC61C, symSize: 0x2C }
  - { offset: 0xC0A10, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfcTo', symObjAddr: 0x160, symBinAddr: 0xC648, symSize: 0x2C }
  - { offset: 0xC0A6F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCfD', symObjAddr: 0x18C, symBinAddr: 0xC674, symSize: 0x30 }
  - { offset: 0xC0C83, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0xC8E0, symSize: 0x14 }
  - { offset: 0xC0D6C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsCMa', symObjAddr: 0xE0, symBinAddr: 0xC9C0, symSize: 0x20 }
  - { offset: 0xC0D80, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwet', symObjAddr: 0x110, symBinAddr: 0xC9E0, symSize: 0x90 }
  - { offset: 0xC0D94, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwst', symObjAddr: 0x1A0, symBinAddr: 0xCA70, symSize: 0xBC }
  - { offset: 0xC0DA8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwui', symObjAddr: 0x268, symBinAddr: 0xCB2C, symSize: 0x8 }
  - { offset: 0xC0DBC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOMa', symObjAddr: 0x270, symBinAddr: 0xCB34, symSize: 0x10 }
  - { offset: 0xC0DD0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASQWb', symObjAddr: 0x280, symBinAddr: 0xCB44, symSize: 0x4 }
  - { offset: 0xC0DE4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOAESQAAWl', symObjAddr: 0x284, symBinAddr: 0xCB48, symSize: 0x44 }
  - { offset: 0xC0E33, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x80, symBinAddr: 0xC960, symSize: 0x40 }
  - { offset: 0xC0EBA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP7_domainSSvgTW', symObjAddr: 0xC0, symBinAddr: 0xC9A0, symSize: 0x4 }
  - { offset: 0xC0ED6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP5_codeSivgTW', symObjAddr: 0xC4, symBinAddr: 0xC9A4, symSize: 0x4 }
  - { offset: 0xC0EF2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP9_userInfoyXlSgvgTW', symObjAddr: 0xC8, symBinAddr: 0xC9A8, symSize: 0x4 }
  - { offset: 0xC0F0E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0xCC, symBinAddr: 0xC9AC, symSize: 0x4 }
  - { offset: 0xC0F5E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0xC8E0, symSize: 0x14 }
  - { offset: 0xC0FDA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH9hashValueSivgTW', symObjAddr: 0x14, symBinAddr: 0xC8F4, symSize: 0x44 }
  - { offset: 0xC10A0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x58, symBinAddr: 0xC938, symSize: 0x28 }
  - { offset: 0xC10FD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsCfD', symObjAddr: 0xD0, symBinAddr: 0xC9B0, symSize: 0x10 }
  - { offset: 0xC12C0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14sharedInstance33_CAD7F1E8D00B6203AC8D8943A778DF45LLACvpZ', symObjAddr: 0x35D8, symBinAddr: 0xA2640, symSize: 0x0 }
  - { offset: 0xC1504, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14sharedInstance33_CAD7F1E8D00B6203AC8D8943A778DF45LL_WZ', symObjAddr: 0x178, symBinAddr: 0xCD04, symSize: 0x2C }
  - { offset: 0xC175B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCfETo', symObjAddr: 0x22F8, symBinAddr: 0xEE84, symSize: 0x80 }
  - { offset: 0xC178A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCMa', symObjAddr: 0x2378, symBinAddr: 0xEF04, symSize: 0x20 }
  - { offset: 0xC179E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLCMa', symObjAddr: 0x2398, symBinAddr: 0xEF24, symSize: 0x20 }
  - { offset: 0xC17B2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLCMa', symObjAddr: 0x23B8, symBinAddr: 0xEF44, symSize: 0x20 }
  - { offset: 0xC195D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x3478, symBinAddr: 0xFF80, symSize: 0x8 }
  - { offset: 0xC1971, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3480, symBinAddr: 0xFF88, symSize: 0x10 }
  - { offset: 0xC1985, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3490, symBinAddr: 0xFF98, symSize: 0x8 }
  - { offset: 0xC19F6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUserrS0C_So08WKScriptF0CtFTo', symObjAddr: 0x0, symBinAddr: 0xCB8C, symSize: 0x68 }
  - { offset: 0xC1A27, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUsersT0C_So08WKScriptG0CtFTo', symObjAddr: 0x80, symBinAddr: 0xCC0C, symSize: 0x68 }
  - { offset: 0xC1D1E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCACyc33_CAD7F1E8D00B6203AC8D8943A778DF45Llfc', symObjAddr: 0x1A4, symBinAddr: 0xCD30, symSize: 0x33C }
  - { offset: 0xC1DD1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCACyc33_CAD7F1E8D00B6203AC8D8943A778DF45LlfcTo', symObjAddr: 0x4E0, symBinAddr: 0xD06C, symSize: 0x20 }
  - { offset: 0xC1E1C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC12setupBridges33_CAD7F1E8D00B6203AC8D8943A778DF45LLyyF', symObjAddr: 0x500, symBinAddr: 0xD08C, symSize: 0x264 }
  - { offset: 0xC1F72, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14setPaymentDatayySDys11AnyHashableVypGF', symObjAddr: 0x764, symBinAddr: 0xD2F0, symSize: 0x448 }
  - { offset: 0xC21A0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0xBAC, symBinAddr: 0xD738, symSize: 0x28C }
  - { offset: 0xC22D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14getSDKMetadata33_CAD7F1E8D00B6203AC8D8943A778DF45LLyyF', symObjAddr: 0xE38, symBinAddr: 0xD9C4, symSize: 0x1254 }
  - { offset: 0xC2A39, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC5closeyyF', symObjAddr: 0x208C, symBinAddr: 0xEC18, symSize: 0x238 }
  - { offset: 0xC2A8F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCfD', symObjAddr: 0x22C4, symBinAddr: 0xEE50, symSize: 0x34 }
  - { offset: 0xC2B25, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUserrS0C_So08WKScriptF0CtFTf4dnd_n', symObjAddr: 0x23D8, symBinAddr: 0xEF64, symSize: 0x514 }
  - { offset: 0xC2E27, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUsersT0C_So08WKScriptG0CtFTf4dnd_n', symObjAddr: 0x28EC, symBinAddr: 0xF478, symSize: 0x504 }
  - { offset: 0xC30FB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC15initWithWebView_14andMerchantKeyySo05WKWebG0C_SSSgtFZTf4nnd_n', symObjAddr: 0x2E74, symBinAddr: 0xF97C, symSize: 0x40C }
  - { offset: 0xC321E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFTf4dn_n', symObjAddr: 0x3280, symBinAddr: 0xFD88, symSize: 0x1D4 }
  - { offset: 0xC3416, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14sharedInstanceACvpZ', symObjAddr: 0x18800, symBinAddr: 0xB9AF0, symSize: 0x0 }
  - { offset: 0xC35B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C10lumberjack33_C2103DC0A7BEA85E64BF4B6378FF9B75LLAA10LumberjackCSgvpZ', symObjAddr: 0x45B0, symBinAddr: 0xA2708, symSize: 0x0 }
  - { offset: 0xC3607, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE8URIEventABvpZ', symObjAddr: 0x18808, symBinAddr: 0xB9AF8, symSize: 0x0 }
  - { offset: 0xC3615, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14sharedInstance_WZ', symObjAddr: 0x0, symBinAddr: 0xFFE4, symSize: 0x2C }
  - { offset: 0xC3A5C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CfETo', symObjAddr: 0x1BBC, symBinAddr: 0x11BA0, symSize: 0x12C }
  - { offset: 0xC3A8B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CMa', symObjAddr: 0x1CE8, symBinAddr: 0x11CCC, symSize: 0x20 }
  - { offset: 0xC3A9F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14paymentSuccess3str16dictVerificationySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x1D08, symBinAddr: 0x11CEC, symSize: 0x128 }
  - { offset: 0xC3B3B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14paymentFailure4code11description4datays5Int32VSg_SSSgSDys11AnyHashableVypGSgtF', symObjAddr: 0x1E30, symBinAddr: 0x11E14, symSize: 0x250 }
  - { offset: 0xC3D5F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFySaySo19WKWebsiteDataRecordCGYbScMYccfU_', symObjAddr: 0x2080, symBinAddr: 0x12064, symSize: 0x424 }
  - { offset: 0xC3F55, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFySaySo19WKWebsiteDataRecordCGYbScMYccfU_yAGXEfU_yyYbcfU_', symObjAddr: 0x24A4, symBinAddr: 0x12488, symSize: 0x14 }
  - { offset: 0xC3F70, size: 0x8, addend: 0x0, symName: '_$sSaySo19WKWebsiteDataRecordCGIeghg_So7NSArrayCIeyBhy_TR', symObjAddr: 0x24B8, symBinAddr: 0x1249C, symSize: 0x6C }
  - { offset: 0xC3F88, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE8URIEvent_WZ', symObjAddr: 0x2524, symBinAddr: 0x12508, symSize: 0x34 }
  - { offset: 0xC405B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctF', symObjAddr: 0x2558, symBinAddr: 0x1253C, symSize: 0x3FC }
  - { offset: 0xC427B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x2994, symBinAddr: 0x12938, symSize: 0xA7C }
  - { offset: 0xC4643, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x3410, symBinAddr: 0x133B4, symSize: 0x354 }
  - { offset: 0xC498D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x3778, symBinAddr: 0x1371C, symSize: 0x34 }
  - { offset: 0xC49A1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_TA', symObjAddr: 0x37D8, symBinAddr: 0x1377C, symSize: 0xC }
  - { offset: 0xC49B5, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0x37E4, symBinAddr: 0x13788, symSize: 0x34 }
  - { offset: 0xC49C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_yyScMYccfU_TA', symObjAddr: 0x38F8, symBinAddr: 0x13860, symSize: 0x40 }
  - { offset: 0xC4A1B, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3938, symBinAddr: 0x138A0, symSize: 0x10 }
  - { offset: 0xC4A2F, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3948, symBinAddr: 0x138B0, symSize: 0x8 }
  - { offset: 0xC4A43, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGMa', symObjAddr: 0x3990, symBinAddr: 0x138B8, symSize: 0x54 }
  - { offset: 0xC4A57, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOSgWOe', symObjAddr: 0x39E4, symBinAddr: 0x1390C, symSize: 0x18 }
  - { offset: 0xC4A6B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOe', symObjAddr: 0x39FC, symBinAddr: 0x13924, symSize: 0x74 }
  - { offset: 0xC4A7F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A14ResultProtocol_pSgXwWOh', symObjAddr: 0x3A70, symBinAddr: 0x13998, symSize: 0x24 }
  - { offset: 0xC4AD5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C10publishURI4withySS_tFTf4nd_n', symObjAddr: 0x3FF8, symBinAddr: 0x13F20, symSize: 0x1A0 }
  - { offset: 0xC4BE8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFTf4d_n', symObjAddr: 0x4198, symBinAddr: 0x140C0, symSize: 0x1CC }
  - { offset: 0xC4C07, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x439C, symBinAddr: 0x142C4, symSize: 0x44 }
  - { offset: 0xC4C1B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOy', symObjAddr: 0x43E0, symBinAddr: 0x14308, symSize: 0x74 }
  - { offset: 0xC4C2F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC05fetchC0yyySDySSypGSgcFyAA0bC13FetchResponseO_AFtcfU_TA', symObjAddr: 0x4480, symBinAddr: 0x143A8, symSize: 0xC }
  - { offset: 0xC4C43, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFyyScMYccfU0_TA', symObjAddr: 0x44B0, symBinAddr: 0x143D8, symSize: 0xC }
  - { offset: 0xC4FBB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CACyc33_C2103DC0A7BEA85E64BF4B6378FF9B75Llfc', symObjAddr: 0x2C, symBinAddr: 0x10010, symSize: 0x1FC }
  - { offset: 0xC4FF5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CACyc33_C2103DC0A7BEA85E64BF4B6378FF9B75LlfcTo', symObjAddr: 0x228, symBinAddr: 0x1020C, symSize: 0x20 }
  - { offset: 0xC5051, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14setupAnalytics33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyF', symObjAddr: 0x248, symBinAddr: 0x1022C, symSize: 0x194 }
  - { offset: 0xC52DA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtF', symObjAddr: 0x3DC, symBinAddr: 0x103C0, symSize: 0xEA0 }
  - { offset: 0xC56E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFySDySSypGSgcfU_', symObjAddr: 0x13CC, symBinAddr: 0x113B0, symSize: 0x14 }
  - { offset: 0xC5723, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFyyScMYccfU0_', symObjAddr: 0x13E0, symBinAddr: 0x113C4, symSize: 0x26C }
  - { offset: 0xC583C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C33invokeDelegateWithFailureAndClose33_C2103DC0A7BEA85E64BF4B6378FF9B75LL5errorySS_tF', symObjAddr: 0x127C, symBinAddr: 0x11260, symSize: 0x150 }
  - { offset: 0xC5998, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5closeyyF', symObjAddr: 0x164C, symBinAddr: 0x11630, symSize: 0x394 }
  - { offset: 0xC5AE2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C13initAnalytics33_C2103DC0A7BEA85E64BF4B6378FF9B75LL15withMerchantKeyySS_tF', symObjAddr: 0x19E0, symBinAddr: 0x119C4, symSize: 0x198 }
  - { offset: 0xC5B79, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CfD', symObjAddr: 0x1B78, symBinAddr: 0x11B5C, symSize: 0x44 }
  - { offset: 0xC5C88, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_11andDelegateACSS_AA0A8Protocol_ptFZTf4nnd_g', symObjAddr: 0x3A94, symBinAddr: 0x139BC, symSize: 0x198 }
  - { offset: 0xC5CFF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4DataACSS_AA0a25PaymentCompletionProtocoldH0_ptFZTf4nnd_g', symObjAddr: 0x3C2C, symBinAddr: 0x13B54, symSize: 0x114 }
  - { offset: 0xC5D7D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTf4nnnd_g', symObjAddr: 0x3D40, symBinAddr: 0x13C68, symSize: 0x11C }
  - { offset: 0xC5E19, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTf4nnnd_g', symObjAddr: 0x3E5C, symBinAddr: 0x13D84, symSize: 0x19C }
  - { offset: 0xC6063, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x143FC, symSize: 0x18 }
  - { offset: 0xC6157, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyCMa', symObjAddr: 0xE8, symBinAddr: 0x14438, symSize: 0x20 }
  - { offset: 0xC616B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOwst', symObjAddr: 0x1A8, symBinAddr: 0x14458, symSize: 0xBC }
  - { offset: 0xC617F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOMa', symObjAddr: 0x27C, symBinAddr: 0x14514, symSize: 0x10 }
  - { offset: 0xC6193, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSHAASQWb', symObjAddr: 0x28C, symBinAddr: 0x14524, symSize: 0x4 }
  - { offset: 0xC61A7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOAESQAAWl', symObjAddr: 0x290, symBinAddr: 0x14528, symSize: 0x44 }
  - { offset: 0xC61E9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x143FC, symSize: 0x18 }
  - { offset: 0xC6254, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyCfD', symObjAddr: 0xC4, symBinAddr: 0x14414, symSize: 0x24 }
  - { offset: 0xC672A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCfETo', symObjAddr: 0x754, symBinAddr: 0x14BF0, symSize: 0x60 }
  - { offset: 0xC6759, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCMa', symObjAddr: 0x7B4, symBinAddr: 0x14C50, symSize: 0x20 }
  - { offset: 0xC676D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOwet', symObjAddr: 0x7E4, symBinAddr: 0x14C70, symSize: 0x90 }
  - { offset: 0xC6781, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOwst', symObjAddr: 0x874, symBinAddr: 0x14D00, symSize: 0xBC }
  - { offset: 0xC6795, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOMa', symObjAddr: 0x944, symBinAddr: 0x14DBC, symSize: 0x10 }
  - { offset: 0xC67A9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOSHAASQWb', symObjAddr: 0x954, symBinAddr: 0x14DCC, symSize: 0x4 }
  - { offset: 0xC67BD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOACSQAAWl', symObjAddr: 0x958, symBinAddr: 0x14DD0, symSize: 0x44 }
  - { offset: 0xC688C, size: 0x8, addend: 0x0, symName: '_$sSSSg8Razorpay29ShieldInformationFetcherErrorOSgIeggy_SgWOe', symObjAddr: 0xE54, symBinAddr: 0x151D8, symSize: 0x10 }
  - { offset: 0xC68A0, size: 0x8, addend: 0x0, symName: '_$sSo10CLLocationCMa', symObjAddr: 0xE64, symBinAddr: 0x151E8, symSize: 0x3C }
  - { offset: 0xC6AB6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCACycfc', symObjAddr: 0xD0, symBinAddr: 0x1456C, symSize: 0xF0 }
  - { offset: 0xC6AFD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCACycfcTo', symObjAddr: 0x1C0, symBinAddr: 0x1465C, symSize: 0x20 }
  - { offset: 0xC6B75, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC13createPayload33_435586993CD454125951701030FBBDD3LLSSSg_AA0bcD5ErrorOSgtyF', symObjAddr: 0x1E0, symBinAddr: 0x1467C, symSize: 0x37C }
  - { offset: 0xC6D05, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC20fetchCurrentLocation33_435586993CD454125951701030FBBDD3LLAA0bcD5ErrorOSgyF', symObjAddr: 0x55C, symBinAddr: 0x149F8, symSize: 0xDC }
  - { offset: 0xC6D8B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtFTo', symObjAddr: 0x638, symBinAddr: 0x14AD4, symSize: 0x78 }
  - { offset: 0xC6DBC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_16didFailWithErrorySo010CLLocationF0C_s0J0_ptFTo', symObjAddr: 0x6B0, symBinAddr: 0x14B4C, symSize: 0x64 }
  - { offset: 0xC6DE6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCfD', symObjAddr: 0x714, symBinAddr: 0x14BB0, symSize: 0x40 }
  - { offset: 0xC6F3F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtFTf4dnn_n', symObjAddr: 0xA90, symBinAddr: 0x14E14, symSize: 0x2F8 }
  - { offset: 0xC72AA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_16didFailWithErrorySo010CLLocationF0C_s0J0_ptFTf4ddn_n', symObjAddr: 0xD88, symBinAddr: 0x1510C, symSize: 0xCC }
  - { offset: 0xC7715, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerCMa', symObjAddr: 0xC74, symBinAddr: 0x15E98, symSize: 0x20 }
  - { offset: 0xC7729, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyFyyScMYccfU_TA', symObjAddr: 0xCCC, symBinAddr: 0x15EB8, symSize: 0x8 }
  - { offset: 0xC773D, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xCD4, symBinAddr: 0x15EC0, symSize: 0x10 }
  - { offset: 0xC7751, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xCE4, symBinAddr: 0x15ED0, symSize: 0x8 }
  - { offset: 0xC78E3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC17displayController10dataSource9isTestingACSo06UIViewE0CSg_AA0A13CheckoutModelCSbtcfc', symObjAddr: 0x0, symBinAddr: 0x15224, symSize: 0x2BC }
  - { offset: 0xC7A66, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC15displayCheckoutyyF', symObjAddr: 0x2BC, symBinAddr: 0x154E0, symSize: 0x2F0 }
  - { offset: 0xC7CE8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyF', symObjAddr: 0x5AC, symBinAddr: 0x157D0, symSize: 0x1FC }
  - { offset: 0xC7D2E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyFyyScMYccfU_', symObjAddr: 0x7A8, symBinAddr: 0x159CC, symSize: 0x64 }
  - { offset: 0xC7DF3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC030dismissControllerUsingRootViewE033_5FADD501D99B794A6E8B315B74C25A57LLyyF', symObjAddr: 0x80C, symBinAddr: 0x15A30, symSize: 0x43C }
  - { offset: 0xC8059, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerCfD', symObjAddr: 0xC48, symBinAddr: 0x15E6C, symSize: 0x2C }
  - { offset: 0xC809A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC37displayCheckoutUsingDisplayController33_5FADD501D99B794A6E8B315B74C25A57LL0dH0011checkoutNavH00R2VCySo06UIViewH0C_So012UINavigationH0CAA0aeT0CtFTf4nndd_n', symObjAddr: 0xDC0, symBinAddr: 0x15ED8, symSize: 0x160 }
  - { offset: 0xC810B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC38displayCheckoutUsingRootViewController33_5FADD501D99B794A6E8B315B74C25A57LL011checkoutNavI00S2VCySo012UINavigationI0C_AA0aeU0CtFTf4ndn_n', symObjAddr: 0xF20, symBinAddr: 0x16038, symSize: 0x308 }
  - { offset: 0xC81E6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC029dismissControllerUsingDisplayE033_5FADD501D99B794A6E8B315B74C25A57LL07displayE0ySo06UIViewE0C_tFTf4nd_n', symObjAddr: 0x1228, symBinAddr: 0x16340, symSize: 0x1EC }
  - { offset: 0xC83BA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11merchantKeySSvg', symObjAddr: 0x0, symBinAddr: 0x1652C, symSize: 0x38 }
  - { offset: 0xC84D5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCMa', symObjAddr: 0x1C8, symBinAddr: 0x166D4, symSize: 0x20 }
  - { offset: 0xC84E9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCfETo', symObjAddr: 0x1E8, symBinAddr: 0x166F4, symSize: 0x4C }
  - { offset: 0xC852D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11merchantKeySSvg', symObjAddr: 0x0, symBinAddr: 0x1652C, symSize: 0x38 }
  - { offset: 0xC8552, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC04dictC11InformationSDys11AnyHashableVypGvg', symObjAddr: 0x38, symBinAddr: 0x16564, symSize: 0x10 }
  - { offset: 0xC8575, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC8delegateAA0bC18CompletionDelegate_pvg', symObjAddr: 0x48, symBinAddr: 0x16574, symSize: 0x30 }
  - { offset: 0xC859A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC14getMerchantKeySSyF', symObjAddr: 0x78, symBinAddr: 0x165A4, symSize: 0x48 }
  - { offset: 0xC85BF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC03getC8InfoDictSDys11AnyHashableVypGyF', symObjAddr: 0xC0, symBinAddr: 0x165EC, symSize: 0x20 }
  - { offset: 0xC85E2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11getDelegateAA0bc10CompletionF0_pyF', symObjAddr: 0xE0, symBinAddr: 0x1660C, symSize: 0x40 }
  - { offset: 0xC8613, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCACycfc', symObjAddr: 0x140, symBinAddr: 0x1664C, symSize: 0x2C }
  - { offset: 0xC8671, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCACycfcTo', symObjAddr: 0x16C, symBinAddr: 0x16678, symSize: 0x2C }
  - { offset: 0xC86D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCfD', symObjAddr: 0x198, symBinAddr: 0x166A4, symSize: 0x30 }
  - { offset: 0xC8864, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelVMa', symObjAddr: 0x0, symBinAddr: 0x167A0, symSize: 0x10 }
  - { offset: 0xC887C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelVMa', symObjAddr: 0x0, symBinAddr: 0x167A0, symSize: 0x10 }
  - { offset: 0xC88F7, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x5D0, symBinAddr: 0x16CCC, symSize: 0x48 }
  - { offset: 0xC898A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelV03addB04name8observer8selector6objectSbSS_yp10ObjectiveC8SelectorVypSgtF', symObjAddr: 0x10, symBinAddr: 0x167B0, symSize: 0x2AC }
  - { offset: 0xC8A1E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelV06removeB04name8observer6objectSbSS_ypypSgtF', symObjAddr: 0x2BC, symBinAddr: 0x16A5C, symSize: 0x270 }
  - { offset: 0xC8C4F, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE19reachabilityChangedABvpZ', symObjAddr: 0x9E98, symBinAddr: 0xB9B00, symSize: 0x0 }
  - { offset: 0xC8DDC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC13startNotifieryyKF', symObjAddr: 0x0, symBinAddr: 0x16D14, symSize: 0x344 }
  - { offset: 0xC8EDC, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE19reachabilityChanged_WZ', symObjAddr: 0x354, symBinAddr: 0x17058, symSize: 0x34 }
  - { offset: 0xC8F86, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyF', symObjAddr: 0x388, symBinAddr: 0x1708C, symSize: 0x358 }
  - { offset: 0xC904C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyFyyScMYccfU_', symObjAddr: 0x11C8, symBinAddr: 0x17CD8, symSize: 0x9C }
  - { offset: 0xC9141, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCMa', symObjAddr: 0xDBC, symBinAddr: 0x17A00, symSize: 0x20 }
  - { offset: 0xC9155, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOwst', symObjAddr: 0xE7C, symBinAddr: 0x17A20, symSize: 0xBC }
  - { offset: 0xC9169, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOMa', symObjAddr: 0xF4C, symBinAddr: 0x17ADC, symSize: 0x10 }
  - { offset: 0xC917D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOSHAASQWb', symObjAddr: 0xF5C, symBinAddr: 0x17AEC, symSize: 0x4 }
  - { offset: 0xC9191, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOAESQAAWl', symObjAddr: 0xF60, symBinAddr: 0x17AF0, symSize: 0x44 }
  - { offset: 0xC91A5, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0xFA4, symBinAddr: 0x17B34, symSize: 0x3C }
  - { offset: 0xC920D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay8callback12reachability5flags4infoySo24SCNetworkReachabilityRefa_So0fG5FlagsVSvSgtFTo', symObjAddr: 0x1060, symBinAddr: 0x17BB0, symSize: 0x6C }
  - { offset: 0xC9284, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC13startNotifieryyKFyyYbcfU_TA', symObjAddr: 0x10CC, symBinAddr: 0x17C1C, symSize: 0x14 }
  - { offset: 0xC929C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC13startNotifieryyKFyyYbcfU_TA', symObjAddr: 0x10CC, symBinAddr: 0x17C1C, symSize: 0x14 }
  - { offset: 0xC92B6, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x10E0, symBinAddr: 0x17C30, symSize: 0x10 }
  - { offset: 0xC92CA, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x10F0, symBinAddr: 0x17C40, symSize: 0x8 }
  - { offset: 0xC92DE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOACs0C0AAWl', symObjAddr: 0x1184, symBinAddr: 0x17C94, symSize: 0x44 }
  - { offset: 0xC9303, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCIegg_SgWOy', symObjAddr: 0x1264, symBinAddr: 0x17D74, symSize: 0x10 }
  - { offset: 0xC9317, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyFyyScMYccfU_TA', symObjAddr: 0x12A8, symBinAddr: 0x17DB8, symSize: 0xC }
  - { offset: 0xC932B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOWOy', symObjAddr: 0x12C8, symBinAddr: 0x17DC8, symSize: 0x18 }
  - { offset: 0xC933F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwxx', symObjAddr: 0x12E0, symBinAddr: 0x17DE0, symSize: 0x10 }
  - { offset: 0xC9353, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOWOe', symObjAddr: 0x12F0, symBinAddr: 0x17DF0, symSize: 0x18 }
  - { offset: 0xC9367, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwca', symObjAddr: 0x1354, symBinAddr: 0x17E50, symSize: 0x54 }
  - { offset: 0xC937B, size: 0x8, addend: 0x0, symName: ___swift_memcpy17_8, symObjAddr: 0x13A8, symBinAddr: 0x17EA4, symSize: 0x14 }
  - { offset: 0xC938F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwta', symObjAddr: 0x13BC, symBinAddr: 0x17EB8, symSize: 0x44 }
  - { offset: 0xC93A3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwet', symObjAddr: 0x1400, symBinAddr: 0x17EFC, symSize: 0x48 }
  - { offset: 0xC93B7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwst', symObjAddr: 0x1448, symBinAddr: 0x17F44, symSize: 0x44 }
  - { offset: 0xC93CB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwug', symObjAddr: 0x148C, symBinAddr: 0x17F88, symSize: 0x18 }
  - { offset: 0xC93DF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwui', symObjAddr: 0x14A8, symBinAddr: 0x17FA0, symSize: 0x18 }
  - { offset: 0xC93F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOMa', symObjAddr: 0x14C0, symBinAddr: 0x17FB8, symSize: 0x10 }
  - { offset: 0xC95A3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOs23CustomStringConvertibleAAsAFP11descriptionSSvgTW', symObjAddr: 0x7A0, symBinAddr: 0x173E4, symSize: 0x98 }
  - { offset: 0xC95D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10connectionAC10ConnectionOvg', symObjAddr: 0x838, symBinAddr: 0x1747C, symSize: 0x288 }
  - { offset: 0xC9811, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC15reachabilityRefACSo09SCNetworkbD0a_tcfc', symObjAddr: 0xAC0, symBinAddr: 0x17704, symSize: 0x260 }
  - { offset: 0xC98BC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCfD', symObjAddr: 0xD20, symBinAddr: 0x17964, symSize: 0x9C }
  - { offset: 0xC9E48, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x4E2C, symBinAddr: 0x1CDFC, symSize: 0xC4 }
  - { offset: 0xCA1FD, size: 0x8, addend: 0x0, symName: '_$sSlsE6suffix4from11SubSequenceQz5IndexQz_tFSS_Tg5Tf4ng_n', symObjAddr: 0xA640, symBinAddr: 0x224CC, symSize: 0x4C }
  - { offset: 0xCA591, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14isRetryEnabled33_C52A9CB3C4396E63C4FE51721B47A244LL2inSbSDys11AnyHashableVypG_tF', symObjAddr: 0x0, symBinAddr: 0x17FD0, symSize: 0x268 }
  - { offset: 0xCA88C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC6onLoad33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x268, symBinAddr: 0x18238, symSize: 0xB24 }
  - { offset: 0xCB049, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12onCompletion33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0xD8C, symBinAddr: 0x18D5C, symSize: 0x108 }
  - { offset: 0xCB1B4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7onFault33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0xE94, symBinAddr: 0x18E64, symSize: 0xC30 }
  - { offset: 0xCB7C8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC9onDismiss33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x1AC4, symBinAddr: 0x19A94, symSize: 0x80C }
  - { offset: 0xCBCCC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x22D0, symBinAddr: 0x1A2A0, symSize: 0x104C }
  - { offset: 0xCC409, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tFyyScMYccfU_', symObjAddr: 0x7038, symBinAddr: 0x1F008, symSize: 0x7C }
  - { offset: 0xCC46B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC9onSuccess33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x331C, symBinAddr: 0x1B2EC, symSize: 0x840 }
  - { offset: 0xCC7ED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC33modifyActivityIndicatorAppearance33_C52A9CB3C4396E63C4FE51721B47A244LL13withColorDictySDySS12CoreGraphics7CGFloatVG_tF', symObjAddr: 0x3B5C, symBinAddr: 0x1BB2C, symSize: 0x244 }
  - { offset: 0xCCA5B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tF', symObjAddr: 0x3DA0, symBinAddr: 0x1BD70, symSize: 0x354 }
  - { offset: 0xCCCA1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tFySSSg_AA29ShieldInformationFetcherErrorOSgtcfU_Tf4ndnn_n', symObjAddr: 0xA504, symBinAddr: 0x22390, symSize: 0x13C }
  - { offset: 0xCCE29, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18downloadChallanPdf33_C52A9CB3C4396E63C4FE51721B47A244LL4form11andFileNameySS_SStF', symObjAddr: 0x40F4, symBinAddr: 0x1C0C4, symSize: 0x5C0 }
  - { offset: 0xCD185, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12downloadFile33_C52A9CB3C4396E63C4FE51721B47A244LL4form03andE4Name0P9ExtensionySS_S2StF', symObjAddr: 0x46B4, symBinAddr: 0x1C684, symSize: 0x2D8 }
  - { offset: 0xCD2ED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x498C, symBinAddr: 0x1C95C, symSize: 0x438 }
  - { offset: 0xCD437, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tFyypSg_AItcfU_', symObjAddr: 0x7350, symBinAddr: 0x1F2E4, symSize: 0x81C }
  - { offset: 0xCD961, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21userContentController_10didReceiveySo06WKUsereF0C_So15WKScriptMessageCtFTo', symObjAddr: 0x4DC4, symBinAddr: 0x1CD94, symSize: 0x68 }
  - { offset: 0xCDA4D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtF', symObjAddr: 0x4EF0, symBinAddr: 0x1CEC0, symSize: 0x67C }
  - { offset: 0xCDB30, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU_', symObjAddr: 0x70B4, symBinAddr: 0x1F084, symSize: 0x90 }
  - { offset: 0xCDB86, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU2_', symObjAddr: 0x723C, symBinAddr: 0x1F20C, symSize: 0xC4 }
  - { offset: 0xCDBFF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18saveDocumentAsFile33_C52A9CB3C4396E63C4FE51721B47A244LL4name13fileExtension19base64EncodedStringySS_SS10Foundation4DataVtF', symObjAddr: 0x556C, symBinAddr: 0x1D53C, symSize: 0x484 }
  - { offset: 0xCDF5D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFTo', symObjAddr: 0x59F0, symBinAddr: 0x1D9C0, symSize: 0x68 }
  - { offset: 0xCE059, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadFormyyF', symObjAddr: 0x5A58, symBinAddr: 0x1DA28, symSize: 0x1258 }
  - { offset: 0xCEAB2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31saveAppropriateEventToAnalytics4dict16eventWithoutData0j4WithL0ySDys11AnyHashableVypG_AA0hF0OALtF', symObjAddr: 0x6CB0, symBinAddr: 0x1EC80, symSize: 0x12C }
  - { offset: 0xCEBCD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16isExternalWallet33_C52A9CB3C4396E63C4FE51721B47A244LL6walletSbSS_tF', symObjAddr: 0x6DDC, symBinAddr: 0x1EDAC, symSize: 0x25C }
  - { offset: 0xCECC8, size: 0x8, addend: 0x0, symName: '_$sSo21PHAuthorizationStatusVIegy_ABIeyBy_TR', symObjAddr: 0x7144, symBinAddr: 0x1F114, symSize: 0x3C }
  - { offset: 0xCECE0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14openURISchemes4withySS_tFySbYbScMYccfU_', symObjAddr: 0x7300, symBinAddr: 0x1F2D0, symSize: 0x14 }
  - { offset: 0xCEF03, size: 0x8, addend: 0x0, symName: '_$sSlsE5split9maxSplits25omittingEmptySubsequences14whereSeparatorSay11SubSequenceQzGSi_S2b7ElementQzKXEtKFSS_Tg5', symObjAddr: 0x7B6C, symBinAddr: 0x1FB00, symSize: 0x430 }
  - { offset: 0xCF283, size: 0x8, addend: 0x0, symName: '_$sSlsE5split9maxSplits25omittingEmptySubsequences14whereSeparatorSay11SubSequenceQzGSi_S2b7ElementQzKXEtKF17appendSubsequenceL_3endSb5IndexQz_tSlRzlFSS_Tg5', symObjAddr: 0x7F9C, symBinAddr: 0x1FF30, symSize: 0x10C }
  - { offset: 0xCF41F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17getSelectedObject4with9shortcodeSDyS2SGSgSayAGG_SStFTf4nnd_n', symObjAddr: 0x80A8, symBinAddr: 0x2003C, symSize: 0x15C }
  - { offset: 0xCF5B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12getUriScheme3for6andUrlSSSgSDyS2SG_SStFTf4nnd_n', symObjAddr: 0x8204, symBinAddr: 0x20198, symSize: 0x13C }
  - { offset: 0xCF668, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14openURISchemes4withySS_tFTf4nd_n', symObjAddr: 0x8340, symBinAddr: 0x202D4, symSize: 0x230 }
  - { offset: 0xCF794, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16callNativeIntent33_C52A9CB3C4396E63C4FE51721B47A244LL4dictySDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x8570, symBinAddr: 0x20504, symSize: 0x798 }
  - { offset: 0xCFC64, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16isRetryAvailable33_C52A9CB3C4396E63C4FE51721B47A244LL2inSDys11AnyHashableVypGSgAI_tFTf4nd_n', symObjAddr: 0x8D08, symBinAddr: 0x20C9C, symSize: 0x118 }
  - { offset: 0xCFD0E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19getAvailableUriDataSaySDyS2SGGyFTf4d_n', symObjAddr: 0x8E20, symBinAddr: 0x20DB4, symSize: 0x700 }
  - { offset: 0xD01D7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19getAvailableAppListSaySDyS2SGGyFTf4d_n', symObjAddr: 0x9520, symBinAddr: 0x214B4, symSize: 0x860 }
  - { offset: 0xD0794, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22retrieveContactDetailsSDys11AnyHashableVypGyFTf4d_n', symObjAddr: 0x9D80, symBinAddr: 0x21D14, symSize: 0x170 }
  - { offset: 0xD08B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18saveContactDetails33_C52A9CB3C4396E63C4FE51721B47A244LLyySDyS2SGFTf4nd_n', symObjAddr: 0xA044, symBinAddr: 0x21ED0, symSize: 0x368 }
  - { offset: 0xD0A0F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12sendErrorObj33_C52A9CB3C4396E63C4FE51721B47A244LL8withData07messageF0SDys11AnyHashableVypGSgAJ_AJtFTf4nnd_n', symObjAddr: 0xA3AC, symBinAddr: 0x22238, symSize: 0x158 }
  - { offset: 0xD0B5A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21userContentController_10didReceiveySo06WKUsereF0C_So15WKScriptMessageCtFTf4dnn_n', symObjAddr: 0xA68C, symBinAddr: 0x22518, symSize: 0x2518 }
  - { offset: 0xD17CF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFTf4dndn_n', symObjAddr: 0xCBA4, symBinAddr: 0x24A30, symSize: 0x434 }
  - { offset: 0xD192E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFyyScMYccfU_TA', symObjAddr: 0xD038, symBinAddr: 0x24E88, symSize: 0x28 }
  - { offset: 0xD1960, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xD060, symBinAddr: 0x24EB0, symSize: 0x10 }
  - { offset: 0xD1974, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xD070, symBinAddr: 0x24EC0, symSize: 0x8 }
  - { offset: 0xD1988, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xD188, symBinAddr: 0x24EC8, symSize: 0x3C }
  - { offset: 0xD199C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tFyyScMYccfU_TA', symObjAddr: 0xD270, symBinAddr: 0x24F58, symSize: 0x8 }
  - { offset: 0xD19B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tFySSSg_AA29ShieldInformationFetcherErrorOSgtcfU_TA', symObjAddr: 0xD2A0, symBinAddr: 0x24F88, symSize: 0x8 }
  - { offset: 0xD19D9, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_0, symObjAddr: 0xD2F4, symBinAddr: 0x24F90, symSize: 0x3C }
  - { offset: 0xD19ED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU2_TA', symObjAddr: 0xD3B0, symBinAddr: 0x25008, symSize: 0x14 }
  - { offset: 0xD1A01, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU_TA', symObjAddr: 0xD48C, symBinAddr: 0x250E4, symSize: 0x8 }
  - { offset: 0xD1A15, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tFyypSg_AItcfU_TA', symObjAddr: 0xD4C0, symBinAddr: 0x25118, symSize: 0xC }
  - { offset: 0xD241C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5frame13configurationACSo6CGRectV_So05WKWebD13ConfigurationCtcfcTo', symObjAddr: 0x0, symBinAddr: 0x25154, symSize: 0x84 }
  - { offset: 0xD24E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewCMa', symObjAddr: 0x2DC, symBinAddr: 0x25430, symSize: 0x20 }
  - { offset: 0xD24F8, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOh', symObjAddr: 0x33C, symBinAddr: 0x25450, symSize: 0x40 }
  - { offset: 0xD2590, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5frame13configurationACSo6CGRectV_So05WKWebD13ConfigurationCtcfcTo', symObjAddr: 0x0, symBinAddr: 0x25154, symSize: 0x84 }
  - { offset: 0xD262A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x84, symBinAddr: 0x251D8, symSize: 0x54 }
  - { offset: 0xD266B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC7loadUrl10withStringySS_tF', symObjAddr: 0xD8, symBinAddr: 0x2522C, symSize: 0x1C4 }
  - { offset: 0xD26CE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewCfD', symObjAddr: 0x29C, symBinAddr: 0x253F0, symSize: 0x40 }
  - { offset: 0xD2D09, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCfETo', symObjAddr: 0x147C, symBinAddr: 0x2690C, symSize: 0x94 }
  - { offset: 0xD2D38, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCMa', symObjAddr: 0x1510, symBinAddr: 0x269A0, symSize: 0x20 }
  - { offset: 0xD2D57, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOb', symObjAddr: 0x1AFC, symBinAddr: 0x26EF4, symSize: 0x48 }
  - { offset: 0xD2F9A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC17activityIndicatorSo010UIActivityE4ViewCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x25490, symSize: 0x20 }
  - { offset: 0xD2FFA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC17activityIndicatorSo010UIActivityE4ViewCSgvsTo', symObjAddr: 0x20, symBinAddr: 0x254B0, symSize: 0x14 }
  - { offset: 0xD314F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC11viewDidLoadyyF', symObjAddr: 0x34, symBinAddr: 0x254C4, symSize: 0x280 }
  - { offset: 0xD334D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC11viewDidLoadyyFTo', symObjAddr: 0x2B4, symBinAddr: 0x25744, symSize: 0x28 }
  - { offset: 0xD33A4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC13viewDidAppearyySbF', symObjAddr: 0x2DC, symBinAddr: 0x2576C, symSize: 0x2F0 }
  - { offset: 0xD3569, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC13viewDidAppearyySbFTo', symObjAddr: 0x5CC, symBinAddr: 0x25A5C, symSize: 0x30 }
  - { offset: 0xD3591, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC18addUiViewAsSubviewyyF', symObjAddr: 0x5FC, symBinAddr: 0x25A8C, symSize: 0x10C }
  - { offset: 0xD3630, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC15dismissKeyboardyyFTo', symObjAddr: 0x708, symBinAddr: 0x25B98, symSize: 0x6C }
  - { offset: 0xD3711, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC10initialize13storefrontUrl9itemsData12withDelegateySS_SSAA20MagicXResultProtocol_ptF', symObjAddr: 0x774, symBinAddr: 0x25C04, symSize: 0x15C }
  - { offset: 0xD38E6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC19addWebViewAsSubview33_08CDC63A7B054143135D8C60DBB70CFALL03webF0ySo05WKWebF0C_tF', symObjAddr: 0x8D0, symBinAddr: 0x25D60, symSize: 0x698 }
  - { offset: 0xD3B00, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_29didStartProvisionalNavigationySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0xF68, symBinAddr: 0x263F8, symSize: 0x68 }
  - { offset: 0xD3B31, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0xFD0, symBinAddr: 0x26460, symSize: 0x68 }
  - { offset: 0xD3BA3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC16handleBackActionyyypF', symObjAddr: 0x1038, symBinAddr: 0x264C8, symSize: 0x110 }
  - { offset: 0xD3C53, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC16handleBackActionyyypFTo', symObjAddr: 0x1148, symBinAddr: 0x265D8, symSize: 0x64 }
  - { offset: 0xD3C67, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x11AC, symBinAddr: 0x2663C, symSize: 0x12C }
  - { offset: 0xD3CA8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x12D8, symBinAddr: 0x26768, symSize: 0x60 }
  - { offset: 0xD3CF0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x1338, symBinAddr: 0x267C8, symSize: 0xEC }
  - { offset: 0xD3D23, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x1424, symBinAddr: 0x268B4, symSize: 0x28 }
  - { offset: 0xD3D37, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCfD', symObjAddr: 0x144C, symBinAddr: 0x268DC, symSize: 0x30 }
  - { offset: 0xD3D5A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_29didStartProvisionalNavigationySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x1588, symBinAddr: 0x269C0, symSize: 0x1DC }
  - { offset: 0xD3DFF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x1764, symBinAddr: 0x26B9C, symSize: 0x358 }
  - { offset: 0xD4035, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerCfD', symObjAddr: 0x20, symBinAddr: 0x26F3C, symSize: 0x44 }
  - { offset: 0xD4086, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerCMa', symObjAddr: 0x64, symBinAddr: 0x26F80, symSize: 0x20 }
  - { offset: 0xD409A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwxx', symObjAddr: 0xB4, symBinAddr: 0x26FA0, symSize: 0x30 }
  - { offset: 0xD40AE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwcp', symObjAddr: 0xE4, symBinAddr: 0x26FD0, symSize: 0x5C }
  - { offset: 0xD40C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwca', symObjAddr: 0x140, symBinAddr: 0x2702C, symSize: 0x94 }
  - { offset: 0xD40D6, size: 0x8, addend: 0x0, symName: ___swift_memcpy56_8, symObjAddr: 0x1D4, symBinAddr: 0x270C0, symSize: 0x1C }
  - { offset: 0xD40EA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwta', symObjAddr: 0x1F0, symBinAddr: 0x270DC, symSize: 0x5C }
  - { offset: 0xD40FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwet', symObjAddr: 0x24C, symBinAddr: 0x27138, symSize: 0x48 }
  - { offset: 0xD4112, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwst', symObjAddr: 0x294, symBinAddr: 0x27180, symSize: 0x4C }
  - { offset: 0xD4126, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVMa', symObjAddr: 0x2E0, symBinAddr: 0x271CC, symSize: 0x10 }
  - { offset: 0xD413A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocol_pSgWOh', symObjAddr: 0x2F0, symBinAddr: 0x271DC, symSize: 0x40 }
  - { offset: 0xD417E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerCfD', symObjAddr: 0x20, symBinAddr: 0x26F3C, symSize: 0x44 }
  - { offset: 0xD43C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsCMa', symObjAddr: 0x10, symBinAddr: 0x2721C, symSize: 0x20 }
  - { offset: 0xD4487, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC04doesB18ExistAtDefaultPathySbSSFZTf4nd_n', symObjAddr: 0x30, symBinAddr: 0x2723C, symSize: 0x1D4 }
  - { offset: 0xD45E6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC07writeToB13AtDefaultPath_7contentySS_SStFZTf4nnd_n', symObjAddr: 0x204, symBinAddr: 0x27410, symSize: 0x808 }
  - { offset: 0xD4926, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC08readFromB13AtDefaultPathyS2SFZTf4nd_n', symObjAddr: 0xAA8, symBinAddr: 0x27C18, symSize: 0x364 }
  - { offset: 0xD4A55, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC06removeB13AtDefaultPathyySSFZTf4nd_n', symObjAddr: 0xE0C, symBinAddr: 0x27F7C, symSize: 0x4B8 }
  - { offset: 0xD4DD5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x28434, symSize: 0x20 }
  - { offset: 0xD5004, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCfETo', symObjAddr: 0x2DC, symBinAddr: 0x28710, symSize: 0x48 }
  - { offset: 0xD5033, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCMa', symObjAddr: 0x324, symBinAddr: 0x28758, symSize: 0x20 }
  - { offset: 0xD511F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x28434, symSize: 0x20 }
  - { offset: 0xD517F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvsTo', symObjAddr: 0x20, symBinAddr: 0x28454, symSize: 0x14 }
  - { offset: 0xD51D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC17checksDetailLabelSo7UILabelCSgvgTo', symObjAddr: 0x34, symBinAddr: 0x28468, symSize: 0x20 }
  - { offset: 0xD5225, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC17checksDetailLabelSo7UILabelCSgvsTo', symObjAddr: 0x54, symBinAddr: 0x28488, symSize: 0x14 }
  - { offset: 0xD5276, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC18accessoryImageViewSo07UIImageG0CSgvgTo', symObjAddr: 0x68, symBinAddr: 0x2849C, symSize: 0x20 }
  - { offset: 0xD52D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC18accessoryImageViewSo07UIImageG0CSgvsTo', symObjAddr: 0x88, symBinAddr: 0x284BC, symSize: 0x14 }
  - { offset: 0xD5321, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC12awakeFromNibyyFTo', symObjAddr: 0x9C, symBinAddr: 0x284D0, symSize: 0x4C }
  - { offset: 0xD5399, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC11setSelected_8animatedySb_SbtFTo', symObjAddr: 0xE8, symBinAddr: 0x2851C, symSize: 0x64 }
  - { offset: 0xD53FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC5style15reuseIdentifierACSo011UITableViewD5StyleV_SSSgtcfcTo', symObjAddr: 0x14C, symBinAddr: 0x28580, symSize: 0xE0 }
  - { offset: 0xD5486, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x22C, symBinAddr: 0x28660, symSize: 0x80 }
  - { offset: 0xD54C1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCfD', symObjAddr: 0x2AC, symBinAddr: 0x286E0, symSize: 0x30 }
  - { offset: 0xD5661, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14sharedInstance020_D5B288FFF260912DDA9E11E37AC5A806BLLACvpZ', symObjAddr: 0x898, symBinAddr: 0xA2FF8, symSize: 0x0 }
  - { offset: 0xD57FD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14sharedInstance020_D5B288FFF260912DDA9E11E37AC5A806BLL_WZ', symObjAddr: 0x4, symBinAddr: 0x2877C, symSize: 0x2C }
  - { offset: 0xD58AE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCfETo', symObjAddr: 0x54C, symBinAddr: 0x28CC4, symSize: 0x4 }
  - { offset: 0xD58EA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCMa', symObjAddr: 0x7B4, symBinAddr: 0x28F2C, symSize: 0x20 }
  - { offset: 0xD58FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOAEs0D0AAWl', symObjAddr: 0x844, symBinAddr: 0x28FBC, symSize: 0x44 }
  - { offset: 0xD59C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZ', symObjAddr: 0x0, symBinAddr: 0x28778, symSize: 0x4 }
  - { offset: 0xD59F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCACyc020_D5B288FFF260912DDA9C11E37AC5A806BLlfcTo', symObjAddr: 0x30, symBinAddr: 0x287A8, symSize: 0x4C }
  - { offset: 0xD5A64, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC15initWithWebView_14andMerchantKeyySo05WKWebF0C_SSSgtFZ', symObjAddr: 0x7C, symBinAddr: 0x287F4, symSize: 0x14 }
  - { offset: 0xD5AE7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC15initWithWebView_14andMerchantKeyySo05WKWebF0C_SSSgtFZTo', symObjAddr: 0x90, symBinAddr: 0x28808, symSize: 0x78 }
  - { offset: 0xD5B3A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZTo', symObjAddr: 0x108, symBinAddr: 0x28880, symSize: 0x14 }
  - { offset: 0xD5B7B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14setPaymentDatayySDys11AnyHashableVypGF', symObjAddr: 0x11C, symBinAddr: 0x28894, symSize: 0xC8 }
  - { offset: 0xD5C04, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14setPaymentDatayySDys11AnyHashableVypGFTo', symObjAddr: 0x1E4, symBinAddr: 0x2895C, symSize: 0x11C }
  - { offset: 0xD5C56, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKF', symObjAddr: 0x300, symBinAddr: 0x28A78, symSize: 0x14 }
  - { offset: 0xD5C8C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKFTo', symObjAddr: 0x314, symBinAddr: 0x28A8C, symSize: 0x6C }
  - { offset: 0xD5CC3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC5closeyyF', symObjAddr: 0x380, symBinAddr: 0x28AF8, symSize: 0xC4 }
  - { offset: 0xD5D41, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC5closeyyFTo', symObjAddr: 0x444, symBinAddr: 0x28BBC, symSize: 0xD8 }
  - { offset: 0xD5D9A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCfD', symObjAddr: 0x51C, symBinAddr: 0x28C94, symSize: 0x30 }
  - { offset: 0xD5DD1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZTf4d_n', symObjAddr: 0x550, symBinAddr: 0x28CC8, symSize: 0x128 }
  - { offset: 0xD5E35, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKFTf4dd_n', symObjAddr: 0x678, symBinAddr: 0x28DF0, symSize: 0x13C }
  - { offset: 0xD602E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC14sharedInstanceACvpZ', symObjAddr: 0x33F8, symBinAddr: 0xB9B08, symSize: 0x0 }
  - { offset: 0xD60A1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC14sharedInstance_WZ', symObjAddr: 0x0, symBinAddr: 0x29000, symSize: 0x38 }
  - { offset: 0xD60E8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityCMa', symObjAddr: 0x50, symBinAddr: 0x29040, symSize: 0x20 }
  - { offset: 0xD60FC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwxx', symObjAddr: 0x124, symBinAddr: 0x2906C, symSize: 0x30 }
  - { offset: 0xD6110, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwcp', symObjAddr: 0x154, symBinAddr: 0x2909C, symSize: 0x5C }
  - { offset: 0xD6124, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwca', symObjAddr: 0x1B0, symBinAddr: 0x290F8, symSize: 0x94 }
  - { offset: 0xD6138, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0x244, symBinAddr: 0x2918C, symSize: 0x14 }
  - { offset: 0xD614C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwta', symObjAddr: 0x258, symBinAddr: 0x291A0, symSize: 0x64 }
  - { offset: 0xD6160, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwet', symObjAddr: 0x2BC, symBinAddr: 0x29204, symSize: 0x48 }
  - { offset: 0xD6174, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwst', symObjAddr: 0x304, symBinAddr: 0x2924C, symSize: 0x4C }
  - { offset: 0xD6188, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVMa', symObjAddr: 0x350, symBinAddr: 0x29298, symSize: 0x10 }
  - { offset: 0xD61D3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityCfd', symObjAddr: 0x38, symBinAddr: 0x29038, symSize: 0x8 }
  - { offset: 0xD6387, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15TargetConstantsCMa', symObjAddr: 0x10, symBinAddr: 0x292A8, symSize: 0x20 }
  - { offset: 0xD6535, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC14sharedInstance33_C8B8A67CD7964AACF65D90AF0C57FAABLLACvpZ', symObjAddr: 0x9540, symBinAddr: 0xA3168, symSize: 0x0 }
  - { offset: 0xD688D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11isSetupDone33_C8B8A67CD7964AACF65D90AF0C57FAABLLSbvpZ', symObjAddr: 0x9548, symBinAddr: 0xA3170, symSize: 0x0 }
  - { offset: 0xD68A8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11base62chars33_C8B8A67CD7964AACF65D90AF0C57FAABLLSaySJGvpZ', symObjAddr: 0x9550, symBinAddr: 0xA3178, symSize: 0x0 }
  - { offset: 0xD6C48, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tF', symObjAddr: 0x2B2C, symBinAddr: 0x2BDF4, symSize: 0x7A4 }
  - { offset: 0xD70FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tFy10Foundation10URLRequestV_AH4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x79C8, symBinAddr: 0x30C50, symSize: 0x14 }
  - { offset: 0xD714A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x79DC, symBinAddr: 0x30C64, symSize: 0x14 }
  - { offset: 0xD724F, size: 0x8, addend: 0x0, symName: '_$sIegh_IeyBh_TR', symObjAddr: 0x4E9C, symBinAddr: 0x2E164, symSize: 0x2C }
  - { offset: 0xD7267, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC14sharedInstance33_C8B8A67CD7964AACF65D90AF0C57FAABLL_WZ', symObjAddr: 0x4FDC, symBinAddr: 0x2E2A4, symSize: 0x38 }
  - { offset: 0xD72E8, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TR', symObjAddr: 0x706C, symBinAddr: 0x30334, symSize: 0x20 }
  - { offset: 0xD72FC, size: 0x8, addend: 0x0, symName: '_$sIeg_IyB_TR', symObjAddr: 0x708C, symBinAddr: 0x30354, symSize: 0x20 }
  - { offset: 0xD7314, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyF', symObjAddr: 0x70AC, symBinAddr: 0x30374, symSize: 0x6B8 }
  - { offset: 0xD7738, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyFy10Foundation10URLRequestV_AE4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x79A0, symBinAddr: 0x30C28, symSize: 0x14 }
  - { offset: 0xD7784, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x79B4, symBinAddr: 0x30C3C, symSize: 0x14 }
  - { offset: 0xD77D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCMa', symObjAddr: 0x787C, symBinAddr: 0x30B44, symSize: 0x20 }
  - { offset: 0xD77E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16readBatchPayload3keyypSgSS_tFAFyXEfU_TA', symObjAddr: 0x789C, symBinAddr: 0x30B64, symSize: 0x1C }
  - { offset: 0xD781A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11base62chars33_C8B8A67CD7964AACF65D90AF0C57FAABLL_WZ', symObjAddr: 0x78F8, symBinAddr: 0x30B80, symSize: 0xA8 }
  - { offset: 0xD7937, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFyp_Tg5', symObjAddr: 0x7AE0, symBinAddr: 0x30D68, symSize: 0x10C }
  - { offset: 0xD7ABB, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSs_Tg5', symObjAddr: 0x7BEC, symBinAddr: 0x30E74, symSize: 0x108 }
  - { offset: 0xD7C3F, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x7E34, symBinAddr: 0x310BC, symSize: 0xF0 }
  - { offset: 0xD7DB8, size: 0x8, addend: 0x0, symName: '_$sSBss17FixedWidthInteger14RawSignificandRpzrlE6random2in5usingxSnyxG_qd__ztSGRd__lFZSd_s27SystemRandomNumberGeneratorVTgm5', symObjAddr: 0x7F24, symBinAddr: 0x311AC, symSize: 0xA8 }
  - { offset: 0xD7EC3, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSS_Tgq5', symObjAddr: 0x7FCC, symBinAddr: 0x31254, symSize: 0xB8 }
  - { offset: 0xD7EF0, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSJ_Tgmq5', symObjAddr: 0x8084, symBinAddr: 0x3130C, symSize: 0x7C }
  - { offset: 0xD7F26, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptFyyYbcfU_TA', symObjAddr: 0x8348, symBinAddr: 0x31564, symSize: 0x10 }
  - { offset: 0xD7F3A, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x8358, symBinAddr: 0x31574, symSize: 0x10 }
  - { offset: 0xD7F4E, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x8368, symBinAddr: 0x31584, symSize: 0x8 }
  - { offset: 0xD7F62, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC5setup33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyFyyXEfU_TA', symObjAddr: 0x8810, symBinAddr: 0x31A2C, symSize: 0x20 }
  - { offset: 0xD7F81, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x8840, symBinAddr: 0x31A5C, symSize: 0x20 }
  - { offset: 0xD7FAA, size: 0x8, addend: 0x0, symName: '_$sSaySo17OS_dispatch_queueC8DispatchE10AttributesVGMa', symObjAddr: 0x889C, symBinAddr: 0x31A7C, symSize: 0x54 }
  - { offset: 0xD7FBE, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyS2S__GWOe', symObjAddr: 0x88F0, symBinAddr: 0x31AD0, symSize: 0x8 }
  - { offset: 0xD805D, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0x90A0, symBinAddr: 0x32244, symSize: 0x48 }
  - { offset: 0xD8071, size: 0x8, addend: 0x0, symName: '_$ss18ReversedCollectionVySSGMa', symObjAddr: 0x91B0, symBinAddr: 0x3228C, symSize: 0x58 }
  - { offset: 0xD8085, size: 0x8, addend: 0x0, symName: '_$sS2SSKsWl', symObjAddr: 0x9208, symBinAddr: 0x322E4, symSize: 0x44 }
  - { offset: 0xD8099, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17resetBatchPayload33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyFyyXEfU_TA', symObjAddr: 0x924C, symBinAddr: 0x32328, symSize: 0x20 }
  - { offset: 0xD80AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16createNewSessionyyFyyXEfU_TA', symObjAddr: 0x927C, symBinAddr: 0x32358, symSize: 0x20 }
  - { offset: 0xD80C1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20readFullBatchPayloadSDySSypGyFAEyXEfU_TA', symObjAddr: 0x92F0, symBinAddr: 0x32388, symSize: 0x18 }
  - { offset: 0xD81E8, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_yps11AnyHashableVypTg5', symObjAddr: 0x11D8, symBinAddr: 0x2A4A0, symSize: 0x484 }
  - { offset: 0xD8325, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_S2SypTg5', symObjAddr: 0x165C, symBinAddr: 0x2A924, symSize: 0x3E4 }
  - { offset: 0xD846A, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SDyS2SGSSypTg5', symObjAddr: 0x1A40, symBinAddr: 0x2AD08, symSize: 0x3E4 }
  - { offset: 0xD85AF, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SSSgSSypTg5', symObjAddr: 0x1E24, symBinAddr: 0x2B0EC, symSize: 0x3EC }
  - { offset: 0xD86F4, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SDys11AnyHashableVypGAEypTg5', symObjAddr: 0x2210, symBinAddr: 0x2B4D8, symSize: 0x488 }
  - { offset: 0xD881F, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SSs11AnyHashableVypTg5', symObjAddr: 0x2698, symBinAddr: 0x2B960, symSize: 0x494 }
  - { offset: 0xD8A3A, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x79F0, symBinAddr: 0x30C78, symSize: 0xF0 }
  - { offset: 0xD8B70, size: 0x8, addend: 0x0, symName: '_$sSlsE6prefixy11SubSequenceQzSiFSS_Tg5Tf4ng_n', symObjAddr: 0x8A98, symBinAddr: 0x31C78, symSize: 0x88 }
  - { offset: 0xD8D6C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17updateBasePayloadyyF', symObjAddr: 0x0, symBinAddr: 0x292C8, symSize: 0x160 }
  - { offset: 0xD8E2E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16createNewSessionyyF', symObjAddr: 0x160, symBinAddr: 0x29428, symSize: 0x67C }
  - { offset: 0xD8FE4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC10trackEvent9eventNameySS_tF', symObjAddr: 0x7DC, symBinAddr: 0x29AA4, symSize: 0x1C8 }
  - { offset: 0xD918C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC10trackEvent9eventName16havingPropertiesySS_SDySSypGtF', symObjAddr: 0x9A4, symBinAddr: 0x29C6C, symSize: 0x280 }
  - { offset: 0xD937C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18addPaymentProperty9havingKey8andValueySS_yptF', symObjAddr: 0xC24, symBinAddr: 0x29EEC, symSize: 0x1C4 }
  - { offset: 0xD9453, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16addOrderProperty9havingKey8andValueySS_yptF', symObjAddr: 0xDE8, symBinAddr: 0x2A0B0, symSize: 0x298 }
  - { offset: 0xD9560, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC9trackPage_9havingUrlySS_SStF', symObjAddr: 0x1080, symBinAddr: 0x2A348, symSize: 0x158 }
  - { offset: 0xD9778, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyF', symObjAddr: 0x32D0, symBinAddr: 0x2C598, symSize: 0x620 }
  - { offset: 0xD98EB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyFy10Foundation10URLRequestV_AE4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x7764, symBinAddr: 0x30A2C, symSize: 0x14 }
  - { offset: 0xD9933, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x7778, symBinAddr: 0x30A40, symSize: 0x14 }
  - { offset: 0xD999C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC36gatherAnalyticsDataForCustomCheckoutSDySSypGyF', symObjAddr: 0x38F0, symBinAddr: 0x2CBB8, symSize: 0x56C }
  - { offset: 0xD9B31, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC19reachabilityChangedyyAA20InternetConnectivityOF', symObjAddr: 0x3E5C, symBinAddr: 0x2D124, symSize: 0xC98 }
  - { offset: 0xDA24A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC27networkAndLocaleInformationSS_S2StSgyF', symObjAddr: 0x4AF4, symBinAddr: 0x2DDBC, symSize: 0x3A8 }
  - { offset: 0xDA388, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC23fetchBasePayloadContextSDySSypGSgyF', symObjAddr: 0x4EC8, symBinAddr: 0x2E190, symSize: 0x114 }
  - { offset: 0xDA44D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCACyc33_C8B8A67CD7964AACF65D90AF0C57FAABLlfc', symObjAddr: 0x5014, symBinAddr: 0x2E2DC, symSize: 0x29C }
  - { offset: 0xDA524, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18inflateBasePayload33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyF', symObjAddr: 0x52B0, symBinAddr: 0x2E578, symSize: 0x19CC }
  - { offset: 0xDB141, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16readBatchPayload3keyypSgSS_tFAFyXEfU_', symObjAddr: 0x6C7C, symBinAddr: 0x2FF44, symSize: 0xF0 }
  - { offset: 0xDB1AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20readFullBatchPayloadSDySSypGyFAEyXEfU_', symObjAddr: 0x6D6C, symBinAddr: 0x30034, symSize: 0x70 }
  - { offset: 0xDB1E7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptF', symObjAddr: 0x6DDC, symBinAddr: 0x300A4, symSize: 0x194 }
  - { offset: 0xDB240, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptFyyYbcfU_', symObjAddr: 0x6F70, symBinAddr: 0x30238, symSize: 0xFC }
  - { offset: 0xDB2AE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCfd', symObjAddr: 0x7808, symBinAddr: 0x30AD0, symSize: 0x54 }
  - { offset: 0xDB2D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCfD', symObjAddr: 0x785C, symBinAddr: 0x30B24, symSize: 0x20 }
  - { offset: 0xDB32B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20createBaseTrackEvent33_C8B8A67CD7964AACF65D90AF0C57FAABLLySDySSypGSSFTf4nd_n', symObjAddr: 0x8100, symBinAddr: 0x31388, symSize: 0x1A8 }
  - { offset: 0xDB496, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11getDeviceID33_C8B8A67CD7964AACF65D90AF0C57FAABLLSSyFTf4d_n', symObjAddr: 0x8370, symBinAddr: 0x3158C, symSize: 0x2C4 }
  - { offset: 0xDB536, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17getSharedInstance17havingMerchantKeyACSS_tFZTf4nd_g', symObjAddr: 0x8634, symBinAddr: 0x31850, symSize: 0x1DC }
  - { offset: 0xDB5C7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV8tobase626numberSSs5Int64V_tFZTf4nd_n', symObjAddr: 0x88F8, symBinAddr: 0x31AD8, symSize: 0x1A0 }
  - { offset: 0xDB6E9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11getUniqueIdySSSdFZTf4nd_n', symObjAddr: 0x8B20, symBinAddr: 0x31D00, symSize: 0x1E0 }
  - { offset: 0xDB87B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC30gatherAnalyticsDataForCheckoutSDySSypGyFTf4d_n', symObjAddr: 0x8D00, symBinAddr: 0x31EE0, symSize: 0x364 }
  - { offset: 0xDBC2D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC11mercahntKeySSSgvpZ', symObjAddr: 0x163B8, symBinAddr: 0xB9B10, symSize: 0x0 }
  - { offset: 0xDBEF3, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0xAF0, symBinAddr: 0x32ED8, symSize: 0x64 }
  - { offset: 0xDBF2B, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0xB54, symBinAddr: 0x32F3C, symSize: 0x30 }
  - { offset: 0xDBF58, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xB84, symBinAddr: 0x32F6C, symSize: 0x80 }
  - { offset: 0xDC00D, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0xC04, symBinAddr: 0x32FEC, symSize: 0x68 }
  - { offset: 0xDC091, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFs11AnyHashableV_ypTg5', symObjAddr: 0xC6C, symBinAddr: 0x33054, symSize: 0x78 }
  - { offset: 0xDC12B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0xCE4, symBinAddr: 0x330CC, symSize: 0x48 }
  - { offset: 0xDC1BE, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0xD2C, symBinAddr: 0x33114, symSize: 0xE0 }
  - { offset: 0xDC241, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0xE0C, symBinAddr: 0x331F4, symSize: 0xC4 }
  - { offset: 0xDC26E, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xED0, symBinAddr: 0x332B8, symSize: 0x174 }
  - { offset: 0xDC349, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ypTg5', symObjAddr: 0x1044, symBinAddr: 0x3342C, symSize: 0xC8 }
  - { offset: 0xDC3B3, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFs11AnyHashableV_ypTg5', symObjAddr: 0x110C, symBinAddr: 0x334F4, symSize: 0xBC }
  - { offset: 0xDC419, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFs11AnyHashableV_ypTg5', symObjAddr: 0x11C8, symBinAddr: 0x335B0, symSize: 0xE4 }
  - { offset: 0xDC4BF, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFs11AnyHashableV_ypTg5', symObjAddr: 0x12C0, symBinAddr: 0x336A8, symSize: 0xC8 }
  - { offset: 0xDC507, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x1484, symBinAddr: 0x3386C, symSize: 0x1F8 }
  - { offset: 0xDC5A4, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFs11AnyHashableV_ypTg5', symObjAddr: 0x167C, symBinAddr: 0x33A64, symSize: 0x1F8 }
  - { offset: 0xDC655, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0x1874, symBinAddr: 0x33C5C, symSize: 0x1C8 }
  - { offset: 0xDC713, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0x1A3C, symBinAddr: 0x33E24, symSize: 0x32C }
  - { offset: 0xDC811, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFs11AnyHashableV_ypTg5', symObjAddr: 0x1D68, symBinAddr: 0x34150, symSize: 0x328 }
  - { offset: 0xDC94B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0x2090, symBinAddr: 0x34478, symSize: 0x32C }
  - { offset: 0xDCA40, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x23BC, symBinAddr: 0x347A4, symSize: 0x1D4 }
  - { offset: 0xDCAE0, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFs11AnyHashableV_ypTg5', symObjAddr: 0x2590, symBinAddr: 0x34978, symSize: 0x1C8 }
  - { offset: 0xDCB88, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRs11AnyHashableV_ypTg5070$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_s11cD7V_ypTg5Tf3nnpf_n', symObjAddr: 0x2758, symBinAddr: 0x34B40, symSize: 0x30 }
  - { offset: 0xDCC02, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV5merge_8isUnique16uniquingKeysWithyqd__n_Sbq_q__q_tKXEtKSTRd__x_q_t7ElementRtd__lFs11AnyHashableV_yps15LazyMapSequenceVySDyAIypGAI_yptGTg5079$s8Razorpay24SwiftCompatibilityHelperC19combineDictionaries7dictOne0G3TwoSDys11jK25VypGAI_AItFZypyp_yptXEfU_Tf1nncn_n', symObjAddr: 0x2788, symBinAddr: 0x34B70, symSize: 0x288 }
  - { offset: 0xDCD91, size: 0x8, addend: 0x0, symName: '_$ss15LazyMapSequenceV8IteratorV4nextq_SgyFSDys11AnyHashableVypG_AH_yptTg5', symObjAddr: 0x2A10, symBinAddr: 0x34DF8, symSize: 0x254 }
  - { offset: 0xDD0F1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCSgSgWOe', symObjAddr: 0x3798, symBinAddr: 0x35AA0, symSize: 0x10 }
  - { offset: 0xDD105, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCMa', symObjAddr: 0x37A8, symBinAddr: 0x35AB0, symSize: 0x20 }
  - { offset: 0xDD119, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCSgSgWOy', symObjAddr: 0x3948, symBinAddr: 0x35B88, symSize: 0x10 }
  - { offset: 0xDD1F0, size: 0x8, addend: 0x0, symName: '_$sSD7merging_16uniquingKeysWithSDyxq_GACn_q_q__q_tKXEtKFs11AnyHashableV_ypTg5079$s8Razorpay24SwiftCompatibilityHelperC19combineDictionaries7dictOne0G3TwoSDys11eF25VypGAI_AItFZypyp_yptXEfU_Tf1ncn_n', symObjAddr: 0xA74, symBinAddr: 0x32E5C, symSize: 0x7C }
  - { offset: 0xDD2F4, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x2C64, symBinAddr: 0x3504C, symSize: 0x118 }
  - { offset: 0xDD42D, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x2D7C, symBinAddr: 0x35164, symSize: 0x104 }
  - { offset: 0xDD56C, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTgm5Tf4g_n', symObjAddr: 0x2E80, symBinAddr: 0x35268, symSize: 0xEC }
  - { offset: 0xDD6A5, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCs11AnyHashableV_ypTgm5Tf4g_n', symObjAddr: 0x2F78, symBinAddr: 0x35360, symSize: 0x11C }
  - { offset: 0xDD7E4, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_12CoreGraphics7CGFloatVTgm5Tf4g_n', symObjAddr: 0x3180, symBinAddr: 0x35488, symSize: 0xF4 }
  - { offset: 0xDD923, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSSgTgm5Tf4g_n', symObjAddr: 0x3280, symBinAddr: 0x35588, symSize: 0x104 }
  - { offset: 0xDDA62, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo38UIApplicationOpenExternalURLOptionsKeya_ypTgm5Tf4g_n', symObjAddr: 0x3384, symBinAddr: 0x3568C, symSize: 0x108 }
  - { offset: 0xDDBA1, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SiTgm5Tf4g_n', symObjAddr: 0x3498, symBinAddr: 0x357A0, symSize: 0xEC }
  - { offset: 0xDDCE0, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypSgTgm5Tf4g_n', symObjAddr: 0x3584, symBinAddr: 0x3588C, symSize: 0x118 }
  - { offset: 0xDDE3F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCACycfC', symObjAddr: 0x0, symBinAddr: 0x323E8, symSize: 0x38 }
  - { offset: 0xDDE72, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCACycfc', symObjAddr: 0x38, symBinAddr: 0x32420, symSize: 0x20 }
  - { offset: 0xDDEB4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC13analyticsUtilAA09AnalyticsF0CSgvg', symObjAddr: 0x58, symBinAddr: 0x32440, symSize: 0xF8 }
  - { offset: 0xDDF26, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC12getSessionIdSSSgyF', symObjAddr: 0x150, symBinAddr: 0x32538, symSize: 0xB0 }
  - { offset: 0xDDF7B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC14getMercahntKeySSSgyF', symObjAddr: 0x200, symBinAddr: 0x325E8, symSize: 0x44 }
  - { offset: 0xDDF96, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC17getHTMLLoaingPageSSyF', symObjAddr: 0x244, symBinAddr: 0x3262C, symSize: 0x2C }
  - { offset: 0xDDFB7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC16isCFBEnabledUserSbyF', symObjAddr: 0x270, symBinAddr: 0x32658, symSize: 0x18 }
  - { offset: 0xDE081, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC26getBaseAnalyticsPropertiesSDys11AnyHashableVypGyF', symObjAddr: 0x288, symBinAddr: 0x32670, symSize: 0x584 }
  - { offset: 0xDE479, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC10trackEvent9eventName7payloadySS_SDys11AnyHashableVypGtF', symObjAddr: 0x80C, symBinAddr: 0x32BF4, symSize: 0xEC }
  - { offset: 0xDE544, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC21submitAnalyticsEventsyyF', symObjAddr: 0x8F8, symBinAddr: 0x32CE0, symSize: 0x94 }
  - { offset: 0xDE59B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC27gatherAnalyticsDataCustomUISDySSypGSgyF', symObjAddr: 0x98C, symBinAddr: 0x32D74, symSize: 0xA8 }
  - { offset: 0xDE5ED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCfd', symObjAddr: 0xA34, symBinAddr: 0x32E1C, symSize: 0x1C }
  - { offset: 0xDE61E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCfD', symObjAddr: 0xA50, symBinAddr: 0x32E38, symSize: 0x24 }
  - { offset: 0xDE928, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorCMa', symObjAddr: 0x1EC, symBinAddr: 0x35D84, symSize: 0x20 }
  - { offset: 0xDE963, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorC12awakeFromNibyyFTo', symObjAddr: 0x0, symBinAddr: 0x35B98, symSize: 0xEC }
  - { offset: 0xDE9C8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0xF8, symBinAddr: 0x35C90, symSize: 0x6C }
  - { offset: 0xDE9FD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorCfD', symObjAddr: 0x1BC, symBinAddr: 0x35D54, symSize: 0x30 }
  - { offset: 0xDEB91, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x35DA4, symSize: 0x268 }
  - { offset: 0xDEC25, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x35DA4, symSize: 0x268 }
  - { offset: 0xDECFE, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZySo0A6ActionCcfU_TA', symObjAddr: 0x28C, symBinAddr: 0x36030, symSize: 0x28 }
  - { offset: 0xDED30, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2B4, symBinAddr: 0x36058, symSize: 0x10 }
  - { offset: 0xDED44, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2C4, symBinAddr: 0x36068, symSize: 0x8 }
  - { offset: 0xDEEF6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCACycfc', symObjAddr: 0x8, symBinAddr: 0x36070, symSize: 0x138 }
  - { offset: 0xDF067, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCMa', symObjAddr: 0x60C, symBinAddr: 0x36674, symSize: 0x20 }
  - { offset: 0xDF0D6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCACycfc', symObjAddr: 0x8, symBinAddr: 0x36070, symSize: 0x138 }
  - { offset: 0xDF1CB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21startReceivingUpdatesyyKF', symObjAddr: 0x140, symBinAddr: 0x361A8, symSize: 0x164 }
  - { offset: 0xDF236, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21broadcastNotification12notificationy10Foundation0E0V_tF', symObjAddr: 0x2A4, symBinAddr: 0x3630C, symSize: 0x11C }
  - { offset: 0xDF2AE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21broadcastNotification12notificationy10Foundation0E0V_tFTo', symObjAddr: 0x3C0, symBinAddr: 0x36428, symSize: 0x8C }
  - { offset: 0xDF2C7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC20stopReceivingUpdatesyyF', symObjAddr: 0x44C, symBinAddr: 0x364B4, symSize: 0x18C }
  - { offset: 0xDF35B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCfD', symObjAddr: 0x5D8, symBinAddr: 0x36640, symSize: 0x34 }
  - { offset: 0xDF50A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwxx', symObjAddr: 0xA4, symBinAddr: 0x36694, symSize: 0x38 }
  - { offset: 0xDF51E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwcp', symObjAddr: 0x150, symBinAddr: 0x366CC, symSize: 0x98 }
  - { offset: 0xDF532, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwca', symObjAddr: 0x1E8, symBinAddr: 0x36764, symSize: 0xB4 }
  - { offset: 0xDF546, size: 0x8, addend: 0x0, symName: ___swift_memcpy65_8, symObjAddr: 0x29C, symBinAddr: 0x36818, symSize: 0x24 }
  - { offset: 0xDF55A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwta', symObjAddr: 0x2C0, symBinAddr: 0x3683C, symSize: 0x64 }
  - { offset: 0xDF56E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwet', symObjAddr: 0x324, symBinAddr: 0x368A0, symSize: 0x48 }
  - { offset: 0xDF582, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwst', symObjAddr: 0x36C, symBinAddr: 0x368E8, symSize: 0x58 }
  - { offset: 0xDF596, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwug', symObjAddr: 0x3C4, symBinAddr: 0x36940, symSize: 0x8 }
  - { offset: 0xDF5AA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwui', symObjAddr: 0x3D0, symBinAddr: 0x36948, symSize: 0x8 }
  - { offset: 0xDF5BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOMa', symObjAddr: 0x3D8, symBinAddr: 0x36950, symSize: 0x10 }
  - { offset: 0xDF644, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOb', symObjAddr: 0x554, symBinAddr: 0x36ACC, symSize: 0x28 }
  - { offset: 0xDF65C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOb', symObjAddr: 0x554, symBinAddr: 0x36ACC, symSize: 0x28 }
  - { offset: 0xDF696, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeO3URLSSyF', symObjAddr: 0x3E8, symBinAddr: 0x36960, symSize: 0x16C }
  - { offset: 0xDF963, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSON_WZ', symObjAddr: 0x0, symBinAddr: 0x36AF4, symSize: 0x1F8 }
  - { offset: 0xDF987, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSONSDys11AnyHashableVypGvpZ', symObjAddr: 0x2A60, symBinAddr: 0xB9B20, symSize: 0x0 }
  - { offset: 0xDF9E2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSON_WZ', symObjAddr: 0x0, symBinAddr: 0x36AF4, symSize: 0x1F8 }
  - { offset: 0xDFD2E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelC8delegate24analyticsUtilityInstance11checkoutURL13otpelfEnabled7options9publicKey17isDataDelegateSet13timeStampOpen0stU6Millis26arrExternalPaymentEntitiesAcA0aB10VCDelegate_pSg_AA13AnalyticsUtilCSSSbSDys11AnyHashableVypGSSSbS2iSayAA06PluginyQ0_pGSgtcfc', symObjAddr: 0x0, symBinAddr: 0x36CEC, symSize: 0xE4 }
  - { offset: 0xDFDC7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelCMa', symObjAddr: 0x138, symBinAddr: 0x36E24, symSize: 0x20 }
  - { offset: 0xDFE62, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelC8delegate24analyticsUtilityInstance11checkoutURL13otpelfEnabled7options9publicKey17isDataDelegateSet13timeStampOpen0stU6Millis26arrExternalPaymentEntitiesAcA0aB10VCDelegate_pSg_AA13AnalyticsUtilCSSSbSDys11AnyHashableVypGSSSbS2iSayAA06PluginyQ0_pGSgtcfc', symObjAddr: 0x0, symBinAddr: 0x36CEC, symSize: 0xE4 }
  - { offset: 0xDFF4B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelCfD', symObjAddr: 0xE4, symBinAddr: 0x36DD0, symSize: 0x54 }
  - { offset: 0xE02FA, size: 0x8, addend: 0x0, symName: '_$ss30_dictionaryDownCastConditionalySDyq0_q1_GSgSDyxq_GSHRzSHR0_r2_lFs11AnyHashableV_ypSSypTg5', symObjAddr: 0xF30, symBinAddr: 0x37D74, symSize: 0x460 }
  - { offset: 0xE0560, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE6starts4withSbqd___tSTRd__AAQyd__ABRSlFSS_SSTg5', symObjAddr: 0x3798, symBinAddr: 0x3A5DC, symSize: 0x164 }
  - { offset: 0xE06E3, size: 0x8, addend: 0x0, symName: '_$sSo18NSLayoutConstraintC4item9attribute9relatedBy6toItemAD10multiplier8constantAByp_So0A9AttributeVSo0A8RelationVypSgAJ12CoreGraphics7CGFloatVAPtcfCTO', symObjAddr: 0x0, symBinAddr: 0x36E44, symSize: 0x164 }
  - { offset: 0xE0E6F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfETo', symObjAddr: 0x2778, symBinAddr: 0x395BC, symSize: 0xB0 }
  - { offset: 0xE0E9F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCMa', symObjAddr: 0x2B60, symBinAddr: 0x399A4, symSize: 0x20 }
  - { offset: 0xE0ED4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16sendCredResponse33_813001DEEE20F6D789A6CA33DA760B43LL4withySi_tF', symObjAddr: 0x2B80, symBinAddr: 0x399C4, symSize: 0x3CC }
  - { offset: 0xE1182, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15publishCredData33_813001DEEE20F6D789A6CA33DA760B43LLyySo14NSNotificationCF', symObjAddr: 0x2F4C, symBinAddr: 0x39D90, symSize: 0x260 }
  - { offset: 0xE1288, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15publishCredData33_813001DEEE20F6D789A6CA33DA760B43LLyySo14NSNotificationCFTo', symObjAddr: 0x31BC, symBinAddr: 0x3A000, symSize: 0x50 }
  - { offset: 0xE12A4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSF', symObjAddr: 0x320C, symBinAddr: 0x3A050, symSize: 0x58C }
  - { offset: 0xE15F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_Tf2in_n', symObjAddr: 0x38FC, symBinAddr: 0x3A740, symSize: 0xE8 }
  - { offset: 0xE163F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU_', symObjAddr: 0x39E4, symBinAddr: 0x3A828, symSize: 0x24C }
  - { offset: 0xE1797, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU0_', symObjAddr: 0x3C30, symBinAddr: 0x3AA74, symSize: 0x310 }
  - { offset: 0xE19F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17gestureRecognizer_33shouldRecognizeSimultaneouslyWithSbSo09UIGestureE0C_AGtFTo', symObjAddr: 0x3F40, symBinAddr: 0x3AD84, symSize: 0x18 }
  - { offset: 0xE1A18, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17gestureRecognizer_33shouldRecognizeSimultaneouslyWithSbSo09UIGestureE0C_AGtFTo', symObjAddr: 0x3F40, symBinAddr: 0x3AD84, symSize: 0x18 }
  - { offset: 0xE1A44, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_Tf2in_nTA', symObjAddr: 0x403C, symBinAddr: 0x3ADC8, symSize: 0xC }
  - { offset: 0xE1A58, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x4048, symBinAddr: 0x3ADD4, symSize: 0x10 }
  - { offset: 0xE1A6C, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4058, symBinAddr: 0x3ADE4, symSize: 0x8 }
  - { offset: 0xE1A80, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU_TA', symObjAddr: 0x4184, symBinAddr: 0x3AE10, symSize: 0x8 }
  - { offset: 0xE1A94, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU0_TA', symObjAddr: 0x418C, symBinAddr: 0x3AE18, symSize: 0x8 }
  - { offset: 0xE1B66, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x56C8, symBinAddr: 0x3C25C, symSize: 0x8 }
  - { offset: 0xE1B7A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_TA', symObjAddr: 0x56D4, symBinAddr: 0x3C268, symSize: 0xC }
  - { offset: 0xE1B8E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_yyScMYccfU_TA', symObjAddr: 0x5718, symBinAddr: 0x3C2AC, symSize: 0xC }
  - { offset: 0xE1BA2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFySo13UIAlertActionCcfU_TA', symObjAddr: 0x5820, symBinAddr: 0x3C2B8, symSize: 0x30 }
  - { offset: 0xE1BEE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17activityIndicatorAA014CustomActivityE0CSgvgTo', symObjAddr: 0x164, symBinAddr: 0x36FA8, symSize: 0x20 }
  - { offset: 0xE1C4E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17activityIndicatorAA014CustomActivityE0CSgvsTo', symObjAddr: 0x184, symBinAddr: 0x36FC8, symSize: 0x14 }
  - { offset: 0xE1D49, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadViewyyF', symObjAddr: 0x198, symBinAddr: 0x36FDC, symSize: 0x168 }
  - { offset: 0xE1E32, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadViewyyFTo', symObjAddr: 0x300, symBinAddr: 0x37144, symSize: 0x28 }
  - { offset: 0xE1E72, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC11viewDidLoadyyF', symObjAddr: 0x328, symBinAddr: 0x3716C, symSize: 0x14C }
  - { offset: 0xE1EEF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC11viewDidLoadyyFTo', symObjAddr: 0x474, symBinAddr: 0x372B8, symSize: 0x28 }
  - { offset: 0xE1F17, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15dismissKeyboardyyFTo', symObjAddr: 0x49C, symBinAddr: 0x372E0, symSize: 0x6C }
  - { offset: 0xE1FBE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14viewWillAppearyySbFTo', symObjAddr: 0x508, symBinAddr: 0x3734C, symSize: 0x94 }
  - { offset: 0xE2076, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13viewDidAppearyySbF', symObjAddr: 0x59C, symBinAddr: 0x373E0, symSize: 0x2B0 }
  - { offset: 0xE21E9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13viewDidAppearyySbFTo', symObjAddr: 0x84C, symBinAddr: 0x37690, symSize: 0x30 }
  - { offset: 0xE2211, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17viewWillDisappearyySbF', symObjAddr: 0x87C, symBinAddr: 0x376C0, symSize: 0xE0 }
  - { offset: 0xE2264, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17viewWillDisappearyySbFTo', symObjAddr: 0x95C, symBinAddr: 0x377A0, symSize: 0x30 }
  - { offset: 0xE227F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFTo', symObjAddr: 0x98C, symBinAddr: 0x377D0, symSize: 0x4C }
  - { offset: 0xE22D9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyF', symObjAddr: 0x9D8, symBinAddr: 0x3781C, symSize: 0x4D8 }
  - { offset: 0xE2549, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0xEB0, symBinAddr: 0x37CF4, symSize: 0x80 }
  - { offset: 0xE2594, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_', symObjAddr: 0x1390, symBinAddr: 0x381D4, symSize: 0x22C }
  - { offset: 0xE25F1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_yyScMYccfU_', symObjAddr: 0x15BC, symBinAddr: 0x38400, symSize: 0x7C }
  - { offset: 0xE2734, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC29payWithExternalPaymentManager7options9publicKey03arrfG8EntitiesySDys11AnyHashableVypG_SSSayAA06PluginG8Delegate_pGtF', symObjAddr: 0x1638, symBinAddr: 0x3847C, symSize: 0x118 }
  - { offset: 0xE28B6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13cancelPayment33_813001DEEE20F6D789A6CA33DA760B43LLyyF', symObjAddr: 0x1750, symBinAddr: 0x38594, symSize: 0x458 }
  - { offset: 0xE2AFB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19addWebViewAsSubview33_813001DEEE20F6D789A6CA33DA760B43LL03webF0ySo05WKWebF0C_tF', symObjAddr: 0x1BA8, symBinAddr: 0x389EC, symSize: 0x698 }
  - { offset: 0xE2D9F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC24sendUserAgentToAnalytics33_813001DEEE20F6D789A6CA33DA760B43LL15webViewResponse5erroryypSg_s5Error_pSgtF', symObjAddr: 0x2240, symBinAddr: 0x39084, symSize: 0x36C }
  - { offset: 0xE2F9F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12closeWebViewyyF', symObjAddr: 0x25AC, symBinAddr: 0x393F0, symSize: 0x134 }
  - { offset: 0xE3010, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfD', symObjAddr: 0x26E0, symBinAddr: 0x39524, symSize: 0x40 }
  - { offset: 0xE3042, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfDTo', symObjAddr: 0x2720, symBinAddr: 0x39564, symSize: 0x58 }
  - { offset: 0xE3076, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x2828, symBinAddr: 0x3966C, symSize: 0x178 }
  - { offset: 0xE30B7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x29A0, symBinAddr: 0x397E4, symSize: 0x60 }
  - { offset: 0xE3133, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x2A00, symBinAddr: 0x39844, symSize: 0x138 }
  - { offset: 0xE3166, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2B38, symBinAddr: 0x3997C, symSize: 0x28 }
  - { offset: 0xE3210, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15getMobileNumber33_813001DEEE20F6D789A6CA33DA760B43LL11fromPayloadSSSgSDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x41DC, symBinAddr: 0x3AE20, symSize: 0x1D8 }
  - { offset: 0xE32A3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC10getOrderId33_813001DEEE20F6D789A6CA33DA760B43LL11fromPayloadSSSgSDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x43B4, symBinAddr: 0x3AFF8, symSize: 0x118 }
  - { offset: 0xE33F4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC23proceedToInitialization_7webviewyAA0aB5ModelC_AA0aB7WebViewCtFTf4nnd_n', symObjAddr: 0x44CC, symBinAddr: 0x3B110, symSize: 0xE6C }
  - { offset: 0xE3C38, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFTf4dn_n', symObjAddr: 0x5338, symBinAddr: 0x3BF7C, symSize: 0x248 }
  - { offset: 0xE3CDF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15CredURIResponseO8rawValueAESgSS_tcfCTf4nd_n', symObjAddr: 0x5580, symBinAddr: 0x3C1C4, symSize: 0x74 }
  - { offset: 0xE3EC6, size: 0x8, addend: 0x0, symName: '_$sSo8UIDeviceC8RazorpayE9modelNameSSvgTf4d_n', symObjAddr: 0x0, symBinAddr: 0x3C310, symSize: 0x276C }
  - { offset: 0xE3F6C, size: 0x8, addend: 0x0, symName: '_$sSo8UIDeviceC8RazorpayE9modelNameSSvgTf4d_n', symObjAddr: 0x0, symBinAddr: 0x3C310, symSize: 0x276C }
  - { offset: 0xE49FB, size: 0x8, addend: 0x0, symName: '_$sSSSg5label_yp5valuetWOh', symObjAddr: 0x27F8, symBinAddr: 0x3EA8C, symSize: 0x40 }
  - { offset: 0xE4C83, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6LoggerCMa', symObjAddr: 0x10, symBinAddr: 0x3EACC, symSize: 0x20 }
  - { offset: 0xE4E31, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC16internalInstance33_E1A96831138ED3F3E0A51B461F187E33LLACvpZ', symObjAddr: 0x16F0, symBinAddr: 0xA38C8, symSize: 0x0 }
  - { offset: 0xE4EAC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC26startNSUncaughtExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFySo11NSExceptionCcfU_To', symObjAddr: 0x830, symBinAddr: 0x3F31C, symSize: 0x28 }
  - { offset: 0xE4F74, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC16internalInstance33_E1A96831138ED3F3E0A51B461F187E33LL_WZ', symObjAddr: 0x0, symBinAddr: 0x3EAEC, symSize: 0x48 }
  - { offset: 0xE5138, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterCMa', symObjAddr: 0xDF4, symBinAddr: 0x3F8E0, symSize: 0x20 }
  - { offset: 0xE514C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_TA', symObjAddr: 0xE38, symBinAddr: 0x3F924, symSize: 0x8 }
  - { offset: 0xE5160, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xE40, symBinAddr: 0x3F92C, symSize: 0x10 }
  - { offset: 0xE5174, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xE50, symBinAddr: 0x3F93C, symSize: 0x8 }
  - { offset: 0xE51F1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_yyScMYcXEfU_TA', symObjAddr: 0x1684, symBinAddr: 0x40064, symSize: 0xC }
  - { offset: 0xE5205, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x16A0, symBinAddr: 0x40080, symSize: 0x8 }
  - { offset: 0xE541D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_', symObjAddr: 0x48, symBinAddr: 0x3EB34, symSize: 0x1F4 }
  - { offset: 0xE5536, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_yyScMYcXEfU_', symObjAddr: 0x23C, symBinAddr: 0x3ED28, symSize: 0x1F8 }
  - { offset: 0xE5749, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC26startNSUncaughtExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFySo11NSExceptionCcfU_', symObjAddr: 0x434, symBinAddr: 0x3EF20, symSize: 0x3FC }
  - { offset: 0xE5A75, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyF', symObjAddr: 0x858, symBinAddr: 0x3F344, symSize: 0x28C }
  - { offset: 0xE5AE7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_', symObjAddr: 0xAE4, symBinAddr: 0x3F5D0, symSize: 0x1D0 }
  - { offset: 0xE5B87, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU_To', symObjAddr: 0xCB4, symBinAddr: 0x3F7A0, symSize: 0x28 }
  - { offset: 0xE5BA3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU_To', symObjAddr: 0xCB4, symBinAddr: 0x3F7A0, symSize: 0x28 }
  - { offset: 0xE5BED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU0_To', symObjAddr: 0xCDC, symBinAddr: 0x3F7C8, symSize: 0x24 }
  - { offset: 0xE5C09, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU0_To', symObjAddr: 0xCDC, symBinAddr: 0x3F7C8, symSize: 0x24 }
  - { offset: 0xE5C53, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU1_To', symObjAddr: 0xD00, symBinAddr: 0x3F7EC, symSize: 0x24 }
  - { offset: 0xE5C6F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU1_To', symObjAddr: 0xD00, symBinAddr: 0x3F7EC, symSize: 0x24 }
  - { offset: 0xE5CB9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU2_To', symObjAddr: 0xD24, symBinAddr: 0x3F810, symSize: 0x28 }
  - { offset: 0xE5CD5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU2_To', symObjAddr: 0xD24, symBinAddr: 0x3F810, symSize: 0x28 }
  - { offset: 0xE5D1F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU3_To', symObjAddr: 0xD4C, symBinAddr: 0x3F838, symSize: 0x24 }
  - { offset: 0xE5D3B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU3_To', symObjAddr: 0xD4C, symBinAddr: 0x3F838, symSize: 0x24 }
  - { offset: 0xE5D85, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU4_To', symObjAddr: 0xD70, symBinAddr: 0x3F85C, symSize: 0x24 }
  - { offset: 0xE5DA1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU4_To', symObjAddr: 0xD70, symBinAddr: 0x3F85C, symSize: 0x24 }
  - { offset: 0xE5DEB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU5_To', symObjAddr: 0xD94, symBinAddr: 0x3F880, symSize: 0x28 }
  - { offset: 0xE5E07, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU5_To', symObjAddr: 0xD94, symBinAddr: 0x3F880, symSize: 0x28 }
  - { offset: 0xE5E51, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU6_To', symObjAddr: 0xDBC, symBinAddr: 0x3F8A8, symSize: 0x28 }
  - { offset: 0xE5E6D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU6_To', symObjAddr: 0xDBC, symBinAddr: 0x3F8A8, symSize: 0x28 }
  - { offset: 0xE5EBF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterCfD', symObjAddr: 0xDE4, symBinAddr: 0x3F8D0, symSize: 0x10 }
  - { offset: 0xE5EE4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFTf4d_n', symObjAddr: 0xF2C, symBinAddr: 0x3F944, symSize: 0x238 }
  - { offset: 0xE5F36, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC5startyyFZTf4d_n', symObjAddr: 0x1164, symBinAddr: 0x3FB7C, symSize: 0x104 }
  - { offset: 0xE5FD4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC29handleSwiftUnhandledException33_E1A96831138ED3F3E0A51B461F187E33LL16havingSignalCode03andR4Nameys5Int32V_SStFZTf4nnd_n', symObjAddr: 0x1268, symBinAddr: 0x3FC80, symSize: 0x3C0 }
  - { offset: 0xE65B9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC0B7DefaultSDySSypGvpZ', symObjAddr: 0x11B98, symBinAddr: 0xB9B28, symSize: 0x0 }
  - { offset: 0xE6760, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC0B7Default_WZ', symObjAddr: 0x384, symBinAddr: 0x4041C, symSize: 0x177C }
  - { offset: 0xE7F57, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigCMa', symObjAddr: 0x2EBC, symBinAddr: 0x42F54, symSize: 0x20 }
  - { offset: 0xE7F8F, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x2F30, symBinAddr: 0x42F88, symSize: 0xE4 }
  - { offset: 0xE8008, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_AM4DataVSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x32C4, symBinAddr: 0x4331C, symSize: 0x34 }
  - { offset: 0xE801C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_TA', symObjAddr: 0x331C, symBinAddr: 0x43374, symSize: 0x8 }
  - { offset: 0xE8030, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOwet', symObjAddr: 0x3428, symBinAddr: 0x4337C, symSize: 0x90 }
  - { offset: 0xE8044, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOwst', symObjAddr: 0x34B8, symBinAddr: 0x4340C, symSize: 0xBC }
  - { offset: 0xE8058, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOMa', symObjAddr: 0x3588, symBinAddr: 0x434C8, symSize: 0x10 }
  - { offset: 0xE806C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASQWb', symObjAddr: 0x3598, symBinAddr: 0x434D8, symSize: 0x4 }
  - { offset: 0xE8080, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOACSQAAWl', symObjAddr: 0x359C, symBinAddr: 0x434DC, symSize: 0x44 }
  - { offset: 0xE80E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1AC, symBinAddr: 0x40244, symSize: 0x88 }
  - { offset: 0xE81A0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH9hashValueSivgTW', symObjAddr: 0x234, symBinAddr: 0x402CC, symSize: 0x60 }
  - { offset: 0xE821E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x294, symBinAddr: 0x4032C, symSize: 0x40 }
  - { offset: 0xE8270, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x2D4, symBinAddr: 0x4036C, symSize: 0x5C }
  - { offset: 0xE843F, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsEyxSgSScfCSi_Tgm5', symObjAddr: 0x2B58, symBinAddr: 0x42BF0, symSize: 0x24C }
  - { offset: 0xE8635, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x40098, symSize: 0x1AC }
  - { offset: 0xE8678, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x330, symBinAddr: 0x403C8, symSize: 0x2C }
  - { offset: 0xE86A1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x35C, symBinAddr: 0x403F4, symSize: 0x28 }
  - { offset: 0xE873D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctF', symObjAddr: 0x1B00, symBinAddr: 0x41B98, symSize: 0x284 }
  - { offset: 0xE882E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_AM4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x1D84, symBinAddr: 0x41E1C, symSize: 0xDD4 }
  - { offset: 0xE8DE1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x2DA4, symBinAddr: 0x42E3C, symSize: 0xDC }
  - { offset: 0xE8FA0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigCfD', symObjAddr: 0x2E80, symBinAddr: 0x42F18, symSize: 0x3C }
  - { offset: 0xE8FDF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x35E0, symBinAddr: 0x43520, symSize: 0x74 }
  - { offset: 0xE91AB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tF', symObjAddr: 0x2C, symBinAddr: 0x43594, symSize: 0x3B4 }
  - { offset: 0xE9239, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tFyyScMYccfU_Tf2ni_n', symObjAddr: 0x670, symBinAddr: 0x43BD8, symSize: 0x114 }
  - { offset: 0xE9367, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17paymentSuccessful7orderID16dictVerificationySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x3E0, symBinAddr: 0x43948, symSize: 0x290 }
  - { offset: 0xE954A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGSgtFTf4dnnn_n', symObjAddr: 0x784, symBinAddr: 0x43CEC, symSize: 0x360 }
  - { offset: 0xE9791, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tFyyScMYccfU_Tf2ni_nTA', symObjAddr: 0xC3C, symBinAddr: 0x44078, symSize: 0xC }
  - { offset: 0xE97A5, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xC48, symBinAddr: 0x44084, symSize: 0x10 }
  - { offset: 0xE97B9, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xC58, symBinAddr: 0x44094, symSize: 0x8 }
  - { offset: 0xE9B1E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDict_WZ', symObjAddr: 0x0, symBinAddr: 0x4409C, symSize: 0x134 }
  - { offset: 0xE9B42, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDictSDySSypGvpZ', symObjAddr: 0x2DB8, symBinAddr: 0xB9B30, symSize: 0x0 }
  - { offset: 0xE9BBE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDict_WZ', symObjAddr: 0x0, symBinAddr: 0x4409C, symSize: 0x134 }
  - { offset: 0xE9CF6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsCMa', symObjAddr: 0x144, symBinAddr: 0x441D0, symSize: 0x20 }
  - { offset: 0xE9F43, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeWrapperCMa', symObjAddr: 0x18, symBinAddr: 0x441F0, symSize: 0x20 }
  - { offset: 0xEA0CF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x44210, symSize: 0xDC }
  - { offset: 0xEA0F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigC14sharedInstanceACvpZ', symObjAddr: 0xAAB0, symBinAddr: 0xB9B38, symSize: 0x0 }
  - { offset: 0xEA284, size: 0x8, addend: 0x0, symName: '_$sSD8RazorpayE5merge5otherySDyxq_G_tFSS_ypTg5', symObjAddr: 0xDC, symBinAddr: 0x442EC, symSize: 0x3E8 }
  - { offset: 0xEA498, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigC14sharedInstance_WZ', symObjAddr: 0x69C, symBinAddr: 0x448AC, symSize: 0x44 }
  - { offset: 0xEA4DE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCfE', symObjAddr: 0x6E0, symBinAddr: 0x448F0, symSize: 0x1C }
  - { offset: 0xEA544, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCMa', symObjAddr: 0x748, symBinAddr: 0x44958, symSize: 0x20 }
  - { offset: 0xEA6A8, size: 0x8, addend: 0x0, symName: '_$sSS3key_yp5valuetSgWOb', symObjAddr: 0x1B4C, symBinAddr: 0x45CE0, symSize: 0x48 }
  - { offset: 0xEA6BC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOwet', symObjAddr: 0x1BFC, symBinAddr: 0x45D28, symSize: 0x90 }
  - { offset: 0xEA6D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOwst', symObjAddr: 0x1C8C, symBinAddr: 0x45DB8, symSize: 0xBC }
  - { offset: 0xEA6E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOMa', symObjAddr: 0x1D5C, symBinAddr: 0x45E74, symSize: 0x10 }
  - { offset: 0xEA6F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASQWb', symObjAddr: 0x1D6C, symBinAddr: 0x45E84, symSize: 0x4 }
  - { offset: 0xEA70C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOACSQAAWl', symObjAddr: 0x1D70, symBinAddr: 0x45E88, symSize: 0x44 }
  - { offset: 0xEA7A2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4C4, symBinAddr: 0x446D4, symSize: 0x88 }
  - { offset: 0xEA85D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH9hashValueSivgTW', symObjAddr: 0x54C, symBinAddr: 0x4475C, symSize: 0x60 }
  - { offset: 0xEA8DB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x5AC, symBinAddr: 0x447BC, symSize: 0x40 }
  - { offset: 0xEA92D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x5EC, symBinAddr: 0x447FC, symSize: 0x5C }
  - { offset: 0xEAAC1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x44210, symSize: 0xDC }
  - { offset: 0xEAB17, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x648, symBinAddr: 0x44858, symSize: 0x2C }
  - { offset: 0xEAB40, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x674, symBinAddr: 0x44884, symSize: 0x28 }
  - { offset: 0xEABAB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCfD', symObjAddr: 0x6FC, symBinAddr: 0x4490C, symSize: 0x4C }
  - { offset: 0xEAC50, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCACyc33_DF8BC766DBD29A885663B5F7C39C22B4LlfcTf4g_n', symObjAddr: 0x768, symBinAddr: 0x44978, symSize: 0x1368 }
  - { offset: 0xEBA3A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x1DB4, symBinAddr: 0x45ECC, symSize: 0x74 }
  - { offset: 0xEBC19, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11merchantKey33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLLSSvpZ', symObjAddr: 0x1980, symBinAddr: 0xA3CD8, symSize: 0x0 }
  - { offset: 0xEBC33, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC14sharedInstance33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLLACvpZ', symObjAddr: 0x1990, symBinAddr: 0xA3CE8, symSize: 0x0 }
  - { offset: 0xEC0DA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC14sharedInstance33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLL_WZ', symObjAddr: 0x200, symBinAddr: 0x46140, symSize: 0x2C }
  - { offset: 0xEC2E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCfETo', symObjAddr: 0x1210, symBinAddr: 0x47118, symSize: 0x48 }
  - { offset: 0xEC32B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCMa', symObjAddr: 0x17E0, symBinAddr: 0x476E8, symSize: 0x20 }
  - { offset: 0xEC33F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFyyScMYccfU_TA', symObjAddr: 0x1874, symBinAddr: 0x4773C, symSize: 0x10 }
  - { offset: 0xEC353, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1884, symBinAddr: 0x4774C, symSize: 0x10 }
  - { offset: 0xEC367, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1894, symBinAddr: 0x4775C, symSize: 0x8 }
  - { offset: 0xEC4AB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCACyc33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLlfcTo', symObjAddr: 0x0, symBinAddr: 0x45F40, symSize: 0x74 }
  - { offset: 0xEC4F4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvgTo', symObjAddr: 0x74, symBinAddr: 0x45FB4, symSize: 0x48 }
  - { offset: 0xEC529, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvg', symObjAddr: 0xBC, symBinAddr: 0x45FFC, symSize: 0x48 }
  - { offset: 0xEC573, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvsTo', symObjAddr: 0x104, symBinAddr: 0x46044, symSize: 0x64 }
  - { offset: 0xEC5B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvs', symObjAddr: 0x168, symBinAddr: 0x460A8, symSize: 0x50 }
  - { offset: 0xEC5D9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvM', symObjAddr: 0x1B8, symBinAddr: 0x460F8, symSize: 0x44 }
  - { offset: 0xEC5FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvM.resume.0', symObjAddr: 0x1FC, symBinAddr: 0x4613C, symSize: 0x4 }
  - { offset: 0xEC6E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_11andDelegate17forViewControllerySS_AA0A25PaymentCompletionProtocol_pSo06UIViewJ0CtFZTo', symObjAddr: 0x230, symBinAddr: 0x4616C, symSize: 0x4 }
  - { offset: 0xEC6F9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_11andDelegateACSS_AA0A8Protocol_ptFZ', symObjAddr: 0x234, symBinAddr: 0x46170, symSize: 0x28 }
  - { offset: 0xEC70D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZ', symObjAddr: 0x270, symBinAddr: 0x461AC, symSize: 0x18 }
  - { offset: 0xEC728, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTo', symObjAddr: 0x288, symBinAddr: 0x461C4, symSize: 0x9C }
  - { offset: 0xEC752, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4DataACSS_AA0a25PaymentCompletionProtocoldH0_ptFZ', symObjAddr: 0x324, symBinAddr: 0x46260, symSize: 0x28 }
  - { offset: 0xEC766, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZ', symObjAddr: 0x3E4, symBinAddr: 0x46320, symSize: 0x18 }
  - { offset: 0xEC781, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTo', symObjAddr: 0x3FC, symBinAddr: 0x46338, symSize: 0x84 }
  - { offset: 0xEC7BB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10publishUri4withySS_tFZ', symObjAddr: 0x480, symBinAddr: 0x463BC, symSize: 0x60 }
  - { offset: 0xEC82C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10publishUri4withySS_tFZTo', symObjAddr: 0x4E0, symBinAddr: 0x4641C, symSize: 0x70 }
  - { offset: 0xEC8AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pF', symObjAddr: 0x550, symBinAddr: 0x4648C, symSize: 0x48 }
  - { offset: 0xEC92E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pFTo', symObjAddr: 0x598, symBinAddr: 0x464D4, symSize: 0x48 }
  - { offset: 0xEC946, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pFTo', symObjAddr: 0x598, symBinAddr: 0x464D4, symSize: 0x48 }
  - { offset: 0xEC9F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayControllerySDys11AnyHashableVypG_So06UIViewE0CtF', symObjAddr: 0x5E0, symBinAddr: 0x4651C, symSize: 0x7C }
  - { offset: 0xECA96, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayControllerySDys11AnyHashableVypG_So06UIViewE0CtFTo', symObjAddr: 0x65C, symBinAddr: 0x46598, symSize: 0xE8 }
  - { offset: 0xECB7C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4openyySDys11AnyHashableVypGF', symObjAddr: 0x744, symBinAddr: 0x46680, symSize: 0x80 }
  - { offset: 0xECC2F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4openyySDys11AnyHashableVypGFTo', symObjAddr: 0x7C4, symBinAddr: 0x46700, symSize: 0xD4 }
  - { offset: 0xECCCC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayController26arrExternalPaymentEntitiesySDys11AnyHashableVypG_So06UIViewE0CSayAA06PluginH8Delegate_pGtF', symObjAddr: 0x898, symBinAddr: 0x467D4, symSize: 0x80 }
  - { offset: 0xECD8B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayController26arrExternalPaymentEntitiesySDys11AnyHashableVypG_So06UIViewE0CSayAA06PluginH8Delegate_pGtFTo', symObjAddr: 0x918, symBinAddr: 0x46854, symSize: 0x110 }
  - { offset: 0xECE13, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZ', symObjAddr: 0xA28, symBinAddr: 0x46964, symSize: 0x44 }
  - { offset: 0xECE58, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZTo', symObjAddr: 0xA6C, symBinAddr: 0x469A8, symSize: 0x3C }
  - { offset: 0xECE8D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_26arrExternalPaymentEntitiesySDys11AnyHashableVypG_SayAA06PluginF8Delegate_pGtF', symObjAddr: 0xAA8, symBinAddr: 0x469E4, symSize: 0x8C }
  - { offset: 0xECF58, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_26arrExternalPaymentEntitiesySDys11AnyHashableVypG_SayAA06PluginF8Delegate_pGtFTo', symObjAddr: 0xB34, symBinAddr: 0x46A70, symSize: 0x104 }
  - { offset: 0xED001, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptF', symObjAddr: 0xC38, symBinAddr: 0x46B74, symSize: 0x4 }
  - { offset: 0xED015, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFyyScMYccfU_', symObjAddr: 0xC3C, symBinAddr: 0x46B78, symSize: 0x378 }
  - { offset: 0xED215, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFTo', symObjAddr: 0xFB4, symBinAddr: 0x46EF0, symSize: 0x9C }
  - { offset: 0xED23F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC5closeyyF', symObjAddr: 0x1050, symBinAddr: 0x46F8C, symSize: 0x4C }
  - { offset: 0xED287, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC5closeyyFTo', symObjAddr: 0x109C, symBinAddr: 0x46FD8, symSize: 0x68 }
  - { offset: 0xED2DD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC13clearUserDatayyF', symObjAddr: 0x1104, symBinAddr: 0x47040, symSize: 0x60 }
  - { offset: 0xED35D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC13clearUserDatayyFTo', symObjAddr: 0x1164, symBinAddr: 0x470A0, symSize: 0x78 }
  - { offset: 0xED3E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTf4nnnd_g', symObjAddr: 0x1258, symBinAddr: 0x47160, symSize: 0xDC }
  - { offset: 0xED452, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTf4nnnd_g', symObjAddr: 0x1408, symBinAddr: 0x47310, symSize: 0x1A0 }
  - { offset: 0xED506, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFTf4nnnd_n', symObjAddr: 0x15A8, symBinAddr: 0x474B0, symSize: 0x238 }
  - { offset: 0xEDAAE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperCMa', symObjAddr: 0x1FC, symBinAddr: 0x47950, symSize: 0x20 }
  - { offset: 0xEDB50, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_TA', symObjAddr: 0xB48, symBinAddr: 0x4829C, symSize: 0x78 }
  - { offset: 0xEDBA1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_TA', symObjAddr: 0xC04, symBinAddr: 0x48314, symSize: 0x80 }
  - { offset: 0xEDBEA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_TA', symObjAddr: 0xC84, symBinAddr: 0x48394, symSize: 0x7C }
  - { offset: 0xEDC3B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_ApRXEfU_TA', symObjAddr: 0xD00, symBinAddr: 0x48410, symSize: 0x3C }
  - { offset: 0xEDC4F, size: 0x8, addend: 0x0, symName: '_$ss5UInt8VABSzsWl', symObjAddr: 0xD80, symBinAddr: 0x4844C, symSize: 0x44 }
  - { offset: 0xEDC63, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZySPyytGXEfU_TA', symObjAddr: 0xE08, symBinAddr: 0x48490, symSize: 0x1C }
  - { offset: 0xEDC77, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGMa', symObjAddr: 0xEA4, symBinAddr: 0x484AC, symSize: 0x54 }
  - { offset: 0xEDCA7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZySPyytGXEfU_', symObjAddr: 0x0, symBinAddr: 0x47764, symSize: 0xEC }
  - { offset: 0xEDDF6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_ApRXEfU_', symObjAddr: 0xFC, symBinAddr: 0x47860, symSize: 0xE0 }
  - { offset: 0xEDF06, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZTf4nd_n', symObjAddr: 0x21C, symBinAddr: 0x47970, symSize: 0x1A0 }
  - { offset: 0xEDFF5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC22convertDataToHexString4dataSS10Foundation0E0V_tFZTf4nd_n', symObjAddr: 0x3BC, symBinAddr: 0x47B10, symSize: 0x258 }
  - { offset: 0xEE094, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC15validateKeySize33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL3keySb10Foundation4DataV_tFZTf4nd_n', symObjAddr: 0x614, symBinAddr: 0x47D68, symSize: 0x150 }
  - { offset: 0xEE107, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV5countACSi_tcfCTf4nd_n', symObjAddr: 0x774, symBinAddr: 0x47EC8, symSize: 0x9C }
  - { offset: 0xEE151, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZTf4nnnnnd_n', symObjAddr: 0x810, symBinAddr: 0x47F64, symSize: 0x308 }
  - { offset: 0xEE6B9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageV05buildbcdE0012fromWKScriptE0ACSgSo0hE0C_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x48500, symSize: 0x294 }
  - { offset: 0xEE793, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwxx', symObjAddr: 0x380, symBinAddr: 0x48794, symSize: 0x30 }
  - { offset: 0xEE7A7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwcp', symObjAddr: 0x3B0, symBinAddr: 0x487C4, symSize: 0x54 }
  - { offset: 0xEE7BB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwca', symObjAddr: 0x404, symBinAddr: 0x48818, symSize: 0x84 }
  - { offset: 0xEE7CF, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x488, symBinAddr: 0x4889C, symSize: 0x14 }
  - { offset: 0xEE7E3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwta', symObjAddr: 0x49C, symBinAddr: 0x488B0, symSize: 0x54 }
  - { offset: 0xEE7F7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwet', symObjAddr: 0x4F0, symBinAddr: 0x48904, symSize: 0x5C }
  - { offset: 0xEE80B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwst', symObjAddr: 0x54C, symBinAddr: 0x48960, symSize: 0x5C }
  - { offset: 0xEE81F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVMa', symObjAddr: 0x5A8, symBinAddr: 0x489BC, symSize: 0x10 }
  - { offset: 0xEE8A5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageV05buildbcdE0012fromWKScriptE0ACSgSo0hE0C_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x48500, symSize: 0x294 }
  - { offset: 0xEEBDE, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xA8C, symBinAddr: 0x4941C, symSize: 0x10 }
  - { offset: 0xEEBF2, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xA9C, symBinAddr: 0x4942C, symSize: 0x8 }
  - { offset: 0xEEC06, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_TA', symObjAddr: 0xAA4, symBinAddr: 0x49434, symSize: 0xC }
  - { offset: 0xEEC1A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_yycfU_TA', symObjAddr: 0xAD4, symBinAddr: 0x49464, symSize: 0x24 }
  - { offset: 0xEEC4C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_TA', symObjAddr: 0xB24, symBinAddr: 0x494B4, symSize: 0xC }
  - { offset: 0xEEC60, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_ySbcfU0_TA', symObjAddr: 0xB94, symBinAddr: 0x49524, symSize: 0x40 }
  - { offset: 0xEED69, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_', symObjAddr: 0x0, symBinAddr: 0x489CC, symSize: 0x188 }
  - { offset: 0xEEDB6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_', symObjAddr: 0x188, symBinAddr: 0x48B54, symSize: 0x188 }
  - { offset: 0xEEEA7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZTf4nnnd_n', symObjAddr: 0x34C, symBinAddr: 0x48CDC, symSize: 0x6F8 }
  - { offset: 0xEF5A7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsCMa', symObjAddr: 0x668, symBinAddr: 0x49BF0, symSize: 0x20 }
  - { offset: 0xF0069, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath03althI0011withDefaultF00k10LiveReloadF008fallBacklC0xSgAA0bC7OptionsO_ALSgAJS2btlFZSS_Tgm5', symObjAddr: 0x0, symBinAddr: 0x49598, symSize: 0x360 }
  - { offset: 0xF024E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson12ofDictionarySDySSypGSDys11AnyHashableVypG_tFZ', symObjAddr: 0x360, symBinAddr: 0x498F8, symSize: 0x4 }
  - { offset: 0xF0262, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34appropriatePaymentEntityDictionary011arrExternalE8EntitiesSDys11AnyHashableVypGSayAA06PluginE8Delegate_pGSg_tFZyAaI_pXEfU_', symObjAddr: 0x364, symBinAddr: 0x498FC, symSize: 0x2F4 }
  - { offset: 0xF03CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson7ofArraySayypGSayypSgG_tFZTf4nd_n', symObjAddr: 0x688, symBinAddr: 0x49C10, symSize: 0x5D0 }
  - { offset: 0xF0815, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson12ofDictionarySDySSypGSDys11AnyHashableVypG_tFZTf4nd_n', symObjAddr: 0xC58, symBinAddr: 0x4A1E0, symSize: 0x8D0 }
  - { offset: 0xF0BC1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC10filterInfoySDys11AnyHashableVypGAGFZTf4nd_n', symObjAddr: 0x1528, symBinAddr: 0x4AAB0, symSize: 0x74C }
  - { offset: 0xF10C7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC22isCurrentVersionLatest07currentF0SbSS_tKFZTf4nd_n', symObjAddr: 0x1D90, symBinAddr: 0x4B1FC, symSize: 0x6A4 }
  - { offset: 0xF167C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34checkAndShowUpdateSDKToastForDebug8onWindow15withMerchantKeyySo8UIWindowCSg_SStFZTf4nnd_n', symObjAddr: 0x2434, symBinAddr: 0x4B8A0, symSize: 0x424 }
  - { offset: 0xF18BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC18isMessageAuthentic7messageSbSo08WKScriptE0C_tFZTf4nd_n', symObjAddr: 0x2858, symBinAddr: 0x4BCC4, symSize: 0x46C }
  - { offset: 0xF199E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC21convertColorToUIColor9hexStringSo0G0CSgSS_tFZTf4nd_n', symObjAddr: 0x2CC4, symBinAddr: 0x4C130, symSize: 0x2D4 }
  - { offset: 0xF1B40, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34appropriatePaymentEntityDictionary011arrExternalE8EntitiesSDys11AnyHashableVypGSayAA06PluginE8Delegate_pGSg_tFZTf4nd_n', symObjAddr: 0x2F98, symBinAddr: 0x4C404, symSize: 0x294 }
  - { offset: 0xF1F3A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24SwiftCompatibilityHelperCMa', symObjAddr: 0x10, symBinAddr: 0x4C698, symSize: 0x20 }
  - { offset: 0xF20DA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOwet', symObjAddr: 0x10, symBinAddr: 0x4C6B8, symSize: 0x90 }
  - { offset: 0xF20EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOwst', symObjAddr: 0xA0, symBinAddr: 0x4C748, symSize: 0xBC }
  - { offset: 0xF2102, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOMa', symObjAddr: 0x160, symBinAddr: 0x4C804, symSize: 0x10 }
  - { offset: 0xF2116, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOwst', symObjAddr: 0x200, symBinAddr: 0x4C814, symSize: 0xBC }
  - { offset: 0xF212A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOMa', symObjAddr: 0x2D0, symBinAddr: 0x4C8D0, symSize: 0x10 }
  - { offset: 0xF2255, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASQWb', symObjAddr: 0x13E8, symBinAddr: 0x4D8E0, symSize: 0x4 }
  - { offset: 0xF2269, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOACSQAAWl', symObjAddr: 0x13EC, symBinAddr: 0x4D8E4, symSize: 0x44 }
  - { offset: 0xF227D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASQWb', symObjAddr: 0x1660, symBinAddr: 0x4DB00, symSize: 0x4 }
  - { offset: 0xF2291, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOACSQAAWl', symObjAddr: 0x1664, symBinAddr: 0x4DB04, symSize: 0x44 }
  - { offset: 0xF22C2, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF8Razorpay27CheckoutBridgeErrorResponseC0eG0V10CodingKeysO_Tg5', symObjAddr: 0xEEC, symBinAddr: 0x4D4EC, symSize: 0xA4 }
  - { offset: 0xF2346, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF8Razorpay0D10ErrorLevelO_Tg5', symObjAddr: 0x1098, symBinAddr: 0x4D590, symSize: 0x110 }
  - { offset: 0xF23E1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x11A8, symBinAddr: 0x4D6A0, symSize: 0xC }
  - { offset: 0xF2448, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH9hashValueSivgTW', symObjAddr: 0x11B4, symBinAddr: 0x4D6AC, symSize: 0x9C }
  - { offset: 0xF24FC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1250, symBinAddr: 0x4D748, symSize: 0x78 }
  - { offset: 0xF256C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x12C8, symBinAddr: 0x4D7C0, symSize: 0x98 }
  - { offset: 0xF2613, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1488, symBinAddr: 0x4D928, symSize: 0x88 }
  - { offset: 0xF26CE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH9hashValueSivgTW', symObjAddr: 0x1510, symBinAddr: 0x4D9B0, symSize: 0x60 }
  - { offset: 0xF274C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1570, symBinAddr: 0x4DA10, symSize: 0x40 }
  - { offset: 0xF279E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x15B0, symBinAddr: 0x4DA50, symSize: 0x5C }
  - { offset: 0xF2810, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventO8rawValueSSvg', symObjAddr: 0x2E0, symBinAddr: 0x4C8E0, symSize: 0xC0C }
  - { offset: 0xF2888, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x1360, symBinAddr: 0x4D858, symSize: 0x2C }
  - { offset: 0xF28B1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x138C, symBinAddr: 0x4D884, symSize: 0x5C }
  - { offset: 0xF28FF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x160C, symBinAddr: 0x4DAAC, symSize: 0x2C }
  - { offset: 0xF2928, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x1638, symBinAddr: 0x4DAD8, symSize: 0x28 }
  - { offset: 0xF2942, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x16A8, symBinAddr: 0x4DB48, symSize: 0x74 }
  - { offset: 0xF2979, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x171C, symBinAddr: 0x4DBBC, symSize: 0x470 }
  - { offset: 0xF2B85, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12OtpelfBridgeCMa', symObjAddr: 0x10, symBinAddr: 0x4E034, symSize: 0x20 }
  - { offset: 0xF2B99, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeCMa', symObjAddr: 0x30, symBinAddr: 0x4E054, symSize: 0x20 }
  - { offset: 0xF3184, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC14setValueForKey33_8B3A70AEAD4AD830A8BC74BF45B8FC5FLL10fromParams9classNameySayypG_SStFTf4nnd_n', symObjAddr: 0x50, symBinAddr: 0x4E074, symSize: 0x700 }
  - { offset: 0xF353E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC8getValue33_8B3A70AEAD4AD830A8BC74BF45B8FC5FLL10fromParams9className10andWebView0S10CallBackIdySayypG_SSSo05WKWebU0CSSSgtFTf4nnnnd_n', symObjAddr: 0x750, symBinAddr: 0x4E774, symSize: 0x918 }
  - { offset: 0xF3BBF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC15executeFunction_10withParams16havingCallBackId8sentFromySS_SayypGSgSSSgSo9WKWebViewCtFTf4nnnnd_n', symObjAddr: 0x1068, symBinAddr: 0x4F08C, symSize: 0x5EC }
  - { offset: 0xF3E74, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12OtpelfBridgeC15executeFunction_10withParams16havingCallBackId8sentFromySS_SayypGSgSSSgSo9WKWebViewCtFTf4nnddd_n', symObjAddr: 0x1654, symBinAddr: 0x4F678, symSize: 0xF54 }
  - { offset: 0xF4AAA, size: 0x8, addend: 0x0, symName: '_$sypSgs5Error_pSgIeghng_yXlSgSo7NSErrorCSgIeyBhyy_TR', symObjAddr: 0x0, symBinAddr: 0x505D0, symSize: 0x9C }
  - { offset: 0xF4AD1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_7didFail9withErrorySo05WKWebE0C_So12WKNavigationCSgs0I0_ptFTo', symObjAddr: 0xA0, symBinAddr: 0x50670, symSize: 0x88 }
  - { offset: 0xF4B12, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_28didFailProvisionalNavigation9withErrorySo05WKWebE0C_So12WKNavigationCSgs0K0_ptFTo', symObjAddr: 0x128, symBinAddr: 0x506F8, symSize: 0x8C }
  - { offset: 0xF4B4F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0x1B4, symBinAddr: 0x50784, symSize: 0x290 }
  - { offset: 0xF4C17, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0x444, symBinAddr: 0x50A14, symSize: 0x68 }
  - { offset: 0xF4C58, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_15decidePolicyFor15decisionHandlerySo05WKWebE0C_So18WKNavigationActionCySo0lmG0VctFTo', symObjAddr: 0x4AC, symBinAddr: 0x50A7C, symSize: 0x94 }
  - { offset: 0xF4C8A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo11UITextFieldCcfU_', symObjAddr: 0x694, symBinAddr: 0x50C64, symSize: 0x60 }
  - { offset: 0xF4D54, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU0_', symObjAddr: 0x744, symBinAddr: 0x50D10, symSize: 0x18C }
  - { offset: 0xF4EED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFTo', symObjAddr: 0x8D0, symBinAddr: 0x50E9C, symSize: 0x110 }
  - { offset: 0xF4F1F, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgIeyBy_SSSgIegg_TR', symObjAddr: 0x9E0, symBinAddr: 0x50FAC, symSize: 0x44 }
  - { offset: 0xF4F37, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgIeyBy_SSSgIegg_TRTA', symObjAddr: 0xA48, symBinAddr: 0x51014, symSize: 0x8 }
  - { offset: 0xF50D4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_7didFail9withErrorySo05WKWebE0C_So12WKNavigationCSgs0I0_ptFTf4ddnn_n', symObjAddr: 0xA50, symBinAddr: 0x5101C, symSize: 0x1B24 }
  - { offset: 0xF5947, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_28didFailProvisionalNavigation9withErrorySo05WKWebE0C_So12WKNavigationCSgs0K0_ptFTf4ndnn_n', symObjAddr: 0x2574, symBinAddr: 0x52B40, symSize: 0x55C }
  - { offset: 0xF5C2F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x2AD0, symBinAddr: 0x5309C, symSize: 0xA5C }
  - { offset: 0xF602D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_15decidePolicyFor15decisionHandlerySo05WKWebE0C_So18WKNavigationActionCySo0lmG0VctF06$sSo24lmG16VIeyBy_ABIegy_TRALIeyBy_Tf1nncn_nTf4dnng_n', symObjAddr: 0x352C, symBinAddr: 0x53AF8, symSize: 0x574 }
  - { offset: 0xF628F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_34runJavaScriptAlertPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCyyctFTf4dndnn_n', symObjAddr: 0x3AA0, symBinAddr: 0x5406C, symSize: 0x1A8 }
  - { offset: 0xF630F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_36runJavaScriptConfirmPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCySbctFTf4dndnn_n', symObjAddr: 0x3C48, symBinAddr: 0x54214, symSize: 0x284 }
  - { offset: 0xF639C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFTf4dnndnn_n', symObjAddr: 0x3ECC, symBinAddr: 0x54498, symSize: 0x324 }
  - { offset: 0xF6439, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo11UITextFieldCcfU_TA', symObjAddr: 0x4214, symBinAddr: 0x547E0, symSize: 0x8 }
  - { offset: 0xF644D, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x421C, symBinAddr: 0x547E8, symSize: 0x10 }
  - { offset: 0xF6461, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x422C, symBinAddr: 0x547F8, symSize: 0x8 }
  - { offset: 0xF6475, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU0_TA', symObjAddr: 0x4268, symBinAddr: 0x54834, symSize: 0x10 }
  - { offset: 0xF64AF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU1_TA', symObjAddr: 0x429C, symBinAddr: 0x54868, symSize: 0x38 }
  - { offset: 0xF64E3, size: 0x8, addend: 0x0, symName: '_$sSo11UITextFieldCMa', symObjAddr: 0x42D4, symBinAddr: 0x548A0, symSize: 0x3C }
  - { offset: 0xF6502, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x4310, symBinAddr: 0x548DC, symSize: 0x14 }
  - { offset: 0xF6536, size: 0x8, addend: 0x0, symName: '_$sIeyB_Ieg_TRTA', symObjAddr: 0x4374, symBinAddr: 0x54940, symSize: 0xC }
  - { offset: 0xF6585, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_34runJavaScriptAlertPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCyyctFySo13UIAlertActionCcfU_TA', symObjAddr: 0x4380, symBinAddr: 0x5494C, symSize: 0x30 }
  - { offset: 0xF65B9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x44A0, symBinAddr: 0x549A0, symSize: 0x8 }
  - { offset: 0xF6BEC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC14sharedInstanceACvpZ', symObjAddr: 0x2E620, symBinAddr: 0xB9B40, symSize: 0x0 }
  - { offset: 0xF6FE7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC14sharedInstance_WZ', symObjAddr: 0x2368, symBinAddr: 0x56D54, symSize: 0x48 }
  - { offset: 0xF70B5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataCMa', symObjAddr: 0x3B7C, symBinAddr: 0x58568, symSize: 0x20 }
  - { offset: 0xF70C9, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x3BDC, symBinAddr: 0x58588, symSize: 0x50 }
  - { offset: 0xF70FE, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x3C78, symBinAddr: 0x585D8, symSize: 0xBC }
  - { offset: 0xF7156, size: 0x8, addend: 0x0, symName: '_$ss32_copyCollectionToContiguousArrayys0dE0Vy7ElementQzGxSlRzlFSs8UTF8ViewV_Tgq5', symObjAddr: 0x3D34, symBinAddr: 0x58694, symSize: 0xB0 }
  - { offset: 0xF7197, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x3DE4, symBinAddr: 0x58744, symSize: 0xEC }
  - { offset: 0xF71BC, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgmq5', symObjAddr: 0x3ED0, symBinAddr: 0x58830, symSize: 0x6C }
  - { offset: 0xF71F2, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x3F3C, symBinAddr: 0x5889C, symSize: 0x210 }
  - { offset: 0xF722B, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x414C, symBinAddr: 0x58AAC, symSize: 0x78 }
  - { offset: 0xF7243, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x41F0, symBinAddr: 0x58B50, symSize: 0xC }
  - { offset: 0xF7257, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_TA', symObjAddr: 0x426C, symBinAddr: 0x58B90, symSize: 0x10 }
  - { offset: 0xF726B, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x427C, symBinAddr: 0x58BA0, symSize: 0x10 }
  - { offset: 0xF727F, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x428C, symBinAddr: 0x58BB0, symSize: 0x8 }
  - { offset: 0xF733D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_yyScMYcXEfU_TA', symObjAddr: 0x4DA0, symBinAddr: 0x59630, symSize: 0xC }
  - { offset: 0xF7351, size: 0x8, addend: 0x0, symName: '_$sS2sSysWl', symObjAddr: 0x4E08, symBinAddr: 0x5964C, symSize: 0x44 }
  - { offset: 0xF7365, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyFy10Foundation10URLRequestV_AF0C0VSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x4FB4, symBinAddr: 0x596E0, symSize: 0x8 }
  - { offset: 0xF7572, size: 0x8, addend: 0x0, symName: '_$sSlsSQ7ElementRpzrlE10firstIndex2of0C0QzSgAB_tFSS_Tg5', symObjAddr: 0x227C, symBinAddr: 0x56C68, symSize: 0xEC }
  - { offset: 0xF771B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSS_Tg5', symObjAddr: 0x0, symBinAddr: 0x549EC, symSize: 0x838 }
  - { offset: 0xF7B03, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSb_Tg5', symObjAddr: 0x838, symBinAddr: 0x55224, symSize: 0x820 }
  - { offset: 0xF7EEB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSi_Tg5', symObjAddr: 0x1058, symBinAddr: 0x55A44, symSize: 0x838 }
  - { offset: 0xF8388, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSS_Tgm5', symObjAddr: 0x1890, symBinAddr: 0x5627C, symSize: 0x360 }
  - { offset: 0xF85B1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSb_Tgm5', symObjAddr: 0x1BF0, symBinAddr: 0x565DC, symSize: 0x344 }
  - { offset: 0xF87DC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSi_Tgm5', symObjAddr: 0x1F34, symBinAddr: 0x56920, symSize: 0x348 }
  - { offset: 0xF8A16, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC5startyyF', symObjAddr: 0x23B0, symBinAddr: 0x56D9C, symSize: 0x180 }
  - { offset: 0xF8ADC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC5startyyF6$deferL_yyF', symObjAddr: 0x2530, symBinAddr: 0x56F1C, symSize: 0x128 }
  - { offset: 0xF8C32, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyF', symObjAddr: 0x2658, symBinAddr: 0x57044, symSize: 0x414 }
  - { offset: 0xF8E5C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyFy10Foundation10URLRequestV_AF0C0VSo13NSURLResponseCSgtcfU_', symObjAddr: 0x2A6C, symBinAddr: 0x57458, symSize: 0x500 }
  - { offset: 0xF8F9A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tF', symObjAddr: 0x2F90, symBinAddr: 0x5797C, symSize: 0x3FC }
  - { offset: 0xF91BD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_', symObjAddr: 0x338C, symBinAddr: 0x57D78, symSize: 0x36C }
  - { offset: 0xF927C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_', symObjAddr: 0x36F8, symBinAddr: 0x580E4, symSize: 0x258 }
  - { offset: 0xF9337, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_yyScMYcXEfU_', symObjAddr: 0x3950, symBinAddr: 0x5833C, symSize: 0x114 }
  - { offset: 0xF9413, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataCfD', symObjAddr: 0x3B58, symBinAddr: 0x58544, symSize: 0x24 }
  - { offset: 0xF9469, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC014fetchEncryptedB7AndHash33_C969CD0145D9EE45921CEF1FF36E8712LLSS_SStyFTf4d_n', symObjAddr: 0x4328, symBinAddr: 0x58BB8, symSize: 0x2C0 }
  - { offset: 0xF957C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC07decryptB6String8inBase64S2S_tFZTf4nd_n', symObjAddr: 0x45E8, symBinAddr: 0x58E78, symSize: 0x690 }
  - { offset: 0xF97DB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC03getB2JsSSyFTf4n_g', symObjAddr: 0x4C78, symBinAddr: 0x59508, symSize: 0xFC }
  - { offset: 0xF9CFC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCMa', symObjAddr: 0x540, symBinAddr: 0x59C30, symSize: 0x20 }
  - { offset: 0xF9D2A, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo19WKWebsiteDataRecordC_Tgm5', symObjAddr: 0x600, symBinAddr: 0x59CF0, symSize: 0x64 }
  - { offset: 0xF9D57, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlF8Razorpay21PluginPaymentDelegate_p_Tgm5', symObjAddr: 0x664, symBinAddr: 0x59D54, symSize: 0xC }
  - { offset: 0xF9D6F, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo18NSLayoutConstraintC_Tgm5', symObjAddr: 0x670, symBinAddr: 0x59D60, symSize: 0x64 }
  - { offset: 0xF9DA7, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0x904, symBinAddr: 0x59FF4, symSize: 0x1DC }
  - { offset: 0xF9E08, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFypSg_Tg5', symObjAddr: 0xAF4, symBinAddr: 0x5A1E4, symSize: 0x1C }
  - { offset: 0xF9E20, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0xB10, symBinAddr: 0x5A200, symSize: 0x1C }
  - { offset: 0xF9E9B, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFypSg_Tg5', symObjAddr: 0xB2C, symBinAddr: 0x5A21C, symSize: 0x128 }
  - { offset: 0xF9FFE, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0xC54, symBinAddr: 0x5A344, symSize: 0x130 }
  - { offset: 0xFA258, size: 0x8, addend: 0x0, symName: '_$ss14_ArrayProtocolPsE6filterySay7ElementQzGSbAEKXEKFSay8Razorpay21PluginPaymentDelegate_pG_Tg5013$s8Razorpay20fg30ManagerC07processC0yyFSbAA0bC8H7_pXEfU_AG0fG5ModelCTf1cn_nTf4gg_n', symObjAddr: 0xD84, symBinAddr: 0x5A474, symSize: 0x1C0 }
  - { offset: 0xFA456, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC17analyticsInstanceAA13AnalyticsUtilCSgvg', symObjAddr: 0x0, symBinAddr: 0x596F0, symSize: 0x100 }
  - { offset: 0xFA502, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC07processC0yyF', symObjAddr: 0x100, symBinAddr: 0x597F0, symSize: 0x2A0 }
  - { offset: 0xFA80F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC10trackEvent5event14withPropertiesySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x3A0, symBinAddr: 0x59A90, symSize: 0x14C }
  - { offset: 0xFA915, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCfD', symObjAddr: 0x4EC, symBinAddr: 0x59BDC, symSize: 0x54 }
  - { offset: 0xFA97F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP17paymentSuccessful7orderID16dictVerificationySS_SDys11AnyHashableVypGSgtFTW', symObjAddr: 0x560, symBinAddr: 0x59C50, symSize: 0x8C }
  - { offset: 0xFA9DA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGtFTW', symObjAddr: 0x5EC, symBinAddr: 0x59CDC, symSize: 0x10 }
  - { offset: 0xFAA04, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP10trackEvent5event14withPropertiesySS_SDys11AnyHashableVypGSgtFTW', symObjAddr: 0x5FC, symBinAddr: 0x59CEC, symSize: 0x4 }
  - { offset: 0xFAA24, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGtFTf4dnnn_n', symObjAddr: 0xF44, symBinAddr: 0x5A634, symSize: 0x8C }
  - { offset: 0xFAC76, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC5track_16havingProperties6sentByySS_SDySSypGSgAA16RzpWebViewBridgeOtF', symObjAddr: 0x0, symBinAddr: 0x5A6C0, symSize: 0x2AC }
  - { offset: 0xFAD73, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC5track_16havingProperties6sentByySS_SDySSypGSgAA16RzpWebViewBridgeOtF', symObjAddr: 0x0, symBinAddr: 0x5A6C0, symSize: 0x2AC }
  - { offset: 0xFB318, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCfETo', symObjAddr: 0x990, symBinAddr: 0x5B2FC, symSize: 0x80 }
  - { offset: 0xFB347, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCMa', symObjAddr: 0xA10, symBinAddr: 0x5B37C, symSize: 0x20 }
  - { offset: 0xFB36A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC17gestureRecognizer_13shouldReceiveSbSo09UIGestureF0C_So7UITouchCtFTo', symObjAddr: 0xA30, symBinAddr: 0x5B39C, symSize: 0x74 }
  - { offset: 0xFB3E8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_21numberOfRowsInSectionSiSo07UITableF0C_SitFTo', symObjAddr: 0xAA4, symBinAddr: 0x5B410, symSize: 0x24 }
  - { offset: 0xFB408, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_21numberOfRowsInSectionSiSo07UITableF0C_SitFTo', symObjAddr: 0xAA4, symBinAddr: 0x5B410, symSize: 0x24 }
  - { offset: 0xFB4AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_12cellForRowAtSo07UITableF4CellCSo0kF0C_10Foundation9IndexPathVtF', symObjAddr: 0xAC8, symBinAddr: 0x5B434, symSize: 0x570 }
  - { offset: 0xFB784, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_12cellForRowAtSo07UITableF4CellCSo0kF0C_10Foundation9IndexPathVtFTo', symObjAddr: 0x1038, symBinAddr: 0x5B9A4, symSize: 0xB8 }
  - { offset: 0xFB7AF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_14didSelectRowAtySo07UITableF0C_10Foundation9IndexPathVtFTo', symObjAddr: 0x10F0, symBinAddr: 0x5BA5C, symSize: 0xAC }
  - { offset: 0xFB7E1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC17gestureRecognizer_13shouldReceiveSbSo09UIGestureF0C_So7UITouchCtFTf4dnn_n', symObjAddr: 0x119C, symBinAddr: 0x5BB08, symSize: 0xE4 }
  - { offset: 0xFB820, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_14didSelectRowAtySo07UITableF0C_10Foundation9IndexPathVtFTf4dnn_n', symObjAddr: 0x1280, symBinAddr: 0x5BBEC, symSize: 0x2B0 }
  - { offset: 0xFB8C6, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaABSHSCWl', symObjAddr: 0x15B0, symBinAddr: 0x5BE9C, symSize: 0x48 }
  - { offset: 0xFB8DA, size: 0x8, addend: 0x0, symName: '_$sSo6UIViewCMa', symObjAddr: 0x15F8, symBinAddr: 0x5BEE4, symSize: 0x3C }
  - { offset: 0xFB8EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypFyycfU_TA', symObjAddr: 0x1658, symBinAddr: 0x5BF44, symSize: 0x20 }
  - { offset: 0xFB902, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1678, symBinAddr: 0x5BF64, symSize: 0x10 }
  - { offset: 0xFB916, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1688, symBinAddr: 0x5BF74, symSize: 0x8 }
  - { offset: 0xFB92A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC13dismissDailogyyFyycfU_TA', symObjAddr: 0x16A0, symBinAddr: 0x5BF7C, symSize: 0x20 }
  - { offset: 0xFB93E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC011autoDismissC0yyFyycfU_TA', symObjAddr: 0x16C0, symBinAddr: 0x5BF9C, symSize: 0x20 }
  - { offset: 0xFBB34, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableViewSo07UITableF0CSgvgTo', symObjAddr: 0x0, symBinAddr: 0x5A96C, symSize: 0x20 }
  - { offset: 0xFBB94, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableViewSo07UITableF0CSgvsTo', symObjAddr: 0x20, symBinAddr: 0x5A98C, symSize: 0x14 }
  - { offset: 0xFBCE4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC11viewDidLoadyyF', symObjAddr: 0x34, symBinAddr: 0x5A9A0, symSize: 0x274 }
  - { offset: 0xFBE7E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC11viewDidLoadyyFTo', symObjAddr: 0x2A8, symBinAddr: 0x5AC14, symSize: 0x28 }
  - { offset: 0xFBE92, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC011autoDismissC0yyFTo', symObjAddr: 0x2F4, symBinAddr: 0x5AC60, symSize: 0x28 }
  - { offset: 0xFBEA6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC13dismissDailogyyFTo', symObjAddr: 0x418, symBinAddr: 0x5AD84, symSize: 0x28 }
  - { offset: 0xFBF04, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypF', symObjAddr: 0x440, symBinAddr: 0x5ADAC, symSize: 0x1CC }
  - { offset: 0xFBFA2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypFTo', symObjAddr: 0x668, symBinAddr: 0x5AFD4, symSize: 0x64 }
  - { offset: 0xFBFB6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x6CC, symBinAddr: 0x5B038, symSize: 0x124 }
  - { offset: 0xFBFF7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x7F0, symBinAddr: 0x5B15C, symSize: 0x60 }
  - { offset: 0xFC00B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x850, symBinAddr: 0x5B1BC, symSize: 0xE8 }
  - { offset: 0xFC03E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x938, symBinAddr: 0x5B2A4, symSize: 0x28 }
  - { offset: 0xFC052, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCfD', symObjAddr: 0x960, symBinAddr: 0x5B2CC, symSize: 0x30 }
  - { offset: 0xFC2A9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LL_WZ', symObjAddr: 0x0, symBinAddr: 0x5BFD4, symSize: 0x40 }
  - { offset: 0xFC2CD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC05_baseC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA00, symBinAddr: 0xA44A0, symSize: 0x0 }
  - { offset: 0xFC2E7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC8_baseCdn33_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA10, symBinAddr: 0xA44B0, symSize: 0x0 }
  - { offset: 0xFC301, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC10_staticCdn33_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA20, symBinAddr: 0xA44C0, symSize: 0x0 }
  - { offset: 0xFC31B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC06_trackC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA30, symBinAddr: 0xA44D0, symSize: 0x0 }
  - { offset: 0xFC335, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA40, symBinAddr: 0xA44E0, symSize: 0x0 }
  - { offset: 0xFC34F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC07_butlerdC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA50, symBinAddr: 0xA44F0, symSize: 0x0 }
  - { offset: 0xFC35D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LL_WZ', symObjAddr: 0x0, symBinAddr: 0x5BFD4, symSize: 0x40 }
  - { offset: 0xFC402, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigCMa', symObjAddr: 0x50, symBinAddr: 0x5C014, symSize: 0x20 }
  - { offset: 0xFC4E0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC10initialize6configySDyS2SG_tFZTf4nd_n', symObjAddr: 0x70, symBinAddr: 0x5C034, symSize: 0x460 }
  - { offset: 0xFC756, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04baseC8CheckoutSSvgZTf4d_n', symObjAddr: 0x4D0, symBinAddr: 0x5C494, symSize: 0x2B0 }
  - { offset: 0xFCBEC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilCMa', symObjAddr: 0x10, symBinAddr: 0x5C744, symSize: 0x20 }
  - { offset: 0xFCC35, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilC14getStringValue6forKey011withDefaultG0S2S_SSSgtFZTf4nnd_n', symObjAddr: 0x30, symBinAddr: 0x5C764, symSize: 0x164 }
  - { offset: 0xFCC85, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilC11resetValuesyyFZTf4d_n', symObjAddr: 0x25C, symBinAddr: 0x5C8C8, symSize: 0x154 }
  - { offset: 0xFCE30, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19ResourceBundleClass06_E20D2F25E2878EA12AB6F7C669BA135C5LLCMa', symObjAddr: 0x10, symBinAddr: 0x5CA1C, symSize: 0x20 }
  - { offset: 0xFCFCD, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_magic, symObjAddr: 0x28, symBinAddr: 0x5CA3C, symSize: 0x14 }
  - { offset: 0xFCFDF, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_padding_bytes, symObjAddr: 0x44, symBinAddr: 0x5CA50, symSize: 0xC }
  - { offset: 0xFCFF1, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_version, symObjAddr: 0x50, symBinAddr: 0x5CA5C, symSize: 0x10 }
  - { offset: 0xFD034, size: 0x8, addend: 0x0, symName: _lprofProfileDumped, symObjAddr: 0x0, symBinAddr: 0x5CA6C, symSize: 0xC }
  - { offset: 0xFD042, size: 0x8, addend: 0x0, symName: _lprofProfileDumped, symObjAddr: 0x0, symBinAddr: 0x5CA6C, symSize: 0xC }
  - { offset: 0xFD07E, size: 0x8, addend: 0x0, symName: _getValueProfRecordHeaderSize, symObjAddr: 0x0, symBinAddr: 0x5CA78, symSize: 0xC }
  - { offset: 0xFD08C, size: 0x8, addend: 0x0, symName: _getValueProfRecordHeaderSize, symObjAddr: 0x0, symBinAddr: 0x5CA78, symSize: 0xC }
  - { offset: 0xFD0A5, size: 0x8, addend: 0x0, symName: _getValueProfRecordValueData, symObjAddr: 0x1C, symBinAddr: 0x5CA84, symSize: 0x14 }
  - { offset: 0xFD0DC, size: 0x8, addend: 0x0, symName: _getValueProfRecordNext, symObjAddr: 0x178, symBinAddr: 0x5CA98, symSize: 0x168 }
  - { offset: 0xFD11A, size: 0x8, addend: 0x0, symName: _getFirstValueProfRecord, symObjAddr: 0x2E0, symBinAddr: 0x5CC00, symSize: 0x8 }
  - { offset: 0xFD148, size: 0x8, addend: 0x0, symName: _lprofSetupValueProfiler, symObjAddr: 0x80C, symBinAddr: 0x5CC08, symSize: 0x68 }
  - { offset: 0xFD1B4, size: 0x8, addend: 0x0, symName: ___llvm_profile_instrument_target_value, symObjAddr: 0xB24, symBinAddr: 0x5CC70, symSize: 0x2B0 }
  - { offset: 0xFD236, size: 0x8, addend: 0x0, symName: _lprofGetVPDataReader, symObjAddr: 0xE1C, symBinAddr: 0x5CF20, symSize: 0xC }
  - { offset: 0xFD248, size: 0x8, addend: 0x0, symName: _initializeValueProfRuntimeRecord, symObjAddr: 0xE28, symBinAddr: 0x5CF2C, symSize: 0x178 }
  - { offset: 0xFD261, size: 0x8, addend: 0x0, symName: _getNumValueDataForSiteWrapper, symObjAddr: 0xFA0, symBinAddr: 0x5D0A4, symSize: 0x14 }
  - { offset: 0xFD277, size: 0x8, addend: 0x0, symName: _getNumValueDataForSiteWrapper, symObjAddr: 0xFA0, symBinAddr: 0x5D0A4, symSize: 0x14 }
  - { offset: 0xFD28A, size: 0x8, addend: 0x0, symName: _getValueProfDataSizeWrapper, symObjAddr: 0xFB4, symBinAddr: 0x5D0B8, symSize: 0xA8 }
  - { offset: 0xFD30F, size: 0x8, addend: 0x0, symName: _getNumValueSitesRT, symObjAddr: 0x10A0, symBinAddr: 0x5D1A4, symSize: 0x10 }
  - { offset: 0xFD321, size: 0x8, addend: 0x0, symName: _getNumValueDataRT, symObjAddr: 0x10B0, symBinAddr: 0x5D1B4, symSize: 0x154 }
  - { offset: 0xFD333, size: 0x8, addend: 0x0, symName: _getNextNValueData, symObjAddr: 0x105C, symBinAddr: 0x5D160, symSize: 0x44 }
  - { offset: 0xFD36F, size: 0x8, addend: 0x0, symName: ___llvm_profile_is_continuous_mode_enabled, symObjAddr: 0x0, symBinAddr: 0x5D308, symSize: 0x1C }
  - { offset: 0xFD37D, size: 0x8, addend: 0x0, symName: ___llvm_profile_is_continuous_mode_enabled, symObjAddr: 0x0, symBinAddr: 0x5D308, symSize: 0x1C }
  - { offset: 0xFD38F, size: 0x8, addend: 0x0, symName: ___llvm_profile_enable_continuous_mode, symObjAddr: 0x1C, symBinAddr: 0x5D324, symSize: 0x10 }
  - { offset: 0xFD3A1, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_page_size, symObjAddr: 0x2C, symBinAddr: 0x5D334, symSize: 0xC }
  - { offset: 0xFD3B3, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_size_for_buffer, symObjAddr: 0x38, symBinAddr: 0x5D340, symSize: 0x6C }
  - { offset: 0xFD458, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_size_for_buffer_internal, symObjAddr: 0xA4, symBinAddr: 0x5D3AC, symSize: 0x138 }
  - { offset: 0xFD62D, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_data, symObjAddr: 0x1DC, symBinAddr: 0x5D4E4, symSize: 0x1C }
  - { offset: 0xFD63F, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_data_size, symObjAddr: 0x1F8, symBinAddr: 0x5D500, symSize: 0x24 }
  - { offset: 0xFD655, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_data_size, symObjAddr: 0x1F8, symBinAddr: 0x5D500, symSize: 0x24 }
  - { offset: 0xFD667, size: 0x8, addend: 0x0, symName: ___llvm_profile_counter_entry_size, symObjAddr: 0x21C, symBinAddr: 0x5D524, symSize: 0x20 }
  - { offset: 0xFD688, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_counters, symObjAddr: 0x23C, symBinAddr: 0x5D544, symSize: 0x4C }
  - { offset: 0xFD6E1, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_counters_size, symObjAddr: 0x288, symBinAddr: 0x5D590, symSize: 0x5C }
  - { offset: 0xFD773, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_padding_sizes_for_counters, symObjAddr: 0x2E4, symBinAddr: 0x5D5EC, symSize: 0xC8 }
  - { offset: 0xFD80C, size: 0x8, addend: 0x0, symName: _initBufferWriter, symObjAddr: 0x3AC, symBinAddr: 0x5D6B4, symSize: 0x10 }
  - { offset: 0xFD86C, size: 0x8, addend: 0x0, symName: _getCurFilenameLength, symObjAddr: 0x1C0, symBinAddr: 0x5D6C4, symSize: 0x324 }
  - { offset: 0xFD8B7, size: 0x8, addend: 0x0, symName: _getCurFilename, symObjAddr: 0x4E4, symBinAddr: 0x5D9E8, symSize: 0x33C }
  - { offset: 0xFD9BB, size: 0x8, addend: 0x0, symName: _parseAndSetFilename, symObjAddr: 0x928, symBinAddr: 0x5DD24, symSize: 0x568 }
  - { offset: 0xFDC0F, size: 0x8, addend: 0x0, symName: _truncateCurrentFile, symObjAddr: 0x1CF4, symBinAddr: 0x5EBF0, symSize: 0xCC }
  - { offset: 0xFDC84, size: 0x8, addend: 0x0, symName: _initializeProfileForContinuousMode, symObjAddr: 0x1DC0, symBinAddr: 0x5ECBC, symSize: 0x250 }
  - { offset: 0xFDE9D, size: 0x8, addend: 0x0, symName: ___llvm_profile_initialize, symObjAddr: 0xE90, symBinAddr: 0x5E28C, symSize: 0xBC }
  - { offset: 0xFDF5B, size: 0x8, addend: 0x0, symName: ___llvm_profile_write_file, symObjAddr: 0xFD0, symBinAddr: 0x5E348, symSize: 0x184 }
  - { offset: 0xFE027, size: 0x8, addend: 0x0, symName: _writeFile, symObjAddr: 0x1154, symBinAddr: 0x5E4CC, symSize: 0x288 }
  - { offset: 0xFE2BD, size: 0x8, addend: 0x0, symName: _createProfileDir, symObjAddr: 0x2010, symBinAddr: 0x5EF0C, symSize: 0x88 }
  - { offset: 0xFE302, size: 0x8, addend: 0x0, symName: _getProfileFileSizeForMerging, symObjAddr: 0x185C, symBinAddr: 0x5E758, symSize: 0x114 }
  - { offset: 0xFE393, size: 0x8, addend: 0x0, symName: _mmapProfileForMerging, symObjAddr: 0x1970, symBinAddr: 0x5E86C, symSize: 0xC0 }
  - { offset: 0xFE452, size: 0x8, addend: 0x0, symName: _writeFileWithoutReturn, symObjAddr: 0x1670, symBinAddr: 0x5E754, symSize: 0x4 }
  - { offset: 0xFE468, size: 0x8, addend: 0x0, symName: _writeFileWithoutReturn, symObjAddr: 0x1670, symBinAddr: 0x5E754, symSize: 0x4 }
  - { offset: 0xFE47A, size: 0x8, addend: 0x0, symName: _mmapForContinuousMode, symObjAddr: 0x1A30, symBinAddr: 0x5E92C, symSize: 0x180 }
  - { offset: 0xFE5C2, size: 0x8, addend: 0x0, symName: _fileWriter, symObjAddr: 0x1BB0, symBinAddr: 0x5EAAC, symSize: 0x144 }
  - { offset: 0xFE61E, size: 0x8, addend: 0x0, symName: _exitSignalHandler, symObjAddr: 0x2098, symBinAddr: 0x5EF94, symSize: 0x10 }
  - { offset: 0xFE671, size: 0x8, addend: 0x0, symName: _lprofGetLoadModuleSignature, symObjAddr: 0x0, symBinAddr: 0x5EFA4, symSize: 0xD4 }
  - { offset: 0xFE67F, size: 0x8, addend: 0x0, symName: _lprofGetLoadModuleSignature, symObjAddr: 0x0, symBinAddr: 0x5EFA4, symSize: 0xD4 }
  - { offset: 0xFE780, size: 0x8, addend: 0x0, symName: ___llvm_profile_check_compatibility, symObjAddr: 0xD4, symBinAddr: 0x5F078, symSize: 0x19C }
  - { offset: 0xFE844, size: 0x8, addend: 0x0, symName: ___llvm_profile_merge_from_buffer, symObjAddr: 0x270, symBinAddr: 0x5F214, symSize: 0x1E8 }
  - { offset: 0xFE8FF, size: 0x8, addend: 0x0, symName: _lprofMergeValueProfData, symObjAddr: 0x0, symBinAddr: 0x5F3FC, symSize: 0xE4 }
  - { offset: 0xFE90D, size: 0x8, addend: 0x0, symName: _lprofMergeValueProfData, symObjAddr: 0x0, symBinAddr: 0x5F3FC, symSize: 0xE4 }
  - { offset: 0xFE9A5, size: 0x8, addend: 0x0, symName: _lprofBufferWriter, symObjAddr: 0x0, symBinAddr: 0x5F4E0, symSize: 0x70 }
  - { offset: 0xFE9B3, size: 0x8, addend: 0x0, symName: _lprofBufferWriter, symObjAddr: 0x0, symBinAddr: 0x5F4E0, symSize: 0x70 }
  - { offset: 0xFE9DA, size: 0x8, addend: 0x0, symName: _lprofBufferIOWrite, symObjAddr: 0x108, symBinAddr: 0x5F550, symSize: 0x138 }
  - { offset: 0xFEA45, size: 0x8, addend: 0x0, symName: _lprofWriteData, symObjAddr: 0x304, symBinAddr: 0x5F688, symSize: 0x98 }
  - { offset: 0xFEAEA, size: 0x8, addend: 0x0, symName: _lprofWriteDataImpl, symObjAddr: 0x39C, symBinAddr: 0x5F720, symSize: 0x2E0 }
  - { offset: 0xFEC3D, size: 0x8, addend: 0x0, symName: _createHeader, symObjAddr: 0x67C, symBinAddr: 0x5FA00, symSize: 0x124 }
  - { offset: 0xFECE7, size: 0x8, addend: 0x0, symName: _writeOneValueProfData, symObjAddr: 0xC5C, symBinAddr: 0x5FB24, symSize: 0x370 }
  - { offset: 0xFEE31, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_data, symObjAddr: 0x0, symBinAddr: 0x5FE94, symSize: 0xC }
  - { offset: 0xFEE3F, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_data, symObjAddr: 0x0, symBinAddr: 0x5FE94, symSize: 0xC }
  - { offset: 0xFEE51, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_data, symObjAddr: 0xC, symBinAddr: 0x5FEA0, symSize: 0xC }
  - { offset: 0xFEE63, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_names, symObjAddr: 0x18, symBinAddr: 0x5FEAC, symSize: 0xC }
  - { offset: 0xFEE75, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_names, symObjAddr: 0x24, symBinAddr: 0x5FEB8, symSize: 0xC }
  - { offset: 0xFEE87, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_counters, symObjAddr: 0x30, symBinAddr: 0x5FEC4, symSize: 0xC }
  - { offset: 0xFEE99, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_counters, symObjAddr: 0x3C, symBinAddr: 0x5FED0, symSize: 0xC }
  - { offset: 0xFEEAB, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_vnodes, symObjAddr: 0x54, symBinAddr: 0x5FEDC, symSize: 0xC }
  - { offset: 0xFEEBD, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_vnodes, symObjAddr: 0x60, symBinAddr: 0x5FEE8, symSize: 0xC }
  - { offset: 0xFEEF9, size: 0x8, addend: 0x0, symName: ___llvm_profile_recursive_mkdir, symObjAddr: 0x0, symBinAddr: 0x5FF10, symSize: 0x5C }
  - { offset: 0xFEF0E, size: 0x8, addend: 0x0, symName: ___llvm_profile_recursive_mkdir, symObjAddr: 0x0, symBinAddr: 0x5FF10, symSize: 0x5C }
  - { offset: 0xFEF4D, size: 0x8, addend: 0x0, symName: _lprofGetHostName, symObjAddr: 0x74, symBinAddr: 0x5FF6C, symSize: 0x80 }
  - { offset: 0xFEF97, size: 0x8, addend: 0x0, symName: _lprofLockFileHandle, symObjAddr: 0x214, symBinAddr: 0x5FFEC, symSize: 0x94 }
  - { offset: 0xFF015, size: 0x8, addend: 0x0, symName: _lprofUnlockFileHandle, symObjAddr: 0x2A8, symBinAddr: 0x60080, symSize: 0x94 }
  - { offset: 0xFF083, size: 0x8, addend: 0x0, symName: _lprofOpenFileEx, symObjAddr: 0x33C, symBinAddr: 0x60114, symSize: 0xD4 }
  - { offset: 0xFF127, size: 0x8, addend: 0x0, symName: _lprofFindFirstDirSeparator, symObjAddr: 0x554, symBinAddr: 0x601E8, symSize: 0x8 }
  - { offset: 0xFF150, size: 0x8, addend: 0x0, symName: _lprofInstallSignalHandler, symObjAddr: 0x564, symBinAddr: 0x601F0, symSize: 0x58 }
...
