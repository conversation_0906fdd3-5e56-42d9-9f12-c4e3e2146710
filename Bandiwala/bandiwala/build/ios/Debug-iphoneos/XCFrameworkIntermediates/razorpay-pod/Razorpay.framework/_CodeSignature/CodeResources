<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		Bju3rMGH/QCKRwqZN0W5qAnnnS4=
		</data>
		<key>Checkout.storyboardc/Info.plist</key>
		<data>
		DFcy10+V49NKtjJ4RLpByN10P1k=
		</data>
		<key>Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<data>
		c8N/593lOrAw9wwSo2JSseKACUI=
		</data>
		<key>Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<data>
		GAbF07uihY0DE3w15+txJ0RlSNw=
		</data>
		<key>Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<data>
		Ffh5EuOShB1cS/eITOW9HbEzxi4=
		</data>
		<key>Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<data>
		6QJ3pwC0+qM8m2WjXuCS4IdsbG0=
		</data>
		<key>Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<data>
		GuaLaOgcxzpvPP6jj7CE6UW1x54=
		</data>
		<key>Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<data>
		Ucg3HkU3DNFIfnKf6gQlzd/zqTg=
		</data>
		<key>CommonAssets/Razorpay_Logo.png</key>
		<data>
		C/QPifs1kjcxzxgwUgDFDlLjpRw=
		</data>
		<key>CommonAssets/check_mark.png</key>
		<data>
		6d4pPz33KoUobYRDPpGmnPiTVMs=
		</data>
		<key>CommonAssets/warning.png</key>
		<data>
		gxArEMTCcu4a+ueYNB3oMoIh48o=
		</data>
		<key>EncryptedOtpelf.js</key>
		<data>
		A893KbMpygzZy6/G1xrQkAudMxw=
		</data>
		<key>Hash.txt</key>
		<data>
		gN8QKnsfFYPlPa6NstmuiJETxJ8=
		</data>
		<key>Headers/Razorpay-Swift.h</key>
		<data>
		PRlKKZ2rnhof0Sd4AKdnLo0/AvU=
		</data>
		<key>Info.plist</key>
		<data>
		yTtAviMT5W1lEvAmyZ50ZTxnFXI=
		</data>
		<key>Modules/Razorpay.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		F/7jgnrK8xMqKBs9ffFHq927Ti8=
		</data>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		mDEFs70Oe5bmoU5DMQSRd71imsE=
		</data>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		o8ksYIFDmoy7v+5tgZadIduCDuQ=
		</data>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		Saoy8zir8yQPMB9nV1bvzjh+2XQ=
		</data>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		o8ksYIFDmoy7v+5tgZadIduCDuQ=
		</data>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<data>
		BYeQaGFtCOSFMpMDeHbXb1ym57E=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		V7p5JZN/5LIVFwwoysVXL04EKZk=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		62HpNLqPh8tKsg+iNP/pSbF2S6M=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			Bju3rMGH/QCKRwqZN0W5qAnnnS4=
			</data>
			<key>hash2</key>
			<data>
			PHb2ZuQOlqYYHw0P0haHf/aFuDNeeCbe+axDReTr7h0=
			</data>
		</dict>
		<key>Checkout.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			DFcy10+V49NKtjJ4RLpByN10P1k=
			</data>
			<key>hash2</key>
			<data>
			XV+Km0uI0aCCx6b8FFBL8ctnAUqg/+iH2HKwpDJJDns=
			</data>
		</dict>
		<key>Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			2CirjXkAaTuDLc4zw3TTR01v6hY=
			</data>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			2CirjXkAaTuDLc4zw3TTR01v6hY=
			</data>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0nbduMecK7uoslXovJgcjilOIB8=
			</data>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0nbduMecK7uoslXovJgcjilOIB8=
			</data>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			c8N/593lOrAw9wwSo2JSseKACUI=
			</data>
			<key>hash2</key>
			<data>
			9gbR1Bca1fy1VmXM6YTr9iMj/H9FiV5FH80kOSCS5cA=
			</data>
		</dict>
		<key>Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			GAbF07uihY0DE3w15+txJ0RlSNw=
			</data>
			<key>hash2</key>
			<data>
			0aP4/XYhE9pTrC1von5mej2B5hZzviQdM4PQj/nojlY=
			</data>
		</dict>
		<key>Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Ffh5EuOShB1cS/eITOW9HbEzxi4=
			</data>
			<key>hash2</key>
			<data>
			kuyVflk39J/m/Tqjb+YY8Y38/6KfwNjCkTXASxoCEXQ=
			</data>
		</dict>
		<key>Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6QJ3pwC0+qM8m2WjXuCS4IdsbG0=
			</data>
			<key>hash2</key>
			<data>
			cAbddAE/I0qU6/oKTC87iT4nJ2fd0p9/BUaCe80QOek=
			</data>
		</dict>
		<key>Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ZnQL9pjNKu2IujGRcLuxvNEwdtg=
			</data>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ZnQL9pjNKu2IujGRcLuxvNEwdtg=
			</data>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eTt4FnKOhvYjwYf1odsqLi0ehZ8=
			</data>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eTt4FnKOhvYjwYf1odsqLi0ehZ8=
			</data>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bHc46UFZ5G9OBiYbYzEHDYGyxtM=
			</data>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bHc46UFZ5G9OBiYbYzEHDYGyxtM=
			</data>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			GuaLaOgcxzpvPP6jj7CE6UW1x54=
			</data>
			<key>hash2</key>
			<data>
			I8p2+VocnQu/7nTAHxHwPLoZ9J+et+y957CgfHi+MQo=
			</data>
		</dict>
		<key>Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Ucg3HkU3DNFIfnKf6gQlzd/zqTg=
			</data>
			<key>hash2</key>
			<data>
			h05YMT+1oAA6um16sH5KVB2c6toLm935YPmV2d/ZDU8=
			</data>
		</dict>
		<key>CommonAssets/Razorpay_Logo.png</key>
		<dict>
			<key>hash</key>
			<data>
			C/QPifs1kjcxzxgwUgDFDlLjpRw=
			</data>
			<key>hash2</key>
			<data>
			udRErjoaEwN536HIEl+2sH6KQ0Q2KzlKwLYCkQlBGKE=
			</data>
		</dict>
		<key>CommonAssets/check_mark.png</key>
		<dict>
			<key>hash</key>
			<data>
			6d4pPz33KoUobYRDPpGmnPiTVMs=
			</data>
			<key>hash2</key>
			<data>
			s+l4gXoMSGUj0xR2eSdXwQTHoyRU5F4+aTSVjO+I8wU=
			</data>
		</dict>
		<key>CommonAssets/warning.png</key>
		<dict>
			<key>hash</key>
			<data>
			gxArEMTCcu4a+ueYNB3oMoIh48o=
			</data>
			<key>hash2</key>
			<data>
			S7OOo4xdlAEiEgfkEuic4ap4JZlhvtYIwFSHWu034SA=
			</data>
		</dict>
		<key>EncryptedOtpelf.js</key>
		<dict>
			<key>hash</key>
			<data>
			A893KbMpygzZy6/G1xrQkAudMxw=
			</data>
			<key>hash2</key>
			<data>
			85Tocsg1aPekBFPeMKMV2IruNGVkOyyV6xlbQkQOH3A=
			</data>
		</dict>
		<key>Hash.txt</key>
		<dict>
			<key>hash</key>
			<data>
			gN8QKnsfFYPlPa6NstmuiJETxJ8=
			</data>
			<key>hash2</key>
			<data>
			B20CsAEe8H45a3Ivw23aBTWNU7qGh1JYKb/iAwwaIEQ=
			</data>
		</dict>
		<key>Headers/Razorpay-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			PRlKKZ2rnhof0Sd4AKdnLo0/AvU=
			</data>
			<key>hash2</key>
			<data>
			5o4CV9wRfjX11ZL9CaviS1IbJOM6E4Nli4KZckfSGXU=
			</data>
		</dict>
		<key>Modules/Razorpay.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			F/7jgnrK8xMqKBs9ffFHq927Ti8=
			</data>
			<key>hash2</key>
			<data>
			gn/Zc8sCX44L/X2ekVhzL9rgB8URsvnWLwO6TW2KAAI=
			</data>
		</dict>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			mDEFs70Oe5bmoU5DMQSRd71imsE=
			</data>
			<key>hash2</key>
			<data>
			UD8hrh8E9DFBZI9XmDBSX2ik4Zx06OdxY2SOAHivYUg=
			</data>
		</dict>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			o8ksYIFDmoy7v+5tgZadIduCDuQ=
			</data>
			<key>hash2</key>
			<data>
			z6sp2+macJnTSCLCpyBPOeGQG1+zGI3Lt2qdo5lUSMY=
			</data>
		</dict>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			Saoy8zir8yQPMB9nV1bvzjh+2XQ=
			</data>
			<key>hash2</key>
			<data>
			oSFfWtYrVIocLybHsfFIWaGB2IqGS93FO+F6Z84Ox7k=
			</data>
		</dict>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			o8ksYIFDmoy7v+5tgZadIduCDuQ=
			</data>
			<key>hash2</key>
			<data>
			z6sp2+macJnTSCLCpyBPOeGQG1+zGI3Lt2qdo5lUSMY=
			</data>
		</dict>
		<key>Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			BYeQaGFtCOSFMpMDeHbXb1ym57E=
			</data>
			<key>hash2</key>
			<data>
			kIJkMhZxSPVsQy4ykDEmrt9WzSFJjxyKR3AQDNBWeOw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			V7p5JZN/5LIVFwwoysVXL04EKZk=
			</data>
			<key>hash2</key>
			<data>
			HN2sZF801VNBYg/HdS8Qv3Qh1iVLaWMgwAMyXqe8iKg=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			62HpNLqPh8tKsg+iNP/pSbF2S6M=
			</data>
			<key>hash2</key>
			<data>
			0GK4q+J5XVD2O8agumlONknk2PSlkzr97y/P84XeCyg=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
