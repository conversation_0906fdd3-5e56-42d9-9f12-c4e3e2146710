<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>Flutter.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Google-Maps-iOS-Utils.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleMaps-GoogleMapsResources.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleMaps.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-Runner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-RunnerTests.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_email_sender-flutter_email_sender.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_email_sender.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_secure_storage-flutter_secure_storage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_secure_storage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>fluttertoast-fluttertoast_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>fluttertoast.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>geocoding_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>geolocator_apple-geolocator_apple_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>geolocator_apple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>google_maps_flutter_ios-google_maps_flutter_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>google_maps_flutter_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>image_picker_ios-image_picker_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>image_picker_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation-path_provider_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple-permission_handler_apple_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>razorpay-pod.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>razorpay_flutter.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation-shared_preferences_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>smart_auth.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios-url_launcher_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
