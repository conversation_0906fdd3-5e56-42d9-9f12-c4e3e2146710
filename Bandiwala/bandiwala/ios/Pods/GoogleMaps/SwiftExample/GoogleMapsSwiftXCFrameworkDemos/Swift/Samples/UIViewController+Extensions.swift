// Copyright 2023 Google LLC. All rights reserved.
//
//
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use this
// file except in compliance with the License. You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed under
// the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF
// ANY KIND, either express or implied. See the License for the specific language governing
// permissions and limitations under the License.

import UIKit

extension UIViewController {
  public func showToast(message: String) {
    let toast = UIAlertController(title: nil, message: message, preferredStyle: .alert)
    present(
      toast, animated: true,
      completion: {
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + .seconds(2)) {
          toast.dismiss(animated: true)
        }
      })
  }

  public func promptForMapID(description: String, completion: @escaping (String) -> Void) {
    let alert = UIAlertController(
      title: "Map ID to use",
      message: "Enter a Map ID configured from the Cloud Console \(description)",
      preferredStyle: .alert)
    alert.addAction(
      UIAlertAction(
        title: "Cancel", style: .cancel,
        handler: { _ in
          completion("")
        }))
    alert.addAction(
      UIAlertAction(
        title: "Confirm", style: .default,
        handler: { _ in
          completion(alert.textFields?.first?.text ?? "")
        }))
    alert.addTextField(configurationHandler: { $0.placeholder = "Map ID" })
    self.present(alert, animated: true, completion: nil)
  }
}
