<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="10116" systemVersion="15F34" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" initialViewController="FA1-J0-KAa">
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="10085"/>
    </dependencies>
    <scenes>
        <!--Table View Controller-->
        <scene sceneID="BiP-r6-d2e">
            <objects>
                <tableViewController clearsSelectionOnViewWillAppear="NO" id="MEq-fz-d5D" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="static" style="plain" separatorStyle="default" rowHeight="44" sectionHeaderHeight="28" sectionFooterHeight="28" id="TxE-uG-WRf">
                        <rect key="frame" x="0.0" y="64" width="600" height="536"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <sections>
                            <tableViewSection headerTitle=" " id="knR-Pj-As9">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="USa-fA-rSo">
                                        <rect key="frame" x="0.0" y="28" width="600" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="USa-fA-rSo" id="B07-0v-1rs">
                                            <rect key="frame" x="0.0" y="0.0" width="600" height="43"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="cx7-vJ-Uys">
                                        <rect key="frame" x="0.0" y="72" width="600" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="cx7-vJ-Uys" id="yzB-kq-4h5">
                                            <rect key="frame" x="0.0" y="0.0" width="600" height="43"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="Y7t-7w-LlT">
                                        <rect key="frame" x="0.0" y="116" width="600" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Y7t-7w-LlT" id="xEX-UD-8pF">
                                            <rect key="frame" x="0.0" y="0.0" width="600" height="43"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="MEq-fz-d5D" id="ifl-DD-lEK"/>
                            <outlet property="delegate" destination="MEq-fz-d5D" id="dSy-kE-SWt"/>
                        </connections>
                    </tableView>
                    <extendedEdge key="edgesForExtendedLayout" bottom="YES"/>
                    <navigationItem key="navigationItem" id="OVc-ik-KAD"/>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="yhF-d5-Y7D" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1630" y="-96"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="6Gc-8o-0et">
            <objects>
                <viewController id="YRZ-j4-N6z" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="I2y-I1-GwB"/>
                        <viewControllerLayoutGuide type="bottom" id="hWz-J8-gdE"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="9XH-ez-F0t">
                        <rect key="frame" x="0.0" y="0.0" width="600" height="600"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="0.9137254901960784" green="0.89803921568627454" blue="0.85882352941176465" alpha="1" colorSpace="calibratedRGB"/>
                    </view>
                    <navigationItem key="navigationItem" id="gM0-ZE-dNL"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="eOH-9U-1Zu" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1630" y="588"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="GlB-rK-pG2">
            <objects>
                <navigationController id="WOd-NA-Oqr" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="s66-cv-zc0">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="MEq-fz-d5D" kind="relationship" relationship="rootViewController" id="thR-Dz-4vA"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="72E-Ti-ULj" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="876" y="-96"/>
        </scene>
        <!--Split View Controller-->
        <scene sceneID="EIS-Y8-RQ6">
            <objects>
                <splitViewController id="FA1-J0-KAa" sceneMemberID="viewController">
                    <connections>
                        <segue destination="WOd-NA-Oqr" kind="relationship" relationship="masterViewController" id="Zm1-nY-LVC"/>
                        <segue destination="l6G-hw-ciz" kind="relationship" relationship="detailViewController" id="gpz-nc-7Ae"/>
                    </connections>
                </splitViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="LLF-Wb-Fvv" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-10" y="235"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="Cv7-Zr-41p">
            <objects>
                <navigationController id="l6G-hw-ciz" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="rqN-jS-PAw">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="YRZ-j4-N6z" kind="relationship" relationship="rootViewController" id="Xg7-wq-csC"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="hgH-vw-zuX" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="876" y="588"/>
        </scene>
    </scenes>
</document>
