[{"featureType": "all", "elementType": "labels.text.fill", "stylers": [{"color": "#755f5d"}]}, {"featureType": "administrative", "elementType": "geometry.fill", "stylers": [{"color": "#d4ccb9"}]}, {"featureType": "administrative.country", "elementType": "geometry.stroke", "stylers": [{"color": "#baafae"}]}, {"featureType": "administrative.land_parcel", "elementType": "geometry.stroke", "stylers": [{"color": "#d4ccb9"}]}, {"featureType": "landscape.man_made", "elementType": "geometry.fill", "stylers": [{"color": "#ebe3cd"}]}, {"featureType": "landscape.natural", "elementType": "geometry", "stylers": [{"color": "#ebe3cd"}]}, {"featureType": "landscape.natural", "elementType": "geometry.fill", "stylers": [{"lightness": -10}]}, {"featureType": "poi", "elementType": "geometry.fill", "stylers": [{"color": "#d4ccb9"}]}, {"featureType": "poi", "elementType": "labels.icon", "stylers": [{"hue": "#ff7f00"}]}, {"featureType": "poi.park", "elementType": "geometry.fill", "stylers": [{"color": "#9ba56f"}]}, {"featureType": "road", "elementType": "geometry.fill", "stylers": [{"color": "#f5f1e6"}]}, {"featureType": "road", "elementType": "geometry.stroke", "stylers": [{"color": "#dfd8c3"}]}, {"featureType": "road.arterial", "elementType": "geometry.fill", "stylers": [{"color": "#fdfcf8"}]}, {"featureType": "road.arterial", "elementType": "geometry.stroke", "stylers": [{"color": "#e4e3df"}]}, {"featureType": "road.highway", "elementType": "geometry.fill", "stylers": [{"color": "#f2cb77"}]}, {"featureType": "road.highway", "elementType": "geometry.stroke", "stylers": [{"color": "#ecb43d"}]}, {"featureType": "road.highway.controlled_access", "elementType": "geometry.fill", "stylers": [{"color": "#e98d58"}]}, {"featureType": "road.highway.controlled_access", "elementType": "geometry.stroke", "stylers": [{"color": "#d27f4f"}]}, {"featureType": "transit.line", "elementType": "geometry", "stylers": [{"color": "#d4ccb9"}]}, {"featureType": "transit.station.airport", "elementType": "geometry.fill", "stylers": [{"color": "#d4ccb9"}]}, {"featureType": "water", "elementType": "geometry.fill", "stylers": [{"color": "#b9d3c2"}]}]