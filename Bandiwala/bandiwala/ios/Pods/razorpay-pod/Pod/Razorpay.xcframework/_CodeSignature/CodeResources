<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/Razorpay.framework/Assets.car</key>
		<data>
		Bju3rMGH/QCKRwqZN0W5qAnnnS4=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/Info.plist</key>
		<data>
		DFcy10+V49NKtjJ4RLpByN10P1k=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<data>
		c8N/593lOrAw9wwSo2JSseKACUI=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<data>
		GAbF07uihY0DE3w15+txJ0RlSNw=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<data>
		Ffh5EuOShB1cS/eITOW9HbEzxi4=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<data>
		6QJ3pwC0+qM8m2WjXuCS4IdsbG0=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<data>
		GuaLaOgcxzpvPP6jj7CE6UW1x54=
		</data>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<data>
		Ucg3HkU3DNFIfnKf6gQlzd/zqTg=
		</data>
		<key>ios-arm64/Razorpay.framework/CommonAssets/Razorpay_Logo.png</key>
		<data>
		C/QPifs1kjcxzxgwUgDFDlLjpRw=
		</data>
		<key>ios-arm64/Razorpay.framework/CommonAssets/check_mark.png</key>
		<data>
		6d4pPz33KoUobYRDPpGmnPiTVMs=
		</data>
		<key>ios-arm64/Razorpay.framework/CommonAssets/warning.png</key>
		<data>
		gxArEMTCcu4a+ueYNB3oMoIh48o=
		</data>
		<key>ios-arm64/Razorpay.framework/EncryptedOtpelf.js</key>
		<data>
		A893KbMpygzZy6/G1xrQkAudMxw=
		</data>
		<key>ios-arm64/Razorpay.framework/Hash.txt</key>
		<data>
		gN8QKnsfFYPlPa6NstmuiJETxJ8=
		</data>
		<key>ios-arm64/Razorpay.framework/Headers/Razorpay-Swift.h</key>
		<data>
		1sJt0oI76TT+xRxnDJcDHj0yJoY=
		</data>
		<key>ios-arm64/Razorpay.framework/Info.plist</key>
		<data>
		Cw5egKGuBPPchZLu1T2oHdF2hp0=
		</data>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		xHOI7sCquIprmbdzlZtwGUQCs6A=
		</data>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		RkTTy58lUjQLu32Z1NQ8LwOB3JQ=
		</data>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		GLsfx7baeG/t9eh1cdLWIJm6ntM=
		</data>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		Saoy8zir8yQPMB9nV1bvzjh+2XQ=
		</data>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		GLsfx7baeG/t9eh1cdLWIJm6ntM=
		</data>
		<key>ios-arm64/Razorpay.framework/Modules/module.modulemap</key>
		<data>
		V7p5JZN/5LIVFwwoysVXL04EKZk=
		</data>
		<key>ios-arm64/Razorpay.framework/PrivacyInfo.xcprivacy</key>
		<data>
		62HpNLqPh8tKsg+iNP/pSbF2S6M=
		</data>
		<key>ios-arm64/Razorpay.framework/Razorpay</key>
		<data>
		+kvV4qFnfnNKJO2Db7ZYowwdDV8=
		</data>
		<key>ios-arm64/Razorpay.framework/_CodeSignature/CodeResources</key>
		<data>
		ZTFX47KHWHzNSjh5RHCK5tl1QVA=
		</data>
		<key>ios-arm64/dSYMs/Razorpay.framework.dSYM/Contents/Info.plist</key>
		<data>
		XkQP8tnX4SAb6IVXpLZZLNPTUpM=
		</data>
		<key>ios-arm64/dSYMs/Razorpay.framework.dSYM/Contents/Resources/DWARF/Razorpay</key>
		<data>
		/2ulDkrwdQ4aXdLFMLJc30rAI2o=
		</data>
		<key>ios-arm64/dSYMs/Razorpay.framework.dSYM/Contents/Resources/Relocations/aarch64/Razorpay.yml</key>
		<data>
		t6aZA2hrYuBDv2vtfWobiXH5OTg=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Assets.car</key>
		<data>
		Bju3rMGH/QCKRwqZN0W5qAnnnS4=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/Info.plist</key>
		<data>
		DFcy10+V49NKtjJ4RLpByN10P1k=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<data>
		2CirjXkAaTuDLc4zw3TTR01v6hY=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<data>
		0nbduMecK7uoslXovJgcjilOIB8=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<data>
		c8N/593lOrAw9wwSo2JSseKACUI=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<data>
		GAbF07uihY0DE3w15+txJ0RlSNw=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<data>
		Ffh5EuOShB1cS/eITOW9HbEzxi4=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<data>
		6QJ3pwC0+qM8m2WjXuCS4IdsbG0=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<data>
		ZnQL9pjNKu2IujGRcLuxvNEwdtg=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<data>
		eTt4FnKOhvYjwYf1odsqLi0ehZ8=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<data>
		bHc46UFZ5G9OBiYbYzEHDYGyxtM=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<data>
		GuaLaOgcxzpvPP6jj7CE6UW1x54=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<data>
		Ucg3HkU3DNFIfnKf6gQlzd/zqTg=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/CommonAssets/Razorpay_Logo.png</key>
		<data>
		C/QPifs1kjcxzxgwUgDFDlLjpRw=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/CommonAssets/check_mark.png</key>
		<data>
		6d4pPz33KoUobYRDPpGmnPiTVMs=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/CommonAssets/warning.png</key>
		<data>
		gxArEMTCcu4a+ueYNB3oMoIh48o=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/EncryptedOtpelf.js</key>
		<data>
		A893KbMpygzZy6/G1xrQkAudMxw=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Hash.txt</key>
		<data>
		gN8QKnsfFYPlPa6NstmuiJETxJ8=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Headers/Razorpay-Swift.h</key>
		<data>
		aCpEyQx0oDScntCpmjIE+HBAtfA=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Info.plist</key>
		<data>
		H31lLYbowzCDA/myCBcG62SKY4k=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		Z+G0pIvfT/0bBEh3zuxenvvbqcc=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		nxKJAkx2tpl+KnApt9mKYxrFpPE=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		BXfzM55NKli4ndBCtfN5hB/iRmU=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		3JE0nXJKK0WmL73d8i6/Lu27Sto=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		UtI2Pd/U8nKz9wjn5MtAcxNjIQQ=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		3JE0nXJKK0WmL73d8i6/Lu27Sto=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		BXfzM55NKli4ndBCtfN5hB/iRmU=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		UddMz7kFhBo+r8+nMJCY+MIzITM=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		cb2QHROtqN4bLrwP0PKv+ItBFsA=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		UddMz7kFhBo+r8+nMJCY+MIzITM=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/module.modulemap</key>
		<data>
		V7p5JZN/5LIVFwwoysVXL04EKZk=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/PrivacyInfo.xcprivacy</key>
		<data>
		62HpNLqPh8tKsg+iNP/pSbF2S6M=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Razorpay</key>
		<data>
		SiiFRt9zgHhyz9A3VylnLgraqDY=
		</data>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/_CodeSignature/CodeResources</key>
		<data>
		csfbDxTDAsdrh+MgLjQgDF+allE=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/Razorpay.framework.dSYM/Contents/Info.plist</key>
		<data>
		XkQP8tnX4SAb6IVXpLZZLNPTUpM=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/Razorpay.framework.dSYM/Contents/Resources/DWARF/Razorpay</key>
		<data>
		wLDq5K253LgQKWG1fcHIafBE+4g=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/Razorpay.framework.dSYM/Contents/Resources/Relocations/aarch64/Razorpay.yml</key>
		<data>
		GoyTOffppMpO7hKIeHv6oiQt7VQ=
		</data>
		<key>ios-arm64_x86_64-simulator/dSYMs/Razorpay.framework.dSYM/Contents/Resources/Relocations/x86_64/Razorpay.yml</key>
		<data>
		jcV9TlPLJtPI64vBAXKX4S4wEfY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/Razorpay.framework/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			Bju3rMGH/QCKRwqZN0W5qAnnnS4=
			</data>
			<key>hash2</key>
			<data>
			PHb2ZuQOlqYYHw0P0haHf/aFuDNeeCbe+axDReTr7h0=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			DFcy10+V49NKtjJ4RLpByN10P1k=
			</data>
			<key>hash2</key>
			<data>
			XV+Km0uI0aCCx6b8FFBL8ctnAUqg/+iH2HKwpDJJDns=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			2CirjXkAaTuDLc4zw3TTR01v6hY=
			</data>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			2CirjXkAaTuDLc4zw3TTR01v6hY=
			</data>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0nbduMecK7uoslXovJgcjilOIB8=
			</data>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0nbduMecK7uoslXovJgcjilOIB8=
			</data>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			c8N/593lOrAw9wwSo2JSseKACUI=
			</data>
			<key>hash2</key>
			<data>
			9gbR1Bca1fy1VmXM6YTr9iMj/H9FiV5FH80kOSCS5cA=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			GAbF07uihY0DE3w15+txJ0RlSNw=
			</data>
			<key>hash2</key>
			<data>
			0aP4/XYhE9pTrC1von5mej2B5hZzviQdM4PQj/nojlY=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Ffh5EuOShB1cS/eITOW9HbEzxi4=
			</data>
			<key>hash2</key>
			<data>
			kuyVflk39J/m/Tqjb+YY8Y38/6KfwNjCkTXASxoCEXQ=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6QJ3pwC0+qM8m2WjXuCS4IdsbG0=
			</data>
			<key>hash2</key>
			<data>
			cAbddAE/I0qU6/oKTC87iT4nJ2fd0p9/BUaCe80QOek=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ZnQL9pjNKu2IujGRcLuxvNEwdtg=
			</data>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ZnQL9pjNKu2IujGRcLuxvNEwdtg=
			</data>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eTt4FnKOhvYjwYf1odsqLi0ehZ8=
			</data>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eTt4FnKOhvYjwYf1odsqLi0ehZ8=
			</data>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bHc46UFZ5G9OBiYbYzEHDYGyxtM=
			</data>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bHc46UFZ5G9OBiYbYzEHDYGyxtM=
			</data>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			GuaLaOgcxzpvPP6jj7CE6UW1x54=
			</data>
			<key>hash2</key>
			<data>
			I8p2+VocnQu/7nTAHxHwPLoZ9J+et+y957CgfHi+MQo=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Ucg3HkU3DNFIfnKf6gQlzd/zqTg=
			</data>
			<key>hash2</key>
			<data>
			h05YMT+1oAA6um16sH5KVB2c6toLm935YPmV2d/ZDU8=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/CommonAssets/Razorpay_Logo.png</key>
		<dict>
			<key>hash</key>
			<data>
			C/QPifs1kjcxzxgwUgDFDlLjpRw=
			</data>
			<key>hash2</key>
			<data>
			udRErjoaEwN536HIEl+2sH6KQ0Q2KzlKwLYCkQlBGKE=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/CommonAssets/check_mark.png</key>
		<dict>
			<key>hash</key>
			<data>
			6d4pPz33KoUobYRDPpGmnPiTVMs=
			</data>
			<key>hash2</key>
			<data>
			s+l4gXoMSGUj0xR2eSdXwQTHoyRU5F4+aTSVjO+I8wU=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/CommonAssets/warning.png</key>
		<dict>
			<key>hash</key>
			<data>
			gxArEMTCcu4a+ueYNB3oMoIh48o=
			</data>
			<key>hash2</key>
			<data>
			S7OOo4xdlAEiEgfkEuic4ap4JZlhvtYIwFSHWu034SA=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/EncryptedOtpelf.js</key>
		<dict>
			<key>hash</key>
			<data>
			A893KbMpygzZy6/G1xrQkAudMxw=
			</data>
			<key>hash2</key>
			<data>
			85Tocsg1aPekBFPeMKMV2IruNGVkOyyV6xlbQkQOH3A=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Hash.txt</key>
		<dict>
			<key>hash</key>
			<data>
			gN8QKnsfFYPlPa6NstmuiJETxJ8=
			</data>
			<key>hash2</key>
			<data>
			B20CsAEe8H45a3Ivw23aBTWNU7qGh1JYKb/iAwwaIEQ=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Headers/Razorpay-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			1sJt0oI76TT+xRxnDJcDHj0yJoY=
			</data>
			<key>hash2</key>
			<data>
			h4yyBjfIqeBlfYywLHcWhC1h3+NFGt3VPWVurUB+opM=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			Cw5egKGuBPPchZLu1T2oHdF2hp0=
			</data>
			<key>hash2</key>
			<data>
			bCMBqQ+1ZkZtTVn2jzCsPtk/FzeIz/cvylmt3RjpqCE=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			xHOI7sCquIprmbdzlZtwGUQCs6A=
			</data>
			<key>hash2</key>
			<data>
			5xyc9tRwDzBUpxZSpKr1xqmBweOmGQFcHxDJRaY5iyI=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			RkTTy58lUjQLu32Z1NQ8LwOB3JQ=
			</data>
			<key>hash2</key>
			<data>
			ctUL2RaYrJ2fq/EGaZhLM4Z5Tm6sr2TUOhA+M6xuv1g=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			GLsfx7baeG/t9eh1cdLWIJm6ntM=
			</data>
			<key>hash2</key>
			<data>
			/slvx1O0b7Ovc7hrqj6VjXtuPB4fsNjdPa8j36Nhhuo=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			Saoy8zir8yQPMB9nV1bvzjh+2XQ=
			</data>
			<key>hash2</key>
			<data>
			oSFfWtYrVIocLybHsfFIWaGB2IqGS93FO+F6Z84Ox7k=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			GLsfx7baeG/t9eh1cdLWIJm6ntM=
			</data>
			<key>hash2</key>
			<data>
			/slvx1O0b7Ovc7hrqj6VjXtuPB4fsNjdPa8j36Nhhuo=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			V7p5JZN/5LIVFwwoysVXL04EKZk=
			</data>
			<key>hash2</key>
			<data>
			HN2sZF801VNBYg/HdS8Qv3Qh1iVLaWMgwAMyXqe8iKg=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			62HpNLqPh8tKsg+iNP/pSbF2S6M=
			</data>
			<key>hash2</key>
			<data>
			0GK4q+J5XVD2O8agumlONknk2PSlkzr97y/P84XeCyg=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/Razorpay</key>
		<dict>
			<key>hash</key>
			<data>
			+kvV4qFnfnNKJO2Db7ZYowwdDV8=
			</data>
			<key>hash2</key>
			<data>
			sNnlNz0K1LN+OkCaq0m9JoOJwnZ02FuvHWbRG4NX2Jg=
			</data>
		</dict>
		<key>ios-arm64/Razorpay.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			ZTFX47KHWHzNSjh5RHCK5tl1QVA=
			</data>
			<key>hash2</key>
			<data>
			O5dr1LPi1+Av959oIIJiZQlN9/9M6JiYbtb+1HsBUAk=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/Razorpay.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			XkQP8tnX4SAb6IVXpLZZLNPTUpM=
			</data>
			<key>hash2</key>
			<data>
			OLNR6sYjTf0YMqdPv+JblJwyxT4uJjOI6hHB1hHsI2s=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/Razorpay.framework.dSYM/Contents/Resources/DWARF/Razorpay</key>
		<dict>
			<key>hash</key>
			<data>
			/2ulDkrwdQ4aXdLFMLJc30rAI2o=
			</data>
			<key>hash2</key>
			<data>
			K5qMWjcKAzB8L/7Wb1kWL8STRJpZrgQ6Mv6jd0rls2Y=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/Razorpay.framework.dSYM/Contents/Resources/Relocations/aarch64/Razorpay.yml</key>
		<dict>
			<key>hash</key>
			<data>
			t6aZA2hrYuBDv2vtfWobiXH5OTg=
			</data>
			<key>hash2</key>
			<data>
			t+inKtzdHymttMjoXyu0VkhIT2nkCTyNDMal6OuPgPc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			Bju3rMGH/QCKRwqZN0W5qAnnnS4=
			</data>
			<key>hash2</key>
			<data>
			PHb2ZuQOlqYYHw0P0haHf/aFuDNeeCbe+axDReTr7h0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			DFcy10+V49NKtjJ4RLpByN10P1k=
			</data>
			<key>hash2</key>
			<data>
			XV+Km0uI0aCCx6b8FFBL8ctnAUqg/+iH2HKwpDJJDns=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			2CirjXkAaTuDLc4zw3TTR01v6hY=
			</data>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/MagicXNavController.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			2CirjXkAaTuDLc4zw3TTR01v6hY=
			</data>
			<key>hash2</key>
			<data>
			73JFEP0sFACHxUK6LX1J9W1PrvWwlvOD0X4rzePA5gs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0nbduMecK7uoslXovJgcjilOIB8=
			</data>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/OpinionatedAlertVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0nbduMecK7uoslXovJgcjilOIB8=
			</data>
			<key>hash2</key>
			<data>
			TB5d1qjY2vLAC2ml/4EBTkBy3xnnLZQe6gxyjAuM7Hs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			c8N/593lOrAw9wwSo2JSseKACUI=
			</data>
			<key>hash2</key>
			<data>
			9gbR1Bca1fy1VmXM6YTr9iMj/H9FiV5FH80kOSCS5cA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/QhR-ml-Zo4-view-36U-4g-R9b.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			GAbF07uihY0DE3w15+txJ0RlSNw=
			</data>
			<key>hash2</key>
			<data>
			0aP4/XYhE9pTrC1von5mej2B5hZzviQdM4PQj/nojlY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Ffh5EuOShB1cS/eITOW9HbEzxi4=
			</data>
			<key>hash2</key>
			<data>
			kuyVflk39J/m/Tqjb+YY8Y38/6KfwNjCkTXASxoCEXQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RBq-mH-fUs-view-vI7-59-shd.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6QJ3pwC0+qM8m2WjXuCS4IdsbG0=
			</data>
			<key>hash2</key>
			<data>
			cAbddAE/I0qU6/oKTC87iT4nJ2fd0p9/BUaCe80QOek=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ZnQL9pjNKu2IujGRcLuxvNEwdtg=
			</data>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RazorpayCheckoutVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ZnQL9pjNKu2IujGRcLuxvNEwdtg=
			</data>
			<key>hash2</key>
			<data>
			0/j4lIa+XPXRntjqyuVYoj+ec1JQhaWcgbEA+pTeUS4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eTt4FnKOhvYjwYf1odsqLi0ehZ8=
			</data>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/RazorpayMagicxVC.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eTt4FnKOhvYjwYf1odsqLi0ehZ8=
			</data>
			<key>hash2</key>
			<data>
			9zvXxG2snoKkgapAWomrFN7f+rElxJa0oMXH0TXW1Cw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bHc46UFZ5G9OBiYbYzEHDYGyxtM=
			</data>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/UINavigationController-ODs-ga-9IN.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			bHc46UFZ5G9OBiYbYzEHDYGyxtM=
			</data>
			<key>hash2</key>
			<data>
			F+AoeMvt8cWCXt1AHDFVr11OaNWhhz4gCztJy9+OcjI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/objects-11.0+.nib</key>
		<dict>
			<key>hash</key>
			<data>
			GuaLaOgcxzpvPP6jj7CE6UW1x54=
			</data>
			<key>hash2</key>
			<data>
			I8p2+VocnQu/7nTAHxHwPLoZ9J+et+y957CgfHi+MQo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Checkout.storyboardc/ytB-xX-zk3-view-vP9-Lh-TPB.nib/runtime.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Ucg3HkU3DNFIfnKf6gQlzd/zqTg=
			</data>
			<key>hash2</key>
			<data>
			h05YMT+1oAA6um16sH5KVB2c6toLm935YPmV2d/ZDU8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/CommonAssets/Razorpay_Logo.png</key>
		<dict>
			<key>hash</key>
			<data>
			C/QPifs1kjcxzxgwUgDFDlLjpRw=
			</data>
			<key>hash2</key>
			<data>
			udRErjoaEwN536HIEl+2sH6KQ0Q2KzlKwLYCkQlBGKE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/CommonAssets/check_mark.png</key>
		<dict>
			<key>hash</key>
			<data>
			6d4pPz33KoUobYRDPpGmnPiTVMs=
			</data>
			<key>hash2</key>
			<data>
			s+l4gXoMSGUj0xR2eSdXwQTHoyRU5F4+aTSVjO+I8wU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/CommonAssets/warning.png</key>
		<dict>
			<key>hash</key>
			<data>
			gxArEMTCcu4a+ueYNB3oMoIh48o=
			</data>
			<key>hash2</key>
			<data>
			S7OOo4xdlAEiEgfkEuic4ap4JZlhvtYIwFSHWu034SA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/EncryptedOtpelf.js</key>
		<dict>
			<key>hash</key>
			<data>
			A893KbMpygzZy6/G1xrQkAudMxw=
			</data>
			<key>hash2</key>
			<data>
			85Tocsg1aPekBFPeMKMV2IruNGVkOyyV6xlbQkQOH3A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Hash.txt</key>
		<dict>
			<key>hash</key>
			<data>
			gN8QKnsfFYPlPa6NstmuiJETxJ8=
			</data>
			<key>hash2</key>
			<data>
			B20CsAEe8H45a3Ivw23aBTWNU7qGh1JYKb/iAwwaIEQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Headers/Razorpay-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			aCpEyQx0oDScntCpmjIE+HBAtfA=
			</data>
			<key>hash2</key>
			<data>
			9jrRXk93Z1NasDYbrrEan3C4W5fwyaOfCF6rJLl0AGE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			H31lLYbowzCDA/myCBcG62SKY4k=
			</data>
			<key>hash2</key>
			<data>
			ykCzhFB2/gaumixCfMq5dwdlP/vWxzJXXrpjaVjhb3s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			Z+G0pIvfT/0bBEh3zuxenvvbqcc=
			</data>
			<key>hash2</key>
			<data>
			iqVElaQWXXJ9Mrix1Cx2mUIzewZ/AHLrp/wlDvXf01c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			nxKJAkx2tpl+KnApt9mKYxrFpPE=
			</data>
			<key>hash2</key>
			<data>
			4E3AGF8U7PuuetEZfVwL0BBj1yTnm9J10JhXcmoW8XY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			BXfzM55NKli4ndBCtfN5hB/iRmU=
			</data>
			<key>hash2</key>
			<data>
			HZpCFHFlLD9yzIm2CcmdPtqQXj+AFcQ4ChxOosE8C7E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			3JE0nXJKK0WmL73d8i6/Lu27Sto=
			</data>
			<key>hash2</key>
			<data>
			+pnLxX0Okd9Tu1oEZ5f3aZqfaJQLp42YuJn1Q7HKI8Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			UtI2Pd/U8nKz9wjn5MtAcxNjIQQ=
			</data>
			<key>hash2</key>
			<data>
			QRmfI9e/STxwUaegPez7RkPP/3yfLM7IrbC9AAo0UD0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			3JE0nXJKK0WmL73d8i6/Lu27Sto=
			</data>
			<key>hash2</key>
			<data>
			+pnLxX0Okd9Tu1oEZ5f3aZqfaJQLp42YuJn1Q7HKI8Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			BXfzM55NKli4ndBCtfN5hB/iRmU=
			</data>
			<key>hash2</key>
			<data>
			HZpCFHFlLD9yzIm2CcmdPtqQXj+AFcQ4ChxOosE8C7E=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			UddMz7kFhBo+r8+nMJCY+MIzITM=
			</data>
			<key>hash2</key>
			<data>
			OjQCDXxPMAC1bFjsjYgoeCiZlGpOcHIW8qhPNjw2QmU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			cb2QHROtqN4bLrwP0PKv+ItBFsA=
			</data>
			<key>hash2</key>
			<data>
			TtXfCXLuwBiA/FL/sRWHDvePpWku8MLQvgWfYYgRWdY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/Razorpay.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			UddMz7kFhBo+r8+nMJCY+MIzITM=
			</data>
			<key>hash2</key>
			<data>
			OjQCDXxPMAC1bFjsjYgoeCiZlGpOcHIW8qhPNjw2QmU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			V7p5JZN/5LIVFwwoysVXL04EKZk=
			</data>
			<key>hash2</key>
			<data>
			HN2sZF801VNBYg/HdS8Qv3Qh1iVLaWMgwAMyXqe8iKg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			62HpNLqPh8tKsg+iNP/pSbF2S6M=
			</data>
			<key>hash2</key>
			<data>
			0GK4q+J5XVD2O8agumlONknk2PSlkzr97y/P84XeCyg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/Razorpay</key>
		<dict>
			<key>hash</key>
			<data>
			SiiFRt9zgHhyz9A3VylnLgraqDY=
			</data>
			<key>hash2</key>
			<data>
			09uc0/YKTMwLjJMOWitb51d26HShoIqw63B7Uv8oxrI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Razorpay.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			csfbDxTDAsdrh+MgLjQgDF+allE=
			</data>
			<key>hash2</key>
			<data>
			0EZwCob2p/lgSLpS9LzWlC0+74vMNvfsZ7/9dDsLB+Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/Razorpay.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			XkQP8tnX4SAb6IVXpLZZLNPTUpM=
			</data>
			<key>hash2</key>
			<data>
			OLNR6sYjTf0YMqdPv+JblJwyxT4uJjOI6hHB1hHsI2s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/Razorpay.framework.dSYM/Contents/Resources/DWARF/Razorpay</key>
		<dict>
			<key>hash</key>
			<data>
			wLDq5K253LgQKWG1fcHIafBE+4g=
			</data>
			<key>hash2</key>
			<data>
			jLzK5mGVXSXG58RLx6a9OoLt6FgD6YlozCZBp7iMCb4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/Razorpay.framework.dSYM/Contents/Resources/Relocations/aarch64/Razorpay.yml</key>
		<dict>
			<key>hash</key>
			<data>
			GoyTOffppMpO7hKIeHv6oiQt7VQ=
			</data>
			<key>hash2</key>
			<data>
			tcbkCJ0jPuc9MqZX+5vcuzRB9krp9YcJAYmJTVIt0Lc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/dSYMs/Razorpay.framework.dSYM/Contents/Resources/Relocations/x86_64/Razorpay.yml</key>
		<dict>
			<key>hash</key>
			<data>
			jcV9TlPLJtPI64vBAXKX4S4wEfY=
			</data>
			<key>hash2</key>
			<data>
			BvakpepsmLRGftHmX3bvp+4d99J0t1gZ4FQvouR7MCI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
