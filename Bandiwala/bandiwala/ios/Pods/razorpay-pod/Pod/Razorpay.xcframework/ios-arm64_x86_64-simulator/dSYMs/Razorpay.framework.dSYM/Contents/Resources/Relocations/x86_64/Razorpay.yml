---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/DerivedData/iphonesimulator/Build/Products/Release-iphonesimulator/Razorpay.framework/Razorpay'
relocations:
  - { offset: 0xBDEE7, size: 0x8, addend: 0x0, symName: _RazorpayVersionString, symObjAddr: 0x0, symBinAddr: 0x65400, symSize: 0x0 }
  - { offset: 0xBDF1C, size: 0x8, addend: 0x0, symName: _RazorpayVersionNumber, symObjAddr: 0x30, symBinAddr: 0x65430, symSize: 0x0 }
  - { offset: 0xBDF81, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC14sharedInstanceACvpZ', symObjAddr: 0x10F58, symBinAddr: 0xB6330, symSize: 0x0 }
  - { offset: 0xBE09A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC14sharedInstance_WZ', symObjAddr: 0x190, symBinAddr: 0x2250, symSize: 0x40 }
  - { offset: 0xBE131, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCMa', symObjAddr: 0x410, symBinAddr: 0x24D0, symSize: 0x20 }
  - { offset: 0xBE202, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCSQWb', symObjAddr: 0x910, symBinAddr: 0x29A0, symSize: 0x20 }
  - { offset: 0xBE216, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCs25ExpressibleByArrayLiteralPWb', symObjAddr: 0x930, symBinAddr: 0x29C0, symSize: 0x20 }
  - { offset: 0xBE22A, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCSYWb', symObjAddr: 0x980, symBinAddr: 0x2A10, symSize: 0x20 }
  - { offset: 0xBE23E, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCs0E7AlgebraPWb', symObjAddr: 0x9A0, symBinAddr: 0x2A30, symSize: 0x20 }
  - { offset: 0xBE2D6, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0xC00, symBinAddr: 0x2C80, symSize: 0x40 }
  - { offset: 0xBE2EA, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xC40, symBinAddr: 0x2CC0, symSize: 0x40 }
  - { offset: 0xBE2FE, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0xC80, symBinAddr: 0x2D00, symSize: 0x30 }
  - { offset: 0xBE312, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0xCD0, symBinAddr: 0x2D50, symSize: 0x20 }
  - { offset: 0xBE326, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0xCF0, symBinAddr: 0x2D70, symSize: 0x20 }
  - { offset: 0xBE33A, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSQWb', symObjAddr: 0xD10, symBinAddr: 0x2D90, symSize: 0x20 }
  - { offset: 0xBE34E, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_4, symObjAddr: 0xD30, symBinAddr: 0x2DB0, symSize: 0x10 }
  - { offset: 0xBE362, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0xD40, symBinAddr: 0x2DC0, symSize: 0x10 }
  - { offset: 0xBE376, size: 0x8, addend: 0x0, symName: '_$sSo11sockaddr_inVwet', symObjAddr: 0xD50, symBinAddr: 0x2DD0, symSize: 0x20 }
  - { offset: 0xBE38A, size: 0x8, addend: 0x0, symName: '_$sSo11sockaddr_inVwst', symObjAddr: 0xD70, symBinAddr: 0x2DF0, symSize: 0x30 }
  - { offset: 0xBE425, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo38UIApplicationOpenExternalURLOptionsKeya_Tgmq5', symObjAddr: 0x470, symBinAddr: 0x2530, symSize: 0x80 }
  - { offset: 0xBE4BC, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo38UIApplicationOpenExternalURLOptionsKeya_Tgmq5', symObjAddr: 0x4F0, symBinAddr: 0x25B0, symSize: 0x80 }
  - { offset: 0xBE562, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x590, symBinAddr: 0x2640, symSize: 0x10 }
  - { offset: 0xBE5A6, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACPxycfCTW', symObjAddr: 0x5B0, symBinAddr: 0x2660, symSize: 0x10 }
  - { offset: 0xBE5EF, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP5unionyxxnFTW', symObjAddr: 0x5C0, symBinAddr: 0x2670, symSize: 0x10 }
  - { offset: 0xBE68C, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP12intersectionyxxFTW', symObjAddr: 0x5D0, symBinAddr: 0x2680, symSize: 0x10 }
  - { offset: 0xBE721, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP19symmetricDifferenceyxxnFTW', symObjAddr: 0x5E0, symBinAddr: 0x2690, symSize: 0x10 }
  - { offset: 0xBE7BE, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6insertySb8inserted_7ElementQz17memberAfterInserttAHnFTW', symObjAddr: 0x5F0, symBinAddr: 0x26A0, symSize: 0x30 }
  - { offset: 0xBE919, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6removey7ElementQzSgAGFTW', symObjAddr: 0x620, symBinAddr: 0x26D0, symSize: 0x30 }
  - { offset: 0xBEA16, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6update4with7ElementQzSgAHn_tFTW', symObjAddr: 0x650, symBinAddr: 0x2700, symSize: 0x20 }
  - { offset: 0xBEB02, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP9formUnionyyxnFTW', symObjAddr: 0x670, symBinAddr: 0x2720, symSize: 0x10 }
  - { offset: 0xBEB40, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP16formIntersectionyyxFTW', symObjAddr: 0x680, symBinAddr: 0x2730, symSize: 0x10 }
  - { offset: 0xBEB7E, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP23formSymmetricDifferenceyyxnFTW', symObjAddr: 0x690, symBinAddr: 0x2740, symSize: 0x10 }
  - { offset: 0xBEBCB, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP11subtractingyxxFTW', symObjAddr: 0x6A0, symBinAddr: 0x2750, symSize: 0x10 }
  - { offset: 0xBEC74, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP8isSubset2ofSbx_tFTW', symObjAddr: 0x6B0, symBinAddr: 0x2760, symSize: 0x20 }
  - { offset: 0xBECEB, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP10isDisjoint4withSbx_tFTW', symObjAddr: 0x6D0, symBinAddr: 0x2780, symSize: 0x10 }
  - { offset: 0xBEDA3, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP10isSuperset2ofSbx_tFTW', symObjAddr: 0x6E0, symBinAddr: 0x2790, symSize: 0x20 }
  - { offset: 0xBEE3D, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP7isEmptySbvgTW', symObjAddr: 0x700, symBinAddr: 0x27B0, symSize: 0x10 }
  - { offset: 0xBEEA5, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACPyxqd__ncSTRd__7ElementQyd__AERtzlufCTW', symObjAddr: 0x710, symBinAddr: 0x27C0, symSize: 0x20 }
  - { offset: 0xBEEC1, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP8subtractyyxFTW', symObjAddr: 0x730, symBinAddr: 0x27E0, symSize: 0x10 }
  - { offset: 0xBEF3D, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromF1C_6resulty01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x770, symBinAddr: 0x2820, symSize: 0x10 }
  - { offset: 0xBEF59, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromF1C_6resultSb01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x780, symBinAddr: 0x2830, symSize: 0x10 }
  - { offset: 0xBEF84, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromF1Cyx01_F5CTypeQzSgFZTW', symObjAddr: 0x790, symBinAddr: 0x2840, symSize: 0x40 }
  - { offset: 0xBF002, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x830, symBinAddr: 0x28E0, symSize: 0x30 }
  - { offset: 0xBF086, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x860, symBinAddr: 0x2910, symSize: 0x60 }
  - { offset: 0xBF104, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs25ExpressibleByArrayLiteralSCsACP05arrayG0x0fG7ElementQzd_tcfCTW', symObjAddr: 0x8E0, symBinAddr: 0x2970, symSize: 0x30 }
  - { offset: 0xBF153, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x9C0, symBinAddr: 0x2A50, symSize: 0x80 }
  - { offset: 0xBF215, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0xAB0, symBinAddr: 0x2B30, symSize: 0x70 }
  - { offset: 0xBF26D, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xB20, symBinAddr: 0x2BA0, symSize: 0xE0 }
  - { offset: 0xBF3F6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCACyc33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7Llfc', symObjAddr: 0x0, symBinAddr: 0x20C0, symSize: 0x190 }
  - { offset: 0xBF4F5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC013latestFetchedC033_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LLSDySSypGvW', symObjAddr: 0x1D0, symBinAddr: 0x2290, symSize: 0x1A0 }
  - { offset: 0xBF5BA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC05fetchC0yyySDySSypGSgcFyAA0bC13FetchResponseO_AFtcfU_', symObjAddr: 0x370, symBinAddr: 0x2430, symSize: 0x70 }
  - { offset: 0xBF656, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCfD', symObjAddr: 0x3E0, symBinAddr: 0x24A0, symSize: 0x30 }
  - { offset: 0xBF75C, size: 0x8, addend: 0x0, symName: '_$sSo21CLAuthorizationStatusVSYSCSY8rawValue03RawD0QzvgTW', symObjAddr: 0x580, symBinAddr: 0x2630, symSize: 0x10 }
  - { offset: 0xBF7C8, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCsACP8rawValuex03RawG0Qz_tcfCTW', symObjAddr: 0x5A0, symBinAddr: 0x2650, symSize: 0x10 }
  - { offset: 0xBF7E9, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVSYSCSY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x740, symBinAddr: 0x27F0, symSize: 0x10 }
  - { offset: 0xBF80B, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSYSCSY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0xA50, symBinAddr: 0x2AD0, symSize: 0x40 }
  - { offset: 0xBF834, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSYSCSY8rawValue03RawG0QzvgTW', symObjAddr: 0xA90, symBinAddr: 0x2B10, symSize: 0x20 }
  - { offset: 0xBF9D3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LL_WZ', symObjAddr: 0x0, symBinAddr: 0x2EB0, symSize: 0x30 }
  - { offset: 0xBF9F7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LLACvpZ', symObjAddr: 0xA60, symBinAddr: 0xA2170, symSize: 0x0 }
  - { offset: 0xBFB01, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LL_WZ', symObjAddr: 0x0, symBinAddr: 0x2EB0, symSize: 0x30 }
  - { offset: 0xBFB3F, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIeghggg_So6NSDataCSgAGSo7NSErrorCSgIeyBhyyy_TR', symObjAddr: 0x290, symBinAddr: 0x3140, symSize: 0xF0 }
  - { offset: 0xBFB57, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsCMa', symObjAddr: 0x3B0, symBinAddr: 0x3260, symSize: 0x20 }
  - { offset: 0xBFB7C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFyAKSg_ANs5Error_pSgtYbcfU_TA', symObjAddr: 0x740, symBinAddr: 0x35F0, symSize: 0x80 }
  - { offset: 0xBFB90, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x7C0, symBinAddr: 0x3670, symSize: 0x20 }
  - { offset: 0xBFBA4, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x7E0, symBinAddr: 0x3690, symSize: 0x10 }
  - { offset: 0xBFBB8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x7F0, symBinAddr: 0x36A0, symSize: 0x20 }
  - { offset: 0xBFBCC, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x810, symBinAddr: 0x36C0, symSize: 0x50 }
  - { offset: 0xBFBE0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x860, symBinAddr: 0x3710, symSize: 0x50 }
  - { offset: 0xBFBF4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC16getStatusCodeFor10urlRequest14requestHandlery10Foundation10URLRequestV_ySo13NSURLResponseCSgctFyAG4DataVSg_ALs5Error_pSgtYbcfU_TA', symObjAddr: 0xA00, symBinAddr: 0x38B0, symSize: 0x30 }
  - { offset: 0xBFD3D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFyAKSg_ANs5Error_pSgtYbcfU_', symObjAddr: 0x30, symBinAddr: 0x2EE0, symSize: 0x260 }
  - { offset: 0xBFE32, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsCfD', symObjAddr: 0x380, symBinAddr: 0x3230, symSize: 0x30 }
  - { offset: 0xBFF08, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC14sharedInstanceACyFZTf4d_g', symObjAddr: 0x3D0, symBinAddr: 0x3280, symSize: 0x110 }
  - { offset: 0xBFF7E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFTf4nnnd_n', symObjAddr: 0x4E0, symBinAddr: 0x3390, symSize: 0x1D0 }
  - { offset: 0xBFFE6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC16getStatusCodeFor10urlRequest14requestHandlery10Foundation10URLRequestV_ySo13NSURLResponseCSgctFTf4nnd_n', symObjAddr: 0x8B0, symBinAddr: 0x3760, symSize: 0x130 }
  - { offset: 0xC0219, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11isSetupDone33_97E3ACE2739C3B14C03A312B86CD9A70LLSbvpZ', symObjAddr: 0x1C48, symBinAddr: 0xA2248, symSize: 0x0 }
  - { offset: 0xC0233, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC14sharedInstance33_97E3ACE2739C3B14C03A312B86CD9A70LLACvpZ', symObjAddr: 0x1C50, symBinAddr: 0xA2250, symSize: 0x0 }
  - { offset: 0xC03B2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19networkConnectivityAA08InternetE0OvpZ', symObjAddr: 0x1C58, symBinAddr: 0xA2258, symSize: 0x0 }
  - { offset: 0xC03CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC14sharedInstance33_97E3ACE2739C3B14C03A312B86CD9A70LL_WZ', symObjAddr: 0x0, symBinAddr: 0x3900, symSize: 0x40 }
  - { offset: 0xC06E3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCMa', symObjAddr: 0x13D0, symBinAddr: 0x4CD0, symSize: 0x20 }
  - { offset: 0xC06F7, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x13F0, symBinAddr: 0x4CF0, symSize: 0x20 }
  - { offset: 0xC076E, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x1B40, symBinAddr: 0x5400, symSize: 0x30 }
  - { offset: 0xC0782, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVWOr', symObjAddr: 0x1B70, symBinAddr: 0x5430, symSize: 0x40 }
  - { offset: 0xC0796, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x1C10, symBinAddr: 0x54A0, symSize: 0x30 }
  - { offset: 0xC0A1C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCACyc33_97E3ACE2739C3B14C03A312B86CD9A70Llfc', symObjAddr: 0x40, symBinAddr: 0x3940, symSize: 0xD0 }
  - { offset: 0xC0AED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19resetPaymentSessionyyF', symObjAddr: 0x110, symBinAddr: 0x3A10, symSize: 0x120 }
  - { offset: 0xC0B7A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC10trackEvent_16havingProperties02cxG0yAA0bE0O_SDys11AnyHashableVypGAKSgtF', symObjAddr: 0x230, symBinAddr: 0x3B30, symSize: 0x140 }
  - { offset: 0xC0C59, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11addProperty9havingKey03andE0ySS_AA0bE0CtF', symObjAddr: 0x370, symBinAddr: 0x3C70, symSize: 0xB0 }
  - { offset: 0xC0CEC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11reportError_11havingLevel16andCustomMessage11errorSourceySo7NSErrorCSg_AA0aeG0OS2SSgtF', symObjAddr: 0x420, symBinAddr: 0x3D20, symSize: 0xA60 }
  - { offset: 0xC110A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC34removeReachabilityNotifierObserveryyF', symObjAddr: 0xE80, symBinAddr: 0x4780, symSize: 0x180 }
  - { offset: 0xC11B4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC31setReachabilityNotifierObserveryyF', symObjAddr: 0x1000, symBinAddr: 0x4900, symSize: 0x240 }
  - { offset: 0xC1284, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19reachabilityChanged4notey10Foundation12NotificationV_tF', symObjAddr: 0x1240, symBinAddr: 0x4B40, symSize: 0xE0 }
  - { offset: 0xC1319, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19reachabilityChanged4notey10Foundation12NotificationV_tFTo', symObjAddr: 0x1320, symBinAddr: 0x4C20, symSize: 0x80 }
  - { offset: 0xC1341, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCfD', symObjAddr: 0x13A0, symBinAddr: 0x4CA0, symSize: 0x30 }
  - { offset: 0xC13B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC21generateCXErrorObject7cxErrorSDySSypGAA0dE8Protocol_p_tFTf4en_nAA0D0V_TB5Tf4nd_n', symObjAddr: 0x1450, symBinAddr: 0x4D10, symSize: 0x6B0 }
  - { offset: 0xC1936, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x54D0, symSize: 0x10 }
  - { offset: 0xC1951, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x54D0, symSize: 0x10 }
  - { offset: 0xC19C4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH9hashValueSivgTW', symObjAddr: 0x10, symBinAddr: 0x54E0, symSize: 0x80 }
  - { offset: 0xC1A5F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x90, symBinAddr: 0x5560, symSize: 0x50 }
  - { offset: 0xC1AC6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE0, symBinAddr: 0x55B0, symSize: 0x70 }
  - { offset: 0xC1B5A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs28CustomDebugStringConvertibleAAsAHP16debugDescriptionSSvgTW', symObjAddr: 0x2F0, symBinAddr: 0x57C0, symSize: 0x20 }
  - { offset: 0xC1B76, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs23CustomStringConvertibleAAsAHP11descriptionSSvgTW', symObjAddr: 0x310, symBinAddr: 0x57E0, symSize: 0x20 }
  - { offset: 0xC1C03, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x5F0, symBinAddr: 0x5AC0, symSize: 0x30 }
  - { offset: 0xC1CA0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0x6D0, symBinAddr: 0x5B90, symSize: 0x20 }
  - { offset: 0xC1CBC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0x6F0, symBinAddr: 0x5BB0, symSize: 0x20 }
  - { offset: 0xC20A1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCMa', symObjAddr: 0x750, symBinAddr: 0x5C10, symSize: 0x20 }
  - { offset: 0xC20B5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VwCP', symObjAddr: 0x850, symBinAddr: 0x5D10, symSize: 0x30 }
  - { offset: 0xC20C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwxx', symObjAddr: 0x880, symBinAddr: 0x5D40, symSize: 0x30 }
  - { offset: 0xC20DD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwcp', symObjAddr: 0x8B0, symBinAddr: 0x5D70, symSize: 0x40 }
  - { offset: 0xC20F1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwca', symObjAddr: 0x8F0, symBinAddr: 0x5DB0, symSize: 0x60 }
  - { offset: 0xC2105, size: 0x8, addend: 0x0, symName: ___swift_memcpy32_8, symObjAddr: 0x950, symBinAddr: 0x5E10, symSize: 0x20 }
  - { offset: 0xC2119, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwta', symObjAddr: 0x970, symBinAddr: 0x5E30, symSize: 0x50 }
  - { offset: 0xC212D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwet', symObjAddr: 0x9C0, symBinAddr: 0x5E80, symSize: 0x40 }
  - { offset: 0xC2141, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwst', symObjAddr: 0xA00, symBinAddr: 0x5EC0, symSize: 0x50 }
  - { offset: 0xC2155, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VMa', symObjAddr: 0xA50, symBinAddr: 0x5F10, symSize: 0x10 }
  - { offset: 0xC2169, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs0F3KeyAAWl', symObjAddr: 0xC70, symBinAddr: 0x60C0, symSize: 0x30 }
  - { offset: 0xC217D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VAESeAAWl', symObjAddr: 0xCD0, symBinAddr: 0x60F0, symSize: 0x30 }
  - { offset: 0xC2191, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs0F3KeyAAWl', symObjAddr: 0xE80, symBinAddr: 0x62A0, symSize: 0x30 }
  - { offset: 0xC21A5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VAESEAAWl', symObjAddr: 0xEB0, symBinAddr: 0x62D0, symSize: 0x30 }
  - { offset: 0xC21B9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCACSEAAWl', symObjAddr: 0xEE0, symBinAddr: 0x6300, symSize: 0x30 }
  - { offset: 0xC21CD, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0xF60, symBinAddr: 0x6330, symSize: 0x10 }
  - { offset: 0xC21E1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwet', symObjAddr: 0xF80, symBinAddr: 0x6340, symSize: 0x80 }
  - { offset: 0xC21F5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwst', symObjAddr: 0x1000, symBinAddr: 0x63C0, symSize: 0xD0 }
  - { offset: 0xC2209, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwug', symObjAddr: 0x10D0, symBinAddr: 0x6490, symSize: 0x10 }
  - { offset: 0xC221D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwup', symObjAddr: 0x10E0, symBinAddr: 0x64A0, symSize: 0x10 }
  - { offset: 0xC2231, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwui', symObjAddr: 0x10F0, symBinAddr: 0x64B0, symSize: 0x10 }
  - { offset: 0xC2245, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOMa', symObjAddr: 0x1100, symBinAddr: 0x64C0, symSize: 0x10 }
  - { offset: 0xC2259, size: 0x8, addend: 0x0, symName: ___swift_memcpy0_1, symObjAddr: 0x1110, symBinAddr: 0x64D0, symSize: 0x10 }
  - { offset: 0xC226D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwet', symObjAddr: 0x1120, symBinAddr: 0x64E0, symSize: 0x50 }
  - { offset: 0xC2281, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwst', symObjAddr: 0x1170, symBinAddr: 0x6530, symSize: 0xA0 }
  - { offset: 0xC2295, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwug', symObjAddr: 0x1210, symBinAddr: 0x65D0, symSize: 0x10 }
  - { offset: 0xC22A9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOMa', symObjAddr: 0x1240, symBinAddr: 0x65E0, symSize: 0x10 }
  - { offset: 0xC22BD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASQWb', symObjAddr: 0x1250, symBinAddr: 0x65F0, symSize: 0x10 }
  - { offset: 0xC22D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFSQAAWl', symObjAddr: 0x1260, symBinAddr: 0x6600, symSize: 0x30 }
  - { offset: 0xC22E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASQWb', symObjAddr: 0x1290, symBinAddr: 0x6630, symSize: 0x10 }
  - { offset: 0xC22F9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGSQAAWl', symObjAddr: 0x12A0, symBinAddr: 0x6640, symSize: 0x30 }
  - { offset: 0xC230D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x12D0, symBinAddr: 0x6670, symSize: 0x10 }
  - { offset: 0xC2321, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x12E0, symBinAddr: 0x6680, symSize: 0x30 }
  - { offset: 0xC2335, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x1310, symBinAddr: 0x66B0, symSize: 0x10 }
  - { offset: 0xC2349, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs23CustomStringConvertibleAAWl', symObjAddr: 0x1320, symBinAddr: 0x66C0, symSize: 0x30 }
  - { offset: 0xC235D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x1350, symBinAddr: 0x66F0, symSize: 0x10 }
  - { offset: 0xC2371, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x1360, symBinAddr: 0x6700, symSize: 0x30 }
  - { offset: 0xC2385, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x1390, symBinAddr: 0x6730, symSize: 0x10 }
  - { offset: 0xC2399, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x13A0, symBinAddr: 0x6740, symSize: 0x30 }
  - { offset: 0xC2402, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSYAASY8rawValuexSg03RawI0Qz_tcfCTW', symObjAddr: 0x150, symBinAddr: 0x5620, symSize: 0x80 }
  - { offset: 0xC244F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSYAASY8rawValue03RawI0QzvgTW', symObjAddr: 0x1D0, symBinAddr: 0x56A0, symSize: 0x40 }
  - { offset: 0xC248A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP11stringValueSSvgTW', symObjAddr: 0x210, symBinAddr: 0x56E0, symSize: 0x40 }
  - { offset: 0xC24DA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP11stringValuexSgSS_tcfCTW', symObjAddr: 0x250, symBinAddr: 0x5720, symSize: 0x80 }
  - { offset: 0xC253E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP8intValueSiSgvgTW', symObjAddr: 0x2D0, symBinAddr: 0x57A0, symSize: 0x10 }
  - { offset: 0xC2552, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP8intValuexSgSi_tcfCTW', symObjAddr: 0x2E0, symBinAddr: 0x57B0, symSize: 0x10 }
  - { offset: 0xC2566, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V6encode2toys7Encoder_p_tKF', symObjAddr: 0x330, symBinAddr: 0x5800, symSize: 0x110 }
  - { offset: 0xC25AA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x440, symBinAddr: 0x5910, symSize: 0x30 }
  - { offset: 0xC25D3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x470, symBinAddr: 0x5940, symSize: 0x20 }
  - { offset: 0xC261E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10jsonStringSSSgyF', symObjAddr: 0x490, symBinAddr: 0x5960, symSize: 0x100 }
  - { offset: 0xC2675, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x590, symBinAddr: 0x5A60, symSize: 0x10 }
  - { offset: 0xC26B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH9hashValueSivgTW', symObjAddr: 0x5A0, symBinAddr: 0x5A70, symSize: 0x30 }
  - { offset: 0xC2777, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x5D0, symBinAddr: 0x5AA0, symSize: 0x20 }
  - { offset: 0xC27D3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0x620, symBinAddr: 0x5AF0, symSize: 0x20 }
  - { offset: 0xC2806, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0x640, symBinAddr: 0x5B10, symSize: 0x70 }
  - { offset: 0xC2858, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0x6C0, symBinAddr: 0x5B80, symSize: 0x10 }
  - { offset: 0xC2895, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCfD', symObjAddr: 0x710, symBinAddr: 0x5BD0, symSize: 0x40 }
  - { offset: 0xC28D6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC6encode2toys7Encoder_p_tKF', symObjAddr: 0x770, symBinAddr: 0x5C30, symSize: 0xE0 }
  - { offset: 0xC2907, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC4fromACs7Decoder_p_tKcfc', symObjAddr: 0xA60, symBinAddr: 0x5F20, symSize: 0x130 }
  - { offset: 0xC2945, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0xB90, symBinAddr: 0x6050, symSize: 0x50 }
  - { offset: 0xC296E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0xBE0, symBinAddr: 0x60A0, symSize: 0x20 }
  - { offset: 0xC2982, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V4fromAEs7Decoder_p_tKcfCTf4nd_n', symObjAddr: 0xD00, symBinAddr: 0x6120, symSize: 0x180 }
  - { offset: 0xC2BE3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCMa', symObjAddr: 0xE0, symBinAddr: 0x6850, symSize: 0x20 }
  - { offset: 0xC2C0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCfETo', symObjAddr: 0x1B0, symBinAddr: 0x6920, symSize: 0x20 }
  - { offset: 0xC2C3C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24UPITurboPrefetchProtocolPAAE26prefetchAndLinkUpiAccounts27linkAccountWithUPIPinNotSet0J14ActionDelegateySb_yptF', symObjAddr: 0x1D0, symBinAddr: 0x6940, symSize: 0x50 }
  - { offset: 0xC2C95, size: 0x8, addend: 0x0, symName: '_$s8Razorpay30UPITurboPrefetchWithUIProtocolPAAE026prefetchAndLinkUpiAccountsD2UI17completionHandleryyypSg_AFtc_tF', symObjAddr: 0x250, symBinAddr: 0x6990, symSize: 0x90 }
  - { offset: 0xC2CDE, size: 0x8, addend: 0x0, symName: '_$sypSgAAIegnn_yXlSgABIeyByy_TR', symObjAddr: 0x2E0, symBinAddr: 0x6A20, symSize: 0xC0 }
  - { offset: 0xC2CF6, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3A0, symBinAddr: 0x6AE0, symSize: 0x20 }
  - { offset: 0xC2D0A, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3C0, symBinAddr: 0x6B00, symSize: 0x10 }
  - { offset: 0xC2D1F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenSSvg', symObjAddr: 0x0, symBinAddr: 0x6770, symSize: 0x30 }
  - { offset: 0xC2D72, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenACSS_tcfC', symObjAddr: 0x30, symBinAddr: 0x67A0, symSize: 0x60 }
  - { offset: 0xC2DB1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenACSS_tcfc', symObjAddr: 0x90, symBinAddr: 0x6800, symSize: 0x50 }
  - { offset: 0xC2DDA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfC', symObjAddr: 0x100, symBinAddr: 0x6870, symSize: 0x20 }
  - { offset: 0xC2DF4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfc', symObjAddr: 0x120, symBinAddr: 0x6890, symSize: 0x30 }
  - { offset: 0xC2E4D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfcTo', symObjAddr: 0x150, symBinAddr: 0x68C0, symSize: 0x30 }
  - { offset: 0xC2EAC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCfD', symObjAddr: 0x180, symBinAddr: 0x68F0, symSize: 0x30 }
  - { offset: 0xC30C0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x6BB0, symSize: 0x10 }
  - { offset: 0xC31A9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsCMa', symObjAddr: 0x110, symBinAddr: 0x6CC0, symSize: 0x20 }
  - { offset: 0xC31BD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwet', symObjAddr: 0x150, symBinAddr: 0x6CE0, symSize: 0x80 }
  - { offset: 0xC31D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwst', symObjAddr: 0x1D0, symBinAddr: 0x6D60, symSize: 0xD0 }
  - { offset: 0xC31E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwui', symObjAddr: 0x2C0, symBinAddr: 0x6E30, symSize: 0x10 }
  - { offset: 0xC31F9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOMa', symObjAddr: 0x2D0, symBinAddr: 0x6E40, symSize: 0x10 }
  - { offset: 0xC320D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASQWb', symObjAddr: 0x2E0, symBinAddr: 0x6E50, symSize: 0x10 }
  - { offset: 0xC3221, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOAESQAAWl', symObjAddr: 0x2F0, symBinAddr: 0x6E60, symSize: 0x30 }
  - { offset: 0xC3270, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x70, symBinAddr: 0x6C20, symSize: 0x40 }
  - { offset: 0xC32F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP7_domainSSvgTW', symObjAddr: 0xB0, symBinAddr: 0x6C60, symSize: 0x10 }
  - { offset: 0xC3314, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP5_codeSivgTW', symObjAddr: 0xC0, symBinAddr: 0x6C70, symSize: 0x10 }
  - { offset: 0xC3330, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP9_userInfoyXlSgvgTW', symObjAddr: 0xD0, symBinAddr: 0x6C80, symSize: 0x10 }
  - { offset: 0xC334C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0xE0, symBinAddr: 0x6C90, symSize: 0x10 }
  - { offset: 0xC339C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x6BB0, symSize: 0x10 }
  - { offset: 0xC3410, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH9hashValueSivgTW', symObjAddr: 0x10, symBinAddr: 0x6BC0, symSize: 0x40 }
  - { offset: 0xC34D7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x50, symBinAddr: 0x6C00, symSize: 0x20 }
  - { offset: 0xC3534, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsCfD', symObjAddr: 0xF0, symBinAddr: 0x6CA0, symSize: 0x20 }
  - { offset: 0xC36F7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14sharedInstance33_CAD7F1E8D00B6203AC8D8943A778DF45LLACvpZ', symObjAddr: 0x36B8, symBinAddr: 0xA2648, symSize: 0x0 }
  - { offset: 0xC393B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14sharedInstance33_CAD7F1E8D00B6203AC8D8943A778DF45LL_WZ', symObjAddr: 0x1A0, symBinAddr: 0x7030, symSize: 0x30 }
  - { offset: 0xC3B87, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCfETo', symObjAddr: 0x2430, symBinAddr: 0x92C0, symSize: 0x80 }
  - { offset: 0xC3BB6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCMa', symObjAddr: 0x24B0, symBinAddr: 0x9340, symSize: 0x20 }
  - { offset: 0xC3BCA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLCMa', symObjAddr: 0x24D0, symBinAddr: 0x9360, symSize: 0x20 }
  - { offset: 0xC3BDE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLCMa', symObjAddr: 0x24F0, symBinAddr: 0x9380, symSize: 0x20 }
  - { offset: 0xC3D89, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x3590, symBinAddr: 0xA390, symSize: 0x10 }
  - { offset: 0xC3D9D, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x35A0, symBinAddr: 0xA3A0, symSize: 0x20 }
  - { offset: 0xC3DB1, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x35C0, symBinAddr: 0xA3C0, symSize: 0x10 }
  - { offset: 0xC3E22, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUserrS0C_So08WKScriptF0CtFTo', symObjAddr: 0x0, symBinAddr: 0x6E90, symSize: 0x60 }
  - { offset: 0xC3E53, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUsersT0C_So08WKScriptG0CtFTo', symObjAddr: 0xA0, symBinAddr: 0x6F30, symSize: 0x60 }
  - { offset: 0xC4144, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCACyc33_CAD7F1E8D00B6203AC8D8943A778DF45Llfc', symObjAddr: 0x1D0, symBinAddr: 0x7060, symSize: 0x2E0 }
  - { offset: 0xC41F7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCACyc33_CAD7F1E8D00B6203AC8D8943A778DF45LlfcTo', symObjAddr: 0x4B0, symBinAddr: 0x7340, symSize: 0x20 }
  - { offset: 0xC4242, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC12setupBridges33_CAD7F1E8D00B6203AC8D8943A778DF45LLyyF', symObjAddr: 0x4D0, symBinAddr: 0x7360, symSize: 0x220 }
  - { offset: 0xC439A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14setPaymentDatayySDys11AnyHashableVypGF', symObjAddr: 0x6F0, symBinAddr: 0x7580, symSize: 0x410 }
  - { offset: 0xC45C8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0xB00, symBinAddr: 0x7990, symSize: 0x270 }
  - { offset: 0xC46FA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14getSDKMetadata33_CAD7F1E8D00B6203AC8D8943A778DF45LLyyF', symObjAddr: 0xD70, symBinAddr: 0x7C00, symSize: 0x14A0 }
  - { offset: 0xC4CD4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC5closeyyF', symObjAddr: 0x2210, symBinAddr: 0x90A0, symSize: 0x1F0 }
  - { offset: 0xC4D2C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCfD', symObjAddr: 0x2400, symBinAddr: 0x9290, symSize: 0x30 }
  - { offset: 0xC4DC2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUserrS0C_So08WKScriptF0CtFTf4dnd_n', symObjAddr: 0x2510, symBinAddr: 0x93A0, symSize: 0x520 }
  - { offset: 0xC50C4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUsersT0C_So08WKScriptG0CtFTf4dnd_n', symObjAddr: 0x2A30, symBinAddr: 0x98C0, symSize: 0x510 }
  - { offset: 0xC539A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC15initWithWebView_14andMerchantKeyySo05WKWebG0C_SSSgtFZTf4nnd_n', symObjAddr: 0x2FD0, symBinAddr: 0x9DD0, symSize: 0x3E0 }
  - { offset: 0xC54D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFTf4dn_n', symObjAddr: 0x33B0, symBinAddr: 0xA1B0, symSize: 0x1B0 }
  - { offset: 0xC56CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14sharedInstanceACvpZ', symObjAddr: 0x1B230, symBinAddr: 0xB6338, symSize: 0x0 }
  - { offset: 0xC58CE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C10lumberjack33_C2103DC0A7BEA85E64BF4B6378FF9B75LLAA10LumberjackCSgvpZ', symObjAddr: 0x4C68, symBinAddr: 0xA2710, symSize: 0x0 }
  - { offset: 0xC5925, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE8URIEventABvpZ', symObjAddr: 0x1B238, symBinAddr: 0xB6340, symSize: 0x0 }
  - { offset: 0xC5933, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14sharedInstance_WZ', symObjAddr: 0x0, symBinAddr: 0xA400, symSize: 0x30 }
  - { offset: 0xC5CFF, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x15D0, symBinAddr: 0xB9D0, symSize: 0x30 }
  - { offset: 0xC5DBA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CfETo', symObjAddr: 0x1AA0, symBinAddr: 0xBEA0, symSize: 0x130 }
  - { offset: 0xC5DE9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CMa', symObjAddr: 0x1BD0, symBinAddr: 0xBFD0, symSize: 0x20 }
  - { offset: 0xC5DFD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14paymentSuccess3str16dictVerificationySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x1BF0, symBinAddr: 0xBFF0, symSize: 0x120 }
  - { offset: 0xC5E99, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14paymentFailure4code11description4datays5Int32VSg_SSSgSDys11AnyHashableVypGSgtF', symObjAddr: 0x1D10, symBinAddr: 0xC110, symSize: 0x200 }
  - { offset: 0xC60D3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFySaySo19WKWebsiteDataRecordCGYbScMYccfU_', symObjAddr: 0x1F10, symBinAddr: 0xC310, symSize: 0x3F0 }
  - { offset: 0xC6319, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFySaySo19WKWebsiteDataRecordCGYbScMYccfU_yAGXEfU_yyYbcfU_', symObjAddr: 0x2300, symBinAddr: 0xC700, symSize: 0x10 }
  - { offset: 0xC6334, size: 0x8, addend: 0x0, symName: '_$sSaySo19WKWebsiteDataRecordCGIeghg_So7NSArrayCIeyBhy_TR', symObjAddr: 0x2310, symBinAddr: 0xC710, symSize: 0x60 }
  - { offset: 0xC634C, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE8URIEvent_WZ', symObjAddr: 0x2370, symBinAddr: 0xC770, symSize: 0x30 }
  - { offset: 0xC6367, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtF', symObjAddr: 0x23A0, symBinAddr: 0xC7A0, symSize: 0x3B0 }
  - { offset: 0xC64B4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtFyyScMYccfU_', symObjAddr: 0x2750, symBinAddr: 0xCB50, symSize: 0x2D0 }
  - { offset: 0xC65EA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtFyyScMYccfU_yycfU_', symObjAddr: 0x2A20, symBinAddr: 0xCE20, symSize: 0x140 }
  - { offset: 0xC6764, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CAA17SDKChecksProtocolA2aDP17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFTW', symObjAddr: 0x2B60, symBinAddr: 0xCF60, symSize: 0x20 }
  - { offset: 0xC6780, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtFyyScMYccfU_TA', symObjAddr: 0x2BB0, symBinAddr: 0xCFB0, symSize: 0x20 }
  - { offset: 0xC6794, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2BD0, symBinAddr: 0xCFD0, symSize: 0x20 }
  - { offset: 0xC67A8, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2BF0, symBinAddr: 0xCFF0, symSize: 0x10 }
  - { offset: 0xC67BC, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGMa', symObjAddr: 0x2C70, symBinAddr: 0xD000, symSize: 0x50 }
  - { offset: 0xC6888, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctF', symObjAddr: 0x2CC0, symBinAddr: 0xD050, symSize: 0x410 }
  - { offset: 0xC6AA8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x30D0, symBinAddr: 0xD460, symSize: 0xAA0 }
  - { offset: 0xC6E46, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x3B70, symBinAddr: 0xDF00, symSize: 0x340 }
  - { offset: 0xC7190, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtFyyScMYccfU_yycfU_TA', symObjAddr: 0x3EE0, symBinAddr: 0xE270, symSize: 0x20 }
  - { offset: 0xC71A4, size: 0x8, addend: 0x0, symName: '_$sIeg_SgWOe', symObjAddr: 0x3F00, symBinAddr: 0xE290, symSize: 0x20 }
  - { offset: 0xC71B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x3F40, symBinAddr: 0xE2D0, symSize: 0x40 }
  - { offset: 0xC71CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_TA', symObjAddr: 0x3FB0, symBinAddr: 0xE340, symSize: 0x30 }
  - { offset: 0xC71E0, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0x3FE0, symBinAddr: 0xE370, symSize: 0x30 }
  - { offset: 0xC71F4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_yyScMYccfU_TA', symObjAddr: 0x40E0, symBinAddr: 0xE440, symSize: 0x40 }
  - { offset: 0xC7246, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOSgWOe', symObjAddr: 0x4120, symBinAddr: 0xE480, symSize: 0x20 }
  - { offset: 0xC725A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOe', symObjAddr: 0x4140, symBinAddr: 0xE4A0, symSize: 0x60 }
  - { offset: 0xC726E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A14ResultProtocol_pSgXwWOh', symObjAddr: 0x41A0, symBinAddr: 0xE500, symSize: 0x20 }
  - { offset: 0xC72C4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C10publishURI4withySS_tFTf4nd_n', symObjAddr: 0x46C0, symBinAddr: 0xEA20, symSize: 0x1A0 }
  - { offset: 0xC73D7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFTf4d_n', symObjAddr: 0x4860, symBinAddr: 0xEBC0, symSize: 0x1B0 }
  - { offset: 0xC73F6, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x4A40, symBinAddr: 0xEDA0, symSize: 0x30 }
  - { offset: 0xC740A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOy', symObjAddr: 0x4A70, symBinAddr: 0xEDD0, symSize: 0x60 }
  - { offset: 0xC741E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC05fetchC0yyySDySSypGSgcFyAA0bC13FetchResponseO_AFtcfU_TA', symObjAddr: 0x4B00, symBinAddr: 0xEE60, symSize: 0x20 }
  - { offset: 0xC7432, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFyyScMYccfU0_TA', symObjAddr: 0x4B20, symBinAddr: 0xEE80, symSize: 0x20 }
  - { offset: 0xC783A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CACyc33_C2103DC0A7BEA85E64BF4B6378FF9B75Llfc', symObjAddr: 0x30, symBinAddr: 0xA430, symSize: 0x1E0 }
  - { offset: 0xC7874, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CACyc33_C2103DC0A7BEA85E64BF4B6378FF9B75LlfcTo', symObjAddr: 0x210, symBinAddr: 0xA610, symSize: 0x20 }
  - { offset: 0xC78D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14setupAnalytics33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyF', symObjAddr: 0x230, symBinAddr: 0xA630, symSize: 0x160 }
  - { offset: 0xC7BB1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtF', symObjAddr: 0x390, symBinAddr: 0xA790, symSize: 0xED0 }
  - { offset: 0xC7FA2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFySDySSypGSgcfU_', symObjAddr: 0x1390, symBinAddr: 0xB790, symSize: 0x20 }
  - { offset: 0xC7FE0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFyyScMYccfU0_', symObjAddr: 0x13B0, symBinAddr: 0xB7B0, symSize: 0x220 }
  - { offset: 0xC80AF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C33invokeDelegateWithFailureAndClose33_C2103DC0A7BEA85E64BF4B6378FF9B75LL5errorySS_tF', symObjAddr: 0x1260, symBinAddr: 0xB660, symSize: 0x130 }
  - { offset: 0xC820B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5closeyyF', symObjAddr: 0x1600, symBinAddr: 0xBA00, symSize: 0x300 }
  - { offset: 0xC834D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C13initAnalytics33_C2103DC0A7BEA85E64BF4B6378FF9B75LL15withMerchantKeyySS_tF', symObjAddr: 0x1900, symBinAddr: 0xBD00, symSize: 0x160 }
  - { offset: 0xC83E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CfD', symObjAddr: 0x1A60, symBinAddr: 0xBE60, symSize: 0x40 }
  - { offset: 0xC8638, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_11andDelegateACSS_AA0A8Protocol_ptFZTf4nnd_g', symObjAddr: 0x41C0, symBinAddr: 0xE520, symSize: 0x170 }
  - { offset: 0xC86AF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4DataACSS_AA0a25PaymentCompletionProtocoldH0_ptFZTf4nnd_g', symObjAddr: 0x4330, symBinAddr: 0xE690, symSize: 0x100 }
  - { offset: 0xC872D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTf4nnnd_g', symObjAddr: 0x4430, symBinAddr: 0xE790, symSize: 0x100 }
  - { offset: 0xC87C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTf4nnnd_g', symObjAddr: 0x4530, symBinAddr: 0xE890, symSize: 0x190 }
  - { offset: 0xC8A13, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0xEF30, symSize: 0x10 }
  - { offset: 0xC8B07, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyCMa', symObjAddr: 0xD0, symBinAddr: 0xEF60, symSize: 0x20 }
  - { offset: 0xC8B1B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOMa', symObjAddr: 0x290, symBinAddr: 0xEF80, symSize: 0x10 }
  - { offset: 0xC8B2F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSHAASQWb', symObjAddr: 0x2A0, symBinAddr: 0xEF90, symSize: 0x10 }
  - { offset: 0xC8B43, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOAESQAAWl', symObjAddr: 0x2B0, symBinAddr: 0xEFA0, symSize: 0x30 }
  - { offset: 0xC8B85, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0xEF30, symSize: 0x10 }
  - { offset: 0xC8BE8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyCfD', symObjAddr: 0xB0, symBinAddr: 0xEF40, symSize: 0x20 }
  - { offset: 0xC90BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCfETo', symObjAddr: 0x730, symBinAddr: 0xF610, symSize: 0x60 }
  - { offset: 0xC90ED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCMa', symObjAddr: 0x790, symBinAddr: 0xF670, symSize: 0x20 }
  - { offset: 0xC9101, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOwet', symObjAddr: 0x7D0, symBinAddr: 0xF690, symSize: 0x80 }
  - { offset: 0xC9115, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOwst', symObjAddr: 0x850, symBinAddr: 0xF710, symSize: 0xD0 }
  - { offset: 0xC9129, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOMa', symObjAddr: 0x950, symBinAddr: 0xF7E0, symSize: 0x10 }
  - { offset: 0xC913D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOSHAASQWb', symObjAddr: 0x960, symBinAddr: 0xF7F0, symSize: 0x10 }
  - { offset: 0xC9151, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOACSQAAWl', symObjAddr: 0x970, symBinAddr: 0xF800, symSize: 0x30 }
  - { offset: 0xC922B, size: 0x8, addend: 0x0, symName: '_$sSo10CLLocationCMa', symObjAddr: 0xE60, symBinAddr: 0xFBC0, symSize: 0x30 }
  - { offset: 0xC944A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCACycfc', symObjAddr: 0xF0, symBinAddr: 0xEFD0, symSize: 0xE0 }
  - { offset: 0xC9491, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCACycfcTo', symObjAddr: 0x1D0, symBinAddr: 0xF0B0, symSize: 0x20 }
  - { offset: 0xC9509, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC13createPayload33_435586993CD454125951701030FBBDD3LLSSSg_AA0bcD5ErrorOSgtyF', symObjAddr: 0x1F0, symBinAddr: 0xF0D0, symSize: 0x360 }
  - { offset: 0xC9684, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC20fetchCurrentLocation33_435586993CD454125951701030FBBDD3LLAA0bcD5ErrorOSgyF', symObjAddr: 0x550, symBinAddr: 0xF430, symSize: 0xB0 }
  - { offset: 0xC975C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtFTo', symObjAddr: 0x600, symBinAddr: 0xF4E0, symSize: 0x80 }
  - { offset: 0xC978D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_16didFailWithErrorySo010CLLocationF0C_s0J0_ptFTo', symObjAddr: 0x680, symBinAddr: 0xF560, symSize: 0x70 }
  - { offset: 0xC97B7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCfD', symObjAddr: 0x6F0, symBinAddr: 0xF5D0, symSize: 0x40 }
  - { offset: 0xC9910, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtFTf4dnn_n', symObjAddr: 0xAB0, symBinAddr: 0xF830, symSize: 0x2E0 }
  - { offset: 0xC9C9F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_16didFailWithErrorySo010CLLocationF0C_s0J0_ptFTf4ddn_n', symObjAddr: 0xD90, symBinAddr: 0xFB10, symSize: 0xB0 }
  - { offset: 0xCA10D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerCMa', symObjAddr: 0xC70, symBinAddr: 0x10860, symSize: 0x20 }
  - { offset: 0xCA121, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyFyyScMYccfU_TA', symObjAddr: 0xCC0, symBinAddr: 0x10880, symSize: 0x10 }
  - { offset: 0xCA135, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xCD0, symBinAddr: 0x10890, symSize: 0x20 }
  - { offset: 0xCA149, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xCF0, symBinAddr: 0x108B0, symSize: 0x10 }
  - { offset: 0xCA28A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC17displayController10dataSource9isTestingACSo06UIViewE0CSg_AA0A13CheckoutModelCSbtcfc', symObjAddr: 0x0, symBinAddr: 0xFBF0, symSize: 0x290 }
  - { offset: 0xCA409, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC15displayCheckoutyyF', symObjAddr: 0x290, symBinAddr: 0xFE80, symSize: 0x340 }
  - { offset: 0xCA69F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyF', symObjAddr: 0x5D0, symBinAddr: 0x101C0, symSize: 0x1C0 }
  - { offset: 0xCA6E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyFyyScMYccfU_', symObjAddr: 0x790, symBinAddr: 0x10380, symSize: 0x50 }
  - { offset: 0xCA7AA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC030dismissControllerUsingRootViewE033_5FADD501D99B794A6E8B315B74C25A57LLyyF', symObjAddr: 0x7E0, symBinAddr: 0x103D0, symSize: 0x460 }
  - { offset: 0xCAA24, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerCfD', symObjAddr: 0xC40, symBinAddr: 0x10830, symSize: 0x30 }
  - { offset: 0xCAA65, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC37displayCheckoutUsingDisplayController33_5FADD501D99B794A6E8B315B74C25A57LL0dH0011checkoutNavH00R2VCySo06UIViewH0C_So012UINavigationH0CAA0aeT0CtFTf4nndd_n', symObjAddr: 0xDC0, symBinAddr: 0x108C0, symSize: 0x130 }
  - { offset: 0xCAAD6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC38displayCheckoutUsingRootViewController33_5FADD501D99B794A6E8B315B74C25A57LL011checkoutNavI00S2VCySo012UINavigationI0C_AA0aeU0CtFTf4ndn_n', symObjAddr: 0xEF0, symBinAddr: 0x109F0, symSize: 0x2C0 }
  - { offset: 0xCABAF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC029dismissControllerUsingDisplayE033_5FADD501D99B794A6E8B315B74C25A57LL07displayE0ySo06UIViewE0C_tFTf4nd_n', symObjAddr: 0x11B0, symBinAddr: 0x10CB0, symSize: 0x1E0 }
  - { offset: 0xCAD83, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11merchantKeySSvg', symObjAddr: 0x0, symBinAddr: 0x10E90, symSize: 0x30 }
  - { offset: 0xCAE9E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCMa', symObjAddr: 0x1D0, symBinAddr: 0x11060, symSize: 0x20 }
  - { offset: 0xCAEB2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCfETo', symObjAddr: 0x1F0, symBinAddr: 0x11080, symSize: 0x40 }
  - { offset: 0xCAEF6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11merchantKeySSvg', symObjAddr: 0x0, symBinAddr: 0x10E90, symSize: 0x30 }
  - { offset: 0xCAF19, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC04dictC11InformationSDys11AnyHashableVypGvg', symObjAddr: 0x30, symBinAddr: 0x10EC0, symSize: 0x20 }
  - { offset: 0xCAF3C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC8delegateAA0bC18CompletionDelegate_pvg', symObjAddr: 0x50, symBinAddr: 0x10EE0, symSize: 0x30 }
  - { offset: 0xCAF5F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC14getMerchantKeySSyF', symObjAddr: 0x80, symBinAddr: 0x10F10, symSize: 0x40 }
  - { offset: 0xCAF82, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC03getC8InfoDictSDys11AnyHashableVypGyF', symObjAddr: 0xC0, symBinAddr: 0x10F50, symSize: 0x20 }
  - { offset: 0xCAFA5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11getDelegateAA0bc10CompletionF0_pyF', symObjAddr: 0xE0, symBinAddr: 0x10F70, symSize: 0x40 }
  - { offset: 0xCAFCE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCACycfC', symObjAddr: 0x120, symBinAddr: 0x10FB0, symSize: 0x20 }
  - { offset: 0xCAFE8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCACycfc', symObjAddr: 0x140, symBinAddr: 0x10FD0, symSize: 0x30 }
  - { offset: 0xCB046, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCACycfcTo', symObjAddr: 0x170, symBinAddr: 0x11000, symSize: 0x30 }
  - { offset: 0xCB0A5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCfD', symObjAddr: 0x1A0, symBinAddr: 0x11030, symSize: 0x30 }
  - { offset: 0xCB239, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelVMa', symObjAddr: 0x0, symBinAddr: 0x11140, symSize: 0x10 }
  - { offset: 0xCB251, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelVMa', symObjAddr: 0x0, symBinAddr: 0x11140, symSize: 0x10 }
  - { offset: 0xCB2C1, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x590, symBinAddr: 0x11630, symSize: 0x40 }
  - { offset: 0xCB34E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelV03addB04name8observer8selector6objectSbSS_yp10ObjectiveC8SelectorVypSgtF', symObjAddr: 0x10, symBinAddr: 0x11150, symSize: 0x270 }
  - { offset: 0xCB3CD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelV06removeB04name8observer6objectSbSS_ypypSgtF', symObjAddr: 0x280, symBinAddr: 0x113C0, symSize: 0x270 }
  - { offset: 0xCB5E9, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE19reachabilityChangedABvpZ', symObjAddr: 0xA370, symBinAddr: 0xB6348, symSize: 0x0 }
  - { offset: 0xCB776, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC13startNotifieryyKF', symObjAddr: 0x0, symBinAddr: 0x11670, symSize: 0x310 }
  - { offset: 0xCB876, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE19reachabilityChanged_WZ', symObjAddr: 0x350, symBinAddr: 0x11980, symSize: 0x30 }
  - { offset: 0xCB920, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyF', symObjAddr: 0x380, symBinAddr: 0x119B0, symSize: 0x320 }
  - { offset: 0xCBA04, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyFyyScMYccfU_', symObjAddr: 0x1010, symBinAddr: 0x12380, symSize: 0x80 }
  - { offset: 0xCBAF9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCMa', symObjAddr: 0xC10, symBinAddr: 0x12190, symSize: 0x20 }
  - { offset: 0xCBB0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOMa', symObjAddr: 0xDD0, symBinAddr: 0x121B0, symSize: 0x10 }
  - { offset: 0xCBB21, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOSHAASQWb', symObjAddr: 0xDE0, symBinAddr: 0x121C0, symSize: 0x10 }
  - { offset: 0xCBB35, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOAESQAAWl', symObjAddr: 0xDF0, symBinAddr: 0x121D0, symSize: 0x30 }
  - { offset: 0xCBB49, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0xE20, symBinAddr: 0x12200, symSize: 0x30 }
  - { offset: 0xCBBB1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay8callback12reachability5flags4infoySo24SCNetworkReachabilityRefa_So0fG5FlagsVSvSgtFTo', symObjAddr: 0xED0, symBinAddr: 0x12270, symSize: 0x50 }
  - { offset: 0xCBC28, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC13startNotifieryyKFyyYbcfU_TA', symObjAddr: 0xF20, symBinAddr: 0x122C0, symSize: 0x20 }
  - { offset: 0xCBC5A, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xF40, symBinAddr: 0x122E0, symSize: 0x20 }
  - { offset: 0xCBC6E, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xF60, symBinAddr: 0x12300, symSize: 0x10 }
  - { offset: 0xCBC82, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOACs0C0AAWl', symObjAddr: 0xFE0, symBinAddr: 0x12350, symSize: 0x30 }
  - { offset: 0xCBCA7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCIegg_SgWOy', symObjAddr: 0x1090, symBinAddr: 0x12400, symSize: 0x20 }
  - { offset: 0xCBCBB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyFyyScMYccfU_TA', symObjAddr: 0x10E0, symBinAddr: 0x12450, symSize: 0x20 }
  - { offset: 0xCBCCF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOWOy', symObjAddr: 0x1130, symBinAddr: 0x12480, symSize: 0x20 }
  - { offset: 0xCBCE3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwxx', symObjAddr: 0x1150, symBinAddr: 0x124A0, symSize: 0x20 }
  - { offset: 0xCBCF7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOWOe', symObjAddr: 0x1170, symBinAddr: 0x124C0, symSize: 0x20 }
  - { offset: 0xCBD0B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwca', symObjAddr: 0x11E0, symBinAddr: 0x12520, symSize: 0x50 }
  - { offset: 0xCBD1F, size: 0x8, addend: 0x0, symName: ___swift_memcpy17_8, symObjAddr: 0x1230, symBinAddr: 0x12570, symSize: 0x20 }
  - { offset: 0xCBD33, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwta', symObjAddr: 0x1250, symBinAddr: 0x12590, symSize: 0x40 }
  - { offset: 0xCBD47, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwet', symObjAddr: 0x1290, symBinAddr: 0x125D0, symSize: 0x50 }
  - { offset: 0xCBD5B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwst', symObjAddr: 0x12E0, symBinAddr: 0x12620, symSize: 0x50 }
  - { offset: 0xCBD6F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwug', symObjAddr: 0x1330, symBinAddr: 0x12670, symSize: 0x20 }
  - { offset: 0xCBD83, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwui', symObjAddr: 0x1360, symBinAddr: 0x12690, symSize: 0x20 }
  - { offset: 0xCBD97, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOMa', symObjAddr: 0x1380, symBinAddr: 0x126B0, symSize: 0x10 }
  - { offset: 0xCBFA8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOs23CustomStringConvertibleAAsAFP11descriptionSSvgTW', symObjAddr: 0x750, symBinAddr: 0x11CD0, symSize: 0x70 }
  - { offset: 0xCBFCA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10connectionAC10ConnectionOvg', symObjAddr: 0x7C0, symBinAddr: 0x11D40, symSize: 0x1B0 }
  - { offset: 0xCC49D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC15reachabilityRefACSo09SCNetworkbD0a_tcfc', symObjAddr: 0x970, symBinAddr: 0x11EF0, symSize: 0x210 }
  - { offset: 0xCC548, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCfD', symObjAddr: 0xB80, symBinAddr: 0x12100, symSize: 0x90 }
  - { offset: 0xCCACC, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x5380, symBinAddr: 0x17A60, symSize: 0xB0 }
  - { offset: 0xCCEBF, size: 0x8, addend: 0x0, symName: '_$sSlsE6suffix4from11SubSequenceQz5IndexQz_tFSS_Tg5Tf4ng_n', symObjAddr: 0xAFE0, symBinAddr: 0x1D5C0, symSize: 0x70 }
  - { offset: 0xCD248, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14isRetryEnabled33_C52A9CB3C4396E63C4FE51721B47A244LL2inSbSDys11AnyHashableVypG_tF', symObjAddr: 0x0, symBinAddr: 0x126E0, symSize: 0x260 }
  - { offset: 0xCD4F6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC6onLoad33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x260, symBinAddr: 0x12940, symSize: 0xC20 }
  - { offset: 0xCDCAF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12onCompletion33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0xE80, symBinAddr: 0x13560, symSize: 0xE0 }
  - { offset: 0xCDE0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7onFault33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0xF60, symBinAddr: 0x13640, symSize: 0xC50 }
  - { offset: 0xCE3CB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC9onDismiss33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x1BB0, symBinAddr: 0x14290, symSize: 0x950 }
  - { offset: 0xCE841, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x2500, symBinAddr: 0x14BE0, symSize: 0x11C0 }
  - { offset: 0xCEEC7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tFyyScMYccfU_', symObjAddr: 0x7810, symBinAddr: 0x19EF0, symSize: 0x60 }
  - { offset: 0xCEF29, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC9onSuccess33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x36C0, symBinAddr: 0x15DA0, symSize: 0x950 }
  - { offset: 0xCF265, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC33modifyActivityIndicatorAppearance33_C52A9CB3C4396E63C4FE51721B47A244LL13withColorDictySDySS12CoreGraphics7CGFloatVG_tF', symObjAddr: 0x4010, symBinAddr: 0x166F0, symSize: 0x230 }
  - { offset: 0xCF4D4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tF', symObjAddr: 0x4240, symBinAddr: 0x16920, symSize: 0x370 }
  - { offset: 0xCF71A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tFySSSg_AA29ShieldInformationFetcherErrorOSgtcfU_Tf4ndnn_n', symObjAddr: 0xAEA0, symBinAddr: 0x1D480, symSize: 0x140 }
  - { offset: 0xCF8A2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18downloadChallanPdf33_C52A9CB3C4396E63C4FE51721B47A244LL4form11andFileNameySS_SStF', symObjAddr: 0x45B0, symBinAddr: 0x16C90, symSize: 0x5E0 }
  - { offset: 0xCFBFE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12downloadFile33_C52A9CB3C4396E63C4FE51721B47A244LL4form03andE4Name0P9ExtensionySS_S2StF', symObjAddr: 0x4B90, symBinAddr: 0x17270, symSize: 0x2D0 }
  - { offset: 0xCFD66, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x4E60, symBinAddr: 0x17540, symSize: 0x4B0 }
  - { offset: 0xCFE71, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tFyypSg_AItcfU_', symObjAddr: 0x7A90, symBinAddr: 0x1A170, symSize: 0x8C0 }
  - { offset: 0xD03B7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21userContentController_10didReceiveySo06WKUsereF0C_So15WKScriptMessageCtFTo', symObjAddr: 0x5310, symBinAddr: 0x179F0, symSize: 0x70 }
  - { offset: 0xD04AE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtF', symObjAddr: 0x5430, symBinAddr: 0x17B10, symSize: 0x6B0 }
  - { offset: 0xD0591, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU_', symObjAddr: 0x7870, symBinAddr: 0x19F50, symSize: 0x70 }
  - { offset: 0xD05E7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU2_', symObjAddr: 0x79B0, symBinAddr: 0x1A090, symSize: 0x90 }
  - { offset: 0xD0660, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18saveDocumentAsFile33_C52A9CB3C4396E63C4FE51721B47A244LL4name13fileExtension19base64EncodedStringySS_SS10Foundation4DataVtF', symObjAddr: 0x5AE0, symBinAddr: 0x181C0, symSize: 0x4A0 }
  - { offset: 0xD09B6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFTo', symObjAddr: 0x5F80, symBinAddr: 0x18660, symSize: 0x70 }
  - { offset: 0xD0AC8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadFormyyF', symObjAddr: 0x5FF0, symBinAddr: 0x186D0, symSize: 0x14C0 }
  - { offset: 0xD1551, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31saveAppropriateEventToAnalytics4dict16eventWithoutData0j4WithL0ySDys11AnyHashableVypG_AA0hF0OALtF', symObjAddr: 0x74B0, symBinAddr: 0x19B90, symSize: 0xF0 }
  - { offset: 0xD1670, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16isExternalWallet33_C52A9CB3C4396E63C4FE51721B47A244LL6walletSbSS_tF', symObjAddr: 0x75A0, symBinAddr: 0x19C80, symSize: 0x270 }
  - { offset: 0xD1741, size: 0x8, addend: 0x0, symName: '_$sSo21PHAuthorizationStatusVIegy_ABIeyBy_TR', symObjAddr: 0x78E0, symBinAddr: 0x19FC0, symSize: 0x40 }
  - { offset: 0xD1759, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14openURISchemes4withySS_tFySbYbScMYccfU_', symObjAddr: 0x7A40, symBinAddr: 0x1A120, symSize: 0x10 }
  - { offset: 0xD1784, size: 0x8, addend: 0x0, symName: '_$sSbIeghy_SbIeyBhy_TR', symObjAddr: 0x7A50, symBinAddr: 0x1A130, symSize: 0x40 }
  - { offset: 0xD19C2, size: 0x8, addend: 0x0, symName: '_$sSlsE5split9maxSplits25omittingEmptySubsequences14whereSeparatorSay11SubSequenceQzGSi_S2b7ElementQzKXEtKFSS_Tg5', symObjAddr: 0x8350, symBinAddr: 0x1AA30, symSize: 0x540 }
  - { offset: 0xD1D32, size: 0x8, addend: 0x0, symName: '_$sSlsE5split9maxSplits25omittingEmptySubsequences14whereSeparatorSay11SubSequenceQzGSi_S2b7ElementQzKXEtKF17appendSubsequenceL_3endSb5IndexQz_tSlRzlFSS_Tg5', symObjAddr: 0x8890, symBinAddr: 0x1AF70, symSize: 0x110 }
  - { offset: 0xD1E9D, size: 0x8, addend: 0x0, symName: '_$sSlsSQ7ElementRpzrlE5split9separator9maxSplits25omittingEmptySubsequencesSay11SubSequenceQzGAB_SiSbtFSbABXEfU_SS_TG5', symObjAddr: 0x89A0, symBinAddr: 0x1B080, symSize: 0x40 }
  - { offset: 0xD1F54, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17getSelectedObject4with9shortcodeSDyS2SGSgSayAGG_SStFTf4nnd_n', symObjAddr: 0x89E0, symBinAddr: 0x1B0C0, symSize: 0x150 }
  - { offset: 0xD20F9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12getUriScheme3for6andUrlSSSgSDyS2SG_SStFTf4nnd_n', symObjAddr: 0x8B30, symBinAddr: 0x1B210, symSize: 0x120 }
  - { offset: 0xD21A6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14openURISchemes4withySS_tFTf4nd_n', symObjAddr: 0x8C50, symBinAddr: 0x1B330, symSize: 0x200 }
  - { offset: 0xD22BC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16callNativeIntent33_C52A9CB3C4396E63C4FE51721B47A244LL4dictySDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x8E50, symBinAddr: 0x1B530, symSize: 0x870 }
  - { offset: 0xD26E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16isRetryAvailable33_C52A9CB3C4396E63C4FE51721B47A244LL2inSDys11AnyHashableVypGSgAI_tFTf4nd_n', symObjAddr: 0x96C0, symBinAddr: 0x1BDA0, symSize: 0x110 }
  - { offset: 0xD2784, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19getAvailableUriDataSaySDyS2SGGyFTf4d_n', symObjAddr: 0x97D0, symBinAddr: 0x1BEB0, symSize: 0x700 }
  - { offset: 0xD2BF4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19getAvailableAppListSaySDyS2SGGyFTf4d_n', symObjAddr: 0x9ED0, symBinAddr: 0x1C5B0, symSize: 0x870 }
  - { offset: 0xD3191, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22retrieveContactDetailsSDys11AnyHashableVypGyFTf4d_n', symObjAddr: 0xA740, symBinAddr: 0x1CE20, symSize: 0x170 }
  - { offset: 0xD32C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18saveContactDetails33_C52A9CB3C4396E63C4FE51721B47A244LLyySDyS2SGFTf4nd_n', symObjAddr: 0xAA00, symBinAddr: 0x1CFE0, symSize: 0x350 }
  - { offset: 0xD3420, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12sendErrorObj33_C52A9CB3C4396E63C4FE51721B47A244LL8withData07messageF0SDys11AnyHashableVypGSgAJ_AJtFTf4nnd_n', symObjAddr: 0xAD50, symBinAddr: 0x1D330, symSize: 0x150 }
  - { offset: 0xD354F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21userContentController_10didReceiveySo06WKUsereF0C_So15WKScriptMessageCtFTf4dnn_n', symObjAddr: 0xB050, symBinAddr: 0x1D630, symSize: 0x2660 }
  - { offset: 0xD4050, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFTf4dndn_n', symObjAddr: 0xD6B0, symBinAddr: 0x1FC90, symSize: 0x490 }
  - { offset: 0xD41AF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFyyScMYccfU_TA', symObjAddr: 0xDBA0, symBinAddr: 0x20150, symSize: 0x30 }
  - { offset: 0xD41E1, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xDBD0, symBinAddr: 0x20180, symSize: 0x20 }
  - { offset: 0xD41F5, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xDBF0, symBinAddr: 0x201A0, symSize: 0x10 }
  - { offset: 0xD4209, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xDD20, symBinAddr: 0x201B0, symSize: 0x30 }
  - { offset: 0xD421D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tFyyScMYccfU_TA', symObjAddr: 0xDDD0, symBinAddr: 0x20220, symSize: 0x10 }
  - { offset: 0xD4231, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tFySSSg_AA29ShieldInformationFetcherErrorOSgtcfU_TA', symObjAddr: 0xDE10, symBinAddr: 0x20260, symSize: 0x20 }
  - { offset: 0xD425A, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_0, symObjAddr: 0xDE80, symBinAddr: 0x20280, symSize: 0x30 }
  - { offset: 0xD426E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU2_TA', symObjAddr: 0xDF40, symBinAddr: 0x202F0, symSize: 0x40 }
  - { offset: 0xD4282, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU_TA', symObjAddr: 0xE0C0, symBinAddr: 0x20470, symSize: 0x20 }
  - { offset: 0xD4296, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tFyypSg_AItcfU_TA', symObjAddr: 0xE110, symBinAddr: 0x204C0, symSize: 0x20 }
  - { offset: 0xD4CA4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5frame13configurationACSo6CGRectV_So05WKWebD13ConfigurationCtcfcTo', symObjAddr: 0x0, symBinAddr: 0x205A0, symSize: 0x70 }
  - { offset: 0xD4D6C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewCMa', symObjAddr: 0x290, symBinAddr: 0x20830, symSize: 0x20 }
  - { offset: 0xD4D80, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOh', symObjAddr: 0x2F0, symBinAddr: 0x20850, symSize: 0x30 }
  - { offset: 0xD4E18, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5frame13configurationACSo6CGRectV_So05WKWebD13ConfigurationCtcfcTo', symObjAddr: 0x0, symBinAddr: 0x205A0, symSize: 0x70 }
  - { offset: 0xD4EB2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x70, symBinAddr: 0x20610, symSize: 0x50 }
  - { offset: 0xD4EF3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC7loadUrl10withStringySS_tF', symObjAddr: 0xC0, symBinAddr: 0x20660, symSize: 0x190 }
  - { offset: 0xD4F56, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewCfD', symObjAddr: 0x250, symBinAddr: 0x207F0, symSize: 0x40 }
  - { offset: 0xD5591, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCfETo', symObjAddr: 0x1550, symBinAddr: 0x21DD0, symSize: 0x90 }
  - { offset: 0xD55C0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCMa', symObjAddr: 0x15E0, symBinAddr: 0x21E60, symSize: 0x20 }
  - { offset: 0xD55DF, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOb', symObjAddr: 0x1B60, symBinAddr: 0x22340, symSize: 0x40 }
  - { offset: 0xD5822, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC17activityIndicatorSo010UIActivityE4ViewCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x20880, symSize: 0x20 }
  - { offset: 0xD5882, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC17activityIndicatorSo010UIActivityE4ViewCSgvsTo', symObjAddr: 0x20, symBinAddr: 0x208A0, symSize: 0x20 }
  - { offset: 0xD59D9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC11viewDidLoadyyF', symObjAddr: 0x40, symBinAddr: 0x208C0, symSize: 0x2C0 }
  - { offset: 0xD5BD7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC11viewDidLoadyyFTo', symObjAddr: 0x300, symBinAddr: 0x20B80, symSize: 0x30 }
  - { offset: 0xD5C2E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC13viewDidAppearyySbF', symObjAddr: 0x330, symBinAddr: 0x20BB0, symSize: 0x2F0 }
  - { offset: 0xD5DF3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC13viewDidAppearyySbFTo', symObjAddr: 0x620, symBinAddr: 0x20EA0, symSize: 0x30 }
  - { offset: 0xD5E1B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC18addUiViewAsSubviewyyF', symObjAddr: 0x650, symBinAddr: 0x20ED0, symSize: 0x110 }
  - { offset: 0xD5EAF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC15dismissKeyboardyyFTo', symObjAddr: 0x760, symBinAddr: 0x20FE0, symSize: 0x70 }
  - { offset: 0xD5F90, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC10initialize13storefrontUrl9itemsData12withDelegateySS_SSAA20MagicXResultProtocol_ptF', symObjAddr: 0x7D0, symBinAddr: 0x21050, symSize: 0x160 }
  - { offset: 0xD6167, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC19addWebViewAsSubview33_08CDC63A7B054143135D8C60DBB70CFALL03webF0ySo05WKWebF0C_tF', symObjAddr: 0x930, symBinAddr: 0x211B0, symSize: 0x730 }
  - { offset: 0xD6383, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_29didStartProvisionalNavigationySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0x1060, symBinAddr: 0x218E0, symSize: 0x70 }
  - { offset: 0xD63B4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0x10D0, symBinAddr: 0x21950, symSize: 0x70 }
  - { offset: 0xD6426, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC16handleBackActionyyypF', symObjAddr: 0x1140, symBinAddr: 0x219C0, symSize: 0xF0 }
  - { offset: 0xD64C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC16handleBackActionyyypFTo', symObjAddr: 0x1230, symBinAddr: 0x21AB0, symSize: 0x60 }
  - { offset: 0xD64DD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x1290, symBinAddr: 0x21B10, symSize: 0x120 }
  - { offset: 0xD651E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x13B0, symBinAddr: 0x21C30, symSize: 0x50 }
  - { offset: 0xD6566, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x1400, symBinAddr: 0x21C80, symSize: 0xF0 }
  - { offset: 0xD6599, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x14F0, symBinAddr: 0x21D70, symSize: 0x30 }
  - { offset: 0xD65AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCfD', symObjAddr: 0x1520, symBinAddr: 0x21DA0, symSize: 0x30 }
  - { offset: 0xD65D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_29didStartProvisionalNavigationySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x1660, symBinAddr: 0x21E80, symSize: 0x1A0 }
  - { offset: 0xD6667, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x1800, symBinAddr: 0x22020, symSize: 0x320 }
  - { offset: 0xD68C7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC28isIntegrationCheckInitilizedSbvpZ', symObjAddr: 0x15BAE, symBinAddr: 0xB6350, symSize: 0x0 }
  - { offset: 0xD68D5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CAA17SDKChecksProtocolA2aDP03logC4Data14sdkCheckPointsySayAA0hI0VG_tFTW', symObjAddr: 0x40, symBinAddr: 0x22380, symSize: 0x20 }
  - { offset: 0xD6A3A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocolPAAE03logB4Data14sdkCheckPointsySayAA0gH0VG_tFAA08InternalA0C_Tg5', symObjAddr: 0x60, symBinAddr: 0x223A0, symSize: 0x7B0 }
  - { offset: 0xD7011, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocolPAAE03logB4Data14sdkCheckPointsySayAA0gH0VG_tFAA28OpinionatedChecksInitializerV_Tg5', symObjAddr: 0x810, symBinAddr: 0x22B50, symSize: 0x7C0 }
  - { offset: 0xD75FA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC18validateSDKVersion10sdkVersionySSSg_tF', symObjAddr: 0xFD0, symBinAddr: 0x23310, symSize: 0x270 }
  - { offset: 0xD76F7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC18validateSDKVersion10sdkVersionySSSg_tFySo13NSURLResponseCSgcfU_', symObjAddr: 0x2520, symBinAddr: 0x24830, symSize: 0x500 }
  - { offset: 0xD7A46, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerCMa', symObjAddr: 0x13E0, symBinAddr: 0x23720, symSize: 0x20 }
  - { offset: 0xD7A5A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwxx', symObjAddr: 0x1430, symBinAddr: 0x23740, symSize: 0x30 }
  - { offset: 0xD7A6E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwcp', symObjAddr: 0x1460, symBinAddr: 0x23770, symSize: 0x70 }
  - { offset: 0xD7A82, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwca', symObjAddr: 0x14D0, symBinAddr: 0x237E0, symSize: 0x90 }
  - { offset: 0xD7A96, size: 0x8, addend: 0x0, symName: ___swift_memcpy56_8, symObjAddr: 0x1560, symBinAddr: 0x23870, symSize: 0x30 }
  - { offset: 0xD7AAA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwta', symObjAddr: 0x1590, symBinAddr: 0x238A0, symSize: 0x70 }
  - { offset: 0xD7ABE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwet', symObjAddr: 0x1600, symBinAddr: 0x23910, symSize: 0x40 }
  - { offset: 0xD7AD2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwst', symObjAddr: 0x1640, symBinAddr: 0x23950, symSize: 0x50 }
  - { offset: 0xD7AE6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVMa', symObjAddr: 0x1690, symBinAddr: 0x239A0, symSize: 0x10 }
  - { offset: 0xD7AFA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMajorVersion02sdG0ySS_tF', symObjAddr: 0x16A0, symBinAddr: 0x239B0, symSize: 0x240 }
  - { offset: 0xD7BE0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMajorVersion02sdG0ySS_tFySo13NSURLResponseCSgcfU_', symObjAddr: 0x18E0, symBinAddr: 0x23BF0, symSize: 0x500 }
  - { offset: 0xD7E9E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMinorVersion03sdkG0ySS_tF', symObjAddr: 0x1DE0, symBinAddr: 0x240F0, symSize: 0x240 }
  - { offset: 0xD7F84, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMinorVersion03sdkG0ySS_tFySo13NSURLResponseCSgcfU_', symObjAddr: 0x2020, symBinAddr: 0x24330, symSize: 0x500 }
  - { offset: 0xD8242, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVAA17SDKChecksProtocolA2aDP03logE4Data14sdkCheckPointsySayAA0jK0VG_tFTW', symObjAddr: 0x2A20, symBinAddr: 0x24D30, symSize: 0x20 }
  - { offset: 0xD82AB, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0x2A80, symBinAddr: 0x24D50, symSize: 0x3C0 }
  - { offset: 0xD83B6, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFs11AnyHashableV_ypTg5', symObjAddr: 0x2E40, symBinAddr: 0x25110, symSize: 0x3D0 }
  - { offset: 0xD84C1, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0x3210, symBinAddr: 0x254E0, symSize: 0x3A0 }
  - { offset: 0xD85BF, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF8Razorpay11CheckPointsV_Tg5', symObjAddr: 0x35B0, symBinAddr: 0x25880, symSize: 0x20 }
  - { offset: 0xD8640, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsE_5radixxSgqd___SitcSyRd__lufcADSRys5UInt8VGXEfU_Si_SsTG5', symObjAddr: 0x35D0, symBinAddr: 0x258A0, symSize: 0x280 }
  - { offset: 0xD8777, size: 0x8, addend: 0x0, symName: '_$ss5SliceV32withContiguousStorageIfAvailableyqd__Sgqd__SRy7ElementQzGKXEKlFqd__AGKXEfU_SS8UTF8ViewV_SiSgTG5', symObjAddr: 0x3850, symBinAddr: 0x25B20, symSize: 0x40 }
  - { offset: 0xD8897, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC16getVersionUrlFor33_3C86DF60C77CDB1157F770B0FCB4331CLL03sdkF011versionTypeSSSgSS_AA0f11VallidationR0AELLOtFTf4nnd_n', symObjAddr: 0x3940, symBinAddr: 0x25B60, symSize: 0x630 }
  - { offset: 0xD8EE5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC18validateSDKVersion10sdkVersionySSSg_tFySo13NSURLResponseCSgcfU_TA', symObjAddr: 0x3FB0, symBinAddr: 0x261A0, symSize: 0x20 }
  - { offset: 0xD8EF9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocol_pSgWOc', symObjAddr: 0x3FD0, symBinAddr: 0x261C0, symSize: 0x40 }
  - { offset: 0xD8F0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocol_pWOc', symObjAddr: 0x4010, symBinAddr: 0x26200, symSize: 0x30 }
  - { offset: 0xD8F21, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMinorVersion03sdkG0ySS_tFySo13NSURLResponseCSgcfU_TA', symObjAddr: 0x40B0, symBinAddr: 0x26240, symSize: 0x20 }
  - { offset: 0xD8F35, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMajorVersion02sdG0ySS_tFySo13NSURLResponseCSgcfU_TA', symObjAddr: 0x4110, symBinAddr: 0x262A0, symSize: 0x20 }
  - { offset: 0xD8F49, size: 0x8, addend: 0x0, symName: '_$sSlsSQ7ElementRpzrlE5split9separator9maxSplits25omittingEmptySubsequencesSay11SubSequenceQzGAB_SiSbtFSbABXEfU_SS_TG5TA', symObjAddr: 0x4130, symBinAddr: 0x262C0, symSize: 0x10 }
  - { offset: 0xD8F5D, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsE_5radixxSgqd___SitcSyRd__lufcADSRys5UInt8VGXEfU_Si_SsTG5TA', symObjAddr: 0x4140, symBinAddr: 0x262D0, symSize: 0x10 }
  - { offset: 0xD8F71, size: 0x8, addend: 0x0, symName: '_$ss5SliceV32withContiguousStorageIfAvailableyqd__Sgqd__SRy7ElementQzGKXEKlFqd__AGKXEfU_SS8UTF8ViewV_SiSgTG5TA', symObjAddr: 0x4150, symBinAddr: 0x262E0, symSize: 0x20 }
  - { offset: 0xD8F9B, size: 0x8, addend: 0x0, symName: '_$ss5SliceV32withContiguousStorageIfAvailableyqd__Sgqd__SRy7ElementQzGKXEKlFqd__AGKXEfU_SS8UTF8ViewV_SiSgTg5Tf4xnn_n', symObjAddr: 0x4170, symBinAddr: 0x26300, symSize: 0x260 }
  - { offset: 0xD93B3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC27getMinimumDeploymentVersionyyF', symObjAddr: 0x1240, symBinAddr: 0x23580, symSize: 0x150 }
  - { offset: 0xD952F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerCfD', symObjAddr: 0x1390, symBinAddr: 0x236D0, symSize: 0x50 }
  - { offset: 0xD97CE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsCMa', symObjAddr: 0x20, symBinAddr: 0x26590, symSize: 0x20 }
  - { offset: 0xD986E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC04doesB18ExistAtDefaultPathySbSSFZTf4nd_n', symObjAddr: 0x40, symBinAddr: 0x265B0, symSize: 0x1B0 }
  - { offset: 0xD998E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC07writeToB13AtDefaultPath_7contentySS_SStFZTf4nnd_n', symObjAddr: 0x1F0, symBinAddr: 0x26760, symSize: 0x840 }
  - { offset: 0xD9CCE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC08readFromB13AtDefaultPathyS2SFZTf4nd_n', symObjAddr: 0xAD0, symBinAddr: 0x26FA0, symSize: 0x330 }
  - { offset: 0xD9DFD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC06removeB13AtDefaultPathyySSFZTf4nd_n', symObjAddr: 0xE00, symBinAddr: 0x272D0, symSize: 0x4A0 }
  - { offset: 0xDA17D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x27770, symSize: 0x20 }
  - { offset: 0xDA3AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCfETo', symObjAddr: 0x2C0, symBinAddr: 0x27A30, symSize: 0x40 }
  - { offset: 0xDA3DB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCMa', symObjAddr: 0x300, symBinAddr: 0x27A70, symSize: 0x20 }
  - { offset: 0xDA4C7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x27770, symSize: 0x20 }
  - { offset: 0xDA527, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvsTo', symObjAddr: 0x20, symBinAddr: 0x27790, symSize: 0x20 }
  - { offset: 0xDA57A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC17checksDetailLabelSo7UILabelCSgvgTo', symObjAddr: 0x40, symBinAddr: 0x277B0, symSize: 0x20 }
  - { offset: 0xDA5CF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC17checksDetailLabelSo7UILabelCSgvsTo', symObjAddr: 0x60, symBinAddr: 0x277D0, symSize: 0x20 }
  - { offset: 0xDA622, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC18accessoryImageViewSo07UIImageG0CSgvgTo', symObjAddr: 0x80, symBinAddr: 0x277F0, symSize: 0x20 }
  - { offset: 0xDA67C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC18accessoryImageViewSo07UIImageG0CSgvsTo', symObjAddr: 0xA0, symBinAddr: 0x27810, symSize: 0x20 }
  - { offset: 0xDA6CF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC12awakeFromNibyyFTo', symObjAddr: 0xC0, symBinAddr: 0x27830, symSize: 0x40 }
  - { offset: 0xDA747, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC11setSelected_8animatedySb_SbtFTo', symObjAddr: 0x100, symBinAddr: 0x27870, symSize: 0x50 }
  - { offset: 0xDA7AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC5style15reuseIdentifierACSo011UITableViewD5StyleV_SSSgtcfcTo', symObjAddr: 0x150, symBinAddr: 0x278C0, symSize: 0xD0 }
  - { offset: 0xDA832, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x220, symBinAddr: 0x27990, symSize: 0x70 }
  - { offset: 0xDA86D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCfD', symObjAddr: 0x290, symBinAddr: 0x27A00, symSize: 0x30 }
  - { offset: 0xDAA0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14sharedInstance020_D5B288FFF260912DDA9E11E37AC5A806BLLACvpZ', symObjAddr: 0x778, symBinAddr: 0xA3018, symSize: 0x0 }
  - { offset: 0xDABA9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14sharedInstance020_D5B288FFF260912DDA9E11E37AC5A806BLL_WZ', symObjAddr: 0x10, symBinAddr: 0x27AA0, symSize: 0x30 }
  - { offset: 0xDAC5A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCfETo', symObjAddr: 0x4B0, symBinAddr: 0x27F40, symSize: 0x10 }
  - { offset: 0xDAC96, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCMa', symObjAddr: 0x6A0, symBinAddr: 0x28130, symSize: 0x20 }
  - { offset: 0xDACAA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOAEs0D0AAWl', symObjAddr: 0x740, symBinAddr: 0x281D0, symSize: 0x30 }
  - { offset: 0xDAD75, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZ', symObjAddr: 0x0, symBinAddr: 0x27A90, symSize: 0x10 }
  - { offset: 0xDADA4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCACyc020_D5B288FFF260912DDA9C11E37AC5A806BLlfcTo', symObjAddr: 0x40, symBinAddr: 0x27AD0, symSize: 0x40 }
  - { offset: 0xDAE10, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC15initWithWebView_14andMerchantKeyySo05WKWebF0C_SSSgtFZ', symObjAddr: 0x80, symBinAddr: 0x27B10, symSize: 0x20 }
  - { offset: 0xDAE93, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC15initWithWebView_14andMerchantKeyySo05WKWebF0C_SSSgtFZTo', symObjAddr: 0xA0, symBinAddr: 0x27B30, symSize: 0x70 }
  - { offset: 0xDAEE6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZTo', symObjAddr: 0x110, symBinAddr: 0x27BA0, symSize: 0x20 }
  - { offset: 0xDAF27, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14setPaymentDatayySDys11AnyHashableVypGF', symObjAddr: 0x130, symBinAddr: 0x27BC0, symSize: 0xA0 }
  - { offset: 0xDAFB0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14setPaymentDatayySDys11AnyHashableVypGFTo', symObjAddr: 0x1D0, symBinAddr: 0x27C60, symSize: 0x100 }
  - { offset: 0xDB002, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKF', symObjAddr: 0x2D0, symBinAddr: 0x27D60, symSize: 0x10 }
  - { offset: 0xDB038, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKFTo', symObjAddr: 0x2E0, symBinAddr: 0x27D70, symSize: 0x60 }
  - { offset: 0xDB06F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC5closeyyF', symObjAddr: 0x340, symBinAddr: 0x27DD0, symSize: 0x90 }
  - { offset: 0xDB0ED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC5closeyyFTo', symObjAddr: 0x3D0, symBinAddr: 0x27E60, symSize: 0xB0 }
  - { offset: 0xDB146, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCfD', symObjAddr: 0x480, symBinAddr: 0x27F10, symSize: 0x30 }
  - { offset: 0xDB17D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZTf4d_n', symObjAddr: 0x4C0, symBinAddr: 0x27F50, symSize: 0xE0 }
  - { offset: 0xDB1D9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKFTf4dd_n', symObjAddr: 0x5A0, symBinAddr: 0x28030, symSize: 0x100 }
  - { offset: 0xDB3CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC14sharedInstanceACvpZ', symObjAddr: 0x3648, symBinAddr: 0xB6358, symSize: 0x0 }
  - { offset: 0xDB43F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC14sharedInstance_WZ', symObjAddr: 0x0, symBinAddr: 0x28200, symSize: 0x30 }
  - { offset: 0xDB47D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityCMa', symObjAddr: 0x60, symBinAddr: 0x28240, symSize: 0x20 }
  - { offset: 0xDB491, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwxx', symObjAddr: 0x1C0, symBinAddr: 0x28280, symSize: 0x30 }
  - { offset: 0xDB4A5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwcp', symObjAddr: 0x1F0, symBinAddr: 0x282B0, symSize: 0x70 }
  - { offset: 0xDB4B9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwca', symObjAddr: 0x260, symBinAddr: 0x28320, symSize: 0x90 }
  - { offset: 0xDB4CD, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0x2F0, symBinAddr: 0x283B0, symSize: 0x20 }
  - { offset: 0xDB4E1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwta', symObjAddr: 0x310, symBinAddr: 0x283D0, symSize: 0x60 }
  - { offset: 0xDB4F5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwet', symObjAddr: 0x370, symBinAddr: 0x28430, symSize: 0x40 }
  - { offset: 0xDB509, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwst', symObjAddr: 0x3B0, symBinAddr: 0x28470, symSize: 0x50 }
  - { offset: 0xDB51D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVMa', symObjAddr: 0x400, symBinAddr: 0x284C0, symSize: 0x10 }
  - { offset: 0xDB568, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityCfd', symObjAddr: 0x30, symBinAddr: 0x28230, symSize: 0x10 }
  - { offset: 0xDB71C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15TargetConstantsCMa', symObjAddr: 0x20, symBinAddr: 0x284D0, symSize: 0x20 }
  - { offset: 0xDB8CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC14sharedInstance33_C8B8A67CD7964AACF65D90AF0C57FAABLLACvpZ', symObjAddr: 0xA168, symBinAddr: 0xA3188, symSize: 0x0 }
  - { offset: 0xDBC22, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11isSetupDone33_C8B8A67CD7964AACF65D90AF0C57FAABLLSbvpZ', symObjAddr: 0xA170, symBinAddr: 0xA3190, symSize: 0x0 }
  - { offset: 0xDBC3D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11base62chars33_C8B8A67CD7964AACF65D90AF0C57FAABLLSaySJGvpZ', symObjAddr: 0xA178, symBinAddr: 0xA3198, symSize: 0x0 }
  - { offset: 0xDBFF3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tF', symObjAddr: 0x2F00, symBinAddr: 0x2B3F0, symSize: 0x820 }
  - { offset: 0xDC4A3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tFy10Foundation10URLRequestV_AH4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x84C0, symBinAddr: 0x30980, symSize: 0x10 }
  - { offset: 0xDC4EF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x84D0, symBinAddr: 0x30990, symSize: 0x10 }
  - { offset: 0xDC5FF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC14sharedInstance33_C8B8A67CD7964AACF65D90AF0C57FAABLL_WZ', symObjAddr: 0x5790, symBinAddr: 0x2DC50, symSize: 0x40 }
  - { offset: 0xDC696, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TR', symObjAddr: 0x7B50, symBinAddr: 0x30010, symSize: 0x20 }
  - { offset: 0xDC6AA, size: 0x8, addend: 0x0, symName: '_$sIeg_IyB_TR', symObjAddr: 0x7B70, symBinAddr: 0x30030, symSize: 0x20 }
  - { offset: 0xDC6C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyF', symObjAddr: 0x7B90, symBinAddr: 0x30050, symSize: 0x750 }
  - { offset: 0xDCB1C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyFy10Foundation10URLRequestV_AE4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x84A0, symBinAddr: 0x30960, symSize: 0x10 }
  - { offset: 0xDCB68, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x84B0, symBinAddr: 0x30970, symSize: 0x10 }
  - { offset: 0xDCBB5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCMa', symObjAddr: 0x83E0, symBinAddr: 0x308A0, symSize: 0x20 }
  - { offset: 0xDCBEA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11base62chars33_C8B8A67CD7964AACF65D90AF0C57FAABLL_WZ', symObjAddr: 0x8400, symBinAddr: 0x308C0, symSize: 0xA0 }
  - { offset: 0xDCD07, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFyp_Tg5', symObjAddr: 0x85C0, symBinAddr: 0x30A80, symSize: 0x120 }
  - { offset: 0xDCE8B, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSs_Tg5', symObjAddr: 0x86E0, symBinAddr: 0x30BA0, symSize: 0x120 }
  - { offset: 0xDD00F, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF8Razorpay11CheckPointsV_Tg5', symObjAddr: 0x8950, symBinAddr: 0x30E10, symSize: 0x120 }
  - { offset: 0xDD193, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x8A70, symBinAddr: 0x30F30, symSize: 0x100 }
  - { offset: 0xDD322, size: 0x8, addend: 0x0, symName: '_$sSBss17FixedWidthInteger14RawSignificandRpzrlE6random2in5usingxSnyxG_qd__ztSGRd__lFZSd_s27SystemRandomNumberGeneratorVTgm5', symObjAddr: 0x8B70, symBinAddr: 0x31030, symSize: 0xC0 }
  - { offset: 0xDD46A, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSS_Tgq5', symObjAddr: 0x8C30, symBinAddr: 0x310F0, symSize: 0xE0 }
  - { offset: 0xDD497, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSJ_Tgmq5', symObjAddr: 0x8D10, symBinAddr: 0x311D0, symSize: 0x80 }
  - { offset: 0xDD4CD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC5setup33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyFyyXEfU_TA', symObjAddr: 0x9330, symBinAddr: 0x317F0, symSize: 0x20 }
  - { offset: 0xDD4EC, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x9370, symBinAddr: 0x31830, symSize: 0x20 }
  - { offset: 0xDD515, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x9390, symBinAddr: 0x31850, symSize: 0x20 }
  - { offset: 0xDD529, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x93B0, symBinAddr: 0x31870, symSize: 0x10 }
  - { offset: 0xDD53D, size: 0x8, addend: 0x0, symName: '_$sSaySo17OS_dispatch_queueC8DispatchE10AttributesVGMa', symObjAddr: 0x9480, symBinAddr: 0x31880, symSize: 0x50 }
  - { offset: 0xDD551, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16readBatchPayload3keyypSgSS_tFAFyXEfU_TA', symObjAddr: 0x94D0, symBinAddr: 0x318D0, symSize: 0x20 }
  - { offset: 0xDD565, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptFyyYbcfU_TA', symObjAddr: 0x9560, symBinAddr: 0x31930, symSize: 0x20 }
  - { offset: 0xDD579, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyS2S__GWOe', symObjAddr: 0x9580, symBinAddr: 0x31950, symSize: 0x20 }
  - { offset: 0xDD62E, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0x9D60, symBinAddr: 0x32100, symSize: 0x40 }
  - { offset: 0xDD642, size: 0x8, addend: 0x0, symName: '_$ss18ReversedCollectionVySSGMa', symObjAddr: 0x9E70, symBinAddr: 0x32140, symSize: 0x50 }
  - { offset: 0xDD656, size: 0x8, addend: 0x0, symName: '_$sS2SSKsWl', symObjAddr: 0x9EC0, symBinAddr: 0x32190, symSize: 0x30 }
  - { offset: 0xDD66A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17resetBatchPayload33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyFyyXEfU_TA', symObjAddr: 0x9EF0, symBinAddr: 0x321C0, symSize: 0x20 }
  - { offset: 0xDD67E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16createNewSessionyyFyyXEfU_TA', symObjAddr: 0x9F30, symBinAddr: 0x32200, symSize: 0x20 }
  - { offset: 0xDD692, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20readFullBatchPayloadSDySSypGyFAEyXEfU_TA', symObjAddr: 0x9FA0, symBinAddr: 0x32240, symSize: 0x10 }
  - { offset: 0xDD7AD, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_yps11AnyHashableVypTg5', symObjAddr: 0x1220, symBinAddr: 0x29710, symSize: 0x550 }
  - { offset: 0xDD8D6, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_S2SypTg5', symObjAddr: 0x1770, symBinAddr: 0x29C60, symSize: 0x450 }
  - { offset: 0xDDA0D, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SDyS2SGSSypTg5', symObjAddr: 0x1BC0, symBinAddr: 0x2A0B0, symSize: 0x460 }
  - { offset: 0xDDB44, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SSSgSSypTg5', symObjAddr: 0x2020, symBinAddr: 0x2A510, symSize: 0x460 }
  - { offset: 0xDDC7B, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SDys11AnyHashableVypGAEypTg5', symObjAddr: 0x2480, symBinAddr: 0x2A970, symSize: 0x540 }
  - { offset: 0xDDDA6, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SSs11AnyHashableVypTg5', symObjAddr: 0x29C0, symBinAddr: 0x2AEB0, symSize: 0x540 }
  - { offset: 0xDDF73, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF8Razorpay27CheckoutBridgeErrorResponseC0eG0V10CodingKeysO_Tg5', symObjAddr: 0x50F0, symBinAddr: 0x2D5E0, symSize: 0x90 }
  - { offset: 0xDDFF7, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF8Razorpay0D10ErrorLevelO_Tg5', symObjAddr: 0x5180, symBinAddr: 0x2D670, symSize: 0xE0 }
  - { offset: 0xDE118, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x84E0, symBinAddr: 0x309A0, symSize: 0xE0 }
  - { offset: 0xDE25A, size: 0x8, addend: 0x0, symName: '_$sSlsE6prefixy11SubSequenceQzSiFSS_Tg5Tf4ng_n', symObjAddr: 0x9730, symBinAddr: 0x31B00, symSize: 0x90 }
  - { offset: 0xDE471, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17updateBasePayloadyyF', symObjAddr: 0x0, symBinAddr: 0x284F0, symSize: 0x150 }
  - { offset: 0xDE533, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16createNewSessionyyF', symObjAddr: 0x150, symBinAddr: 0x28640, symSize: 0x6A0 }
  - { offset: 0xDE6B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC10trackEvent9eventNameySS_tF', symObjAddr: 0x7F0, symBinAddr: 0x28CE0, symSize: 0x1C0 }
  - { offset: 0xDE837, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC10trackEvent9eventName16havingPropertiesySS_SDySSypGtF', symObjAddr: 0x9B0, symBinAddr: 0x28EA0, symSize: 0x290 }
  - { offset: 0xDE9FF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18addPaymentProperty9havingKey8andValueySS_yptF', symObjAddr: 0xC40, symBinAddr: 0x29130, symSize: 0x1C0 }
  - { offset: 0xDEAC1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16addOrderProperty9havingKey8andValueySS_yptF', symObjAddr: 0xE00, symBinAddr: 0x292F0, symSize: 0x2C0 }
  - { offset: 0xDEBA4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC9trackPage_9havingUrlySS_SStF', symObjAddr: 0x10C0, symBinAddr: 0x295B0, symSize: 0x160 }
  - { offset: 0xDEDD0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyF', symObjAddr: 0x3720, symBinAddr: 0x2BC10, symSize: 0x610 }
  - { offset: 0xDEF47, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyFy10Foundation10URLRequestV_AE4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x82E0, symBinAddr: 0x307A0, symSize: 0x10 }
  - { offset: 0xDEF8F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x82F0, symBinAddr: 0x307B0, symSize: 0x10 }
  - { offset: 0xDEFF8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC36gatherAnalyticsDataForCustomCheckoutSDySSypGyF', symObjAddr: 0x3D30, symBinAddr: 0x2C220, symSize: 0x620 }
  - { offset: 0xDF1B2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC19reachabilityChangedyyAA20InternetConnectivityOF', symObjAddr: 0x4350, symBinAddr: 0x2C840, symSize: 0xDA0 }
  - { offset: 0xDF7F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC27networkAndLocaleInformationSS_S2StSgyF', symObjAddr: 0x5260, symBinAddr: 0x2D750, symSize: 0x400 }
  - { offset: 0xDF8C8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC23fetchBasePayloadContextSDySSypGSgyF', symObjAddr: 0x5690, symBinAddr: 0x2DB50, symSize: 0x100 }
  - { offset: 0xDF978, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCACyc33_C8B8A67CD7964AACF65D90AF0C57FAABLlfc', symObjAddr: 0x57D0, symBinAddr: 0x2DC90, symSize: 0x240 }
  - { offset: 0xDFA4F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18inflateBasePayload33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyF', symObjAddr: 0x5A10, symBinAddr: 0x2DED0, symSize: 0x1DA0 }
  - { offset: 0xE0636, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16readBatchPayload3keyypSgSS_tFAFyXEfU_', symObjAddr: 0x77B0, symBinAddr: 0x2FC70, symSize: 0xE0 }
  - { offset: 0xE068D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20readFullBatchPayloadSDySSypGyFAEyXEfU_', symObjAddr: 0x7890, symBinAddr: 0x2FD50, symSize: 0x50 }
  - { offset: 0xE06C7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptF', symObjAddr: 0x78E0, symBinAddr: 0x2FDA0, symSize: 0x190 }
  - { offset: 0xE0720, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptFyyYbcfU_', symObjAddr: 0x7A70, symBinAddr: 0x2FF30, symSize: 0xE0 }
  - { offset: 0xE078E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCfd', symObjAddr: 0x8360, symBinAddr: 0x30820, symSize: 0x60 }
  - { offset: 0xE07B1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCfD', symObjAddr: 0x83C0, symBinAddr: 0x30880, symSize: 0x20 }
  - { offset: 0xE080B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20createBaseTrackEvent33_C8B8A67CD7964AACF65D90AF0C57FAABLLySDySSypGSSFTf4nd_n', symObjAddr: 0x8D90, symBinAddr: 0x31250, symSize: 0x190 }
  - { offset: 0xE0996, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11getDeviceID33_C8B8A67CD7964AACF65D90AF0C57FAABLLSSyFTf4d_n', symObjAddr: 0x8F20, symBinAddr: 0x313E0, symSize: 0x260 }
  - { offset: 0xE0A36, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17getSharedInstance17havingMerchantKeyACSS_tFZTf4nd_g', symObjAddr: 0x9180, symBinAddr: 0x31640, symSize: 0x1B0 }
  - { offset: 0xE0AC7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV8tobase626numberSSs5Int64V_tFZTf4nd_n', symObjAddr: 0x95A0, symBinAddr: 0x31970, symSize: 0x190 }
  - { offset: 0xE0BE8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11getUniqueIdySSSdFZTf4nd_n', symObjAddr: 0x97C0, symBinAddr: 0x31B90, symSize: 0x1A0 }
  - { offset: 0xE0D7A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC30gatherAnalyticsDataForCheckoutSDySSypGyFTf4d_n', symObjAddr: 0x9960, symBinAddr: 0x31D30, symSize: 0x3D0 }
  - { offset: 0xE11D2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC11mercahntKeySSSgvpZ', symObjAddr: 0x14E80, symBinAddr: 0xB6360, symSize: 0x0 }
  - { offset: 0xE1482, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0xA60, symBinAddr: 0x32D80, symSize: 0x60 }
  - { offset: 0xE14BA, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0xAC0, symBinAddr: 0x32DE0, symSize: 0x30 }
  - { offset: 0xE14E7, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xAF0, symBinAddr: 0x32E10, symSize: 0x80 }
  - { offset: 0xE1591, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0xB70, symBinAddr: 0x32E90, symSize: 0x60 }
  - { offset: 0xE160C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFs11AnyHashableV_ypTg5', symObjAddr: 0xBD0, symBinAddr: 0x32EF0, symSize: 0x70 }
  - { offset: 0xE1692, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0xC40, symBinAddr: 0x32F60, symSize: 0x50 }
  - { offset: 0xE1725, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0xC90, symBinAddr: 0x32FB0, symSize: 0xE0 }
  - { offset: 0xE1798, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0xD70, symBinAddr: 0x33090, symSize: 0xC0 }
  - { offset: 0xE17C5, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xE30, symBinAddr: 0x33150, symSize: 0x180 }
  - { offset: 0xE18B0, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ypTg5', symObjAddr: 0xFB0, symBinAddr: 0x332D0, symSize: 0xC0 }
  - { offset: 0xE1905, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFs11AnyHashableV_ypTg5', symObjAddr: 0x1070, symBinAddr: 0x33390, symSize: 0xB0 }
  - { offset: 0xE1956, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFs11AnyHashableV_ypTg5', symObjAddr: 0x1120, symBinAddr: 0x33440, symSize: 0xE0 }
  - { offset: 0xE19D4, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFs11AnyHashableV_ypTg5', symObjAddr: 0x1220, symBinAddr: 0x33540, symSize: 0xA0 }
  - { offset: 0xE1A1C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x1390, symBinAddr: 0x336B0, symSize: 0x260 }
  - { offset: 0xE1AA5, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFs11AnyHashableV_ypTg5', symObjAddr: 0x15F0, symBinAddr: 0x33910, symSize: 0x260 }
  - { offset: 0xE1B1A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0x1850, symBinAddr: 0x33B70, symSize: 0x220 }
  - { offset: 0xE1BEC, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x1A70, symBinAddr: 0x33D90, symSize: 0x220 }
  - { offset: 0xE1C8F, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFs11AnyHashableV_ypTg5', symObjAddr: 0x1C90, symBinAddr: 0x33FB0, symSize: 0x230 }
  - { offset: 0xE1D1B, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRs11AnyHashableV_ypTg5070$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_s11cD7V_ypTg5Tf3nnpf_n', symObjAddr: 0x1EC0, symBinAddr: 0x341E0, symSize: 0x30 }
  - { offset: 0xE1D95, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV5merge_8isUnique16uniquingKeysWithyqd__n_Sbq_q__q_tKXEtKSTRd__x_q_t7ElementRtd__lFs11AnyHashableV_yps15LazyMapSequenceVySDyAIypGAI_yptGTg5079$s8Razorpay24SwiftCompatibilityHelperC19combineDictionaries7dictOne0G3TwoSDys11jK25VypGAI_AItFZypyp_yptXEfU_Tf1nncn_n', symObjAddr: 0x1EF0, symBinAddr: 0x34210, symSize: 0x2E0 }
  - { offset: 0xE1F09, size: 0x8, addend: 0x0, symName: '_$ss15LazyMapSequenceV8IteratorV4nextq_SgyFSDys11AnyHashableVypG_AH_yptTg5', symObjAddr: 0x21D0, symBinAddr: 0x344F0, symSize: 0x340 }
  - { offset: 0xE222B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCSgSgWOe', symObjAddr: 0x3000, symBinAddr: 0x35230, symSize: 0x20 }
  - { offset: 0xE223F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCMa', symObjAddr: 0x3020, symBinAddr: 0x35250, symSize: 0x20 }
  - { offset: 0xE2253, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCSgSgWOy', symObjAddr: 0x31F0, symBinAddr: 0x35360, symSize: 0x20 }
  - { offset: 0xE231E, size: 0x8, addend: 0x0, symName: '_$sSD7merging_16uniquingKeysWithSDyxq_GACn_q_q__q_tKXEtKFs11AnyHashableV_ypTg5079$s8Razorpay24SwiftCompatibilityHelperC19combineDictionaries7dictOne0G3TwoSDys11eF25VypGAI_AItFZypyp_yptXEfU_Tf1ncn_n', symObjAddr: 0x9F0, symBinAddr: 0x32D10, symSize: 0x70 }
  - { offset: 0xE2428, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x2510, symBinAddr: 0x34830, symSize: 0xF0 }
  - { offset: 0xE254D, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x2600, symBinAddr: 0x34920, symSize: 0x100 }
  - { offset: 0xE268C, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTgm5Tf4g_n', symObjAddr: 0x2700, symBinAddr: 0x34A20, symSize: 0xE0 }
  - { offset: 0xE27C5, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCs11AnyHashableV_ypTgm5Tf4g_n', symObjAddr: 0x2800, symBinAddr: 0x34B20, symSize: 0x100 }
  - { offset: 0xE28F0, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_12CoreGraphics7CGFloatVTgm5Tf4g_n', symObjAddr: 0x2A10, symBinAddr: 0x34C40, symSize: 0xE0 }
  - { offset: 0xE2A2F, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSSgTgm5Tf4g_n', symObjAddr: 0x2B10, symBinAddr: 0x34D40, symSize: 0x100 }
  - { offset: 0xE2B6E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo38UIApplicationOpenExternalURLOptionsKeya_ypTgm5Tf4g_n', symObjAddr: 0x2C10, symBinAddr: 0x34E40, symSize: 0xF0 }
  - { offset: 0xE2C99, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SiTgm5Tf4g_n', symObjAddr: 0x2D20, symBinAddr: 0x34F50, symSize: 0xE0 }
  - { offset: 0xE2DD2, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypSgTgm5Tf4g_n', symObjAddr: 0x2E00, symBinAddr: 0x35030, symSize: 0xF0 }
  - { offset: 0xE2F1D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCACycfC', symObjAddr: 0x0, symBinAddr: 0x32320, symSize: 0x30 }
  - { offset: 0xE2F50, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCACycfc', symObjAddr: 0x30, symBinAddr: 0x32350, symSize: 0x20 }
  - { offset: 0xE2F92, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC13analyticsUtilAA09AnalyticsF0CSgvg', symObjAddr: 0x50, symBinAddr: 0x32370, symSize: 0xB0 }
  - { offset: 0xE3002, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC12getSessionIdSSSgyF', symObjAddr: 0x100, symBinAddr: 0x32420, symSize: 0x90 }
  - { offset: 0xE3055, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC14getMercahntKeySSSgyF', symObjAddr: 0x190, symBinAddr: 0x324B0, symSize: 0x30 }
  - { offset: 0xE3070, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC17getHTMLLoaingPageSSyF', symObjAddr: 0x1C0, symBinAddr: 0x324E0, symSize: 0x30 }
  - { offset: 0xE3091, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC16isCFBEnabledUserSbyF', symObjAddr: 0x1F0, symBinAddr: 0x32510, symSize: 0x10 }
  - { offset: 0xE315B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC26getBaseAnalyticsPropertiesSDys11AnyHashableVypGyF', symObjAddr: 0x200, symBinAddr: 0x32520, symSize: 0x5F0 }
  - { offset: 0xE34C4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC10trackEvent9eventName7payloadySS_SDys11AnyHashableVypGtF', symObjAddr: 0x7F0, symBinAddr: 0x32B10, symSize: 0xC0 }
  - { offset: 0xE358F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC21submitAnalyticsEventsyyF', symObjAddr: 0x8B0, symBinAddr: 0x32BD0, symSize: 0x70 }
  - { offset: 0xE35E6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC27gatherAnalyticsDataCustomUISDySSypGSgyF', symObjAddr: 0x920, symBinAddr: 0x32C40, symSize: 0x90 }
  - { offset: 0xE3638, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCfd', symObjAddr: 0x9B0, symBinAddr: 0x32CD0, symSize: 0x20 }
  - { offset: 0xE3669, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCfD', symObjAddr: 0x9D0, symBinAddr: 0x32CF0, symSize: 0x20 }
  - { offset: 0xE396C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorCMa', symObjAddr: 0x200, symBinAddr: 0x35580, symSize: 0x20 }
  - { offset: 0xE39A7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorC12awakeFromNibyyFTo', symObjAddr: 0x0, symBinAddr: 0x35380, symSize: 0xF0 }
  - { offset: 0xE3A0C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x110, symBinAddr: 0x35490, symSize: 0x60 }
  - { offset: 0xE3A41, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorCfD', symObjAddr: 0x1D0, symBinAddr: 0x35550, symSize: 0x30 }
  - { offset: 0xE3BD5, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x355A0, symSize: 0x240 }
  - { offset: 0xE3C69, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x355A0, symSize: 0x240 }
  - { offset: 0xE3D42, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZySo0A6ActionCcfU_TA', symObjAddr: 0x270, symBinAddr: 0x35810, symSize: 0x30 }
  - { offset: 0xE3D74, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2A0, symBinAddr: 0x35840, symSize: 0x20 }
  - { offset: 0xE3D88, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2C0, symBinAddr: 0x35860, symSize: 0x10 }
  - { offset: 0xE3F3A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCACycfc', symObjAddr: 0x10, symBinAddr: 0x35870, symSize: 0xF0 }
  - { offset: 0xE40AB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCMa', symObjAddr: 0x560, symBinAddr: 0x35DC0, symSize: 0x20 }
  - { offset: 0xE411A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCACycfc', symObjAddr: 0x10, symBinAddr: 0x35870, symSize: 0xF0 }
  - { offset: 0xE4215, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21startReceivingUpdatesyyKF', symObjAddr: 0x100, symBinAddr: 0x35960, symSize: 0x160 }
  - { offset: 0xE429C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21broadcastNotification12notificationy10Foundation0E0V_tF', symObjAddr: 0x260, symBinAddr: 0x35AC0, symSize: 0xD0 }
  - { offset: 0xE435C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21broadcastNotification12notificationy10Foundation0E0V_tFTo', symObjAddr: 0x330, symBinAddr: 0x35B90, symSize: 0x80 }
  - { offset: 0xE4375, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC20stopReceivingUpdatesyyF', symObjAddr: 0x3B0, symBinAddr: 0x35C10, symSize: 0x170 }
  - { offset: 0xE4409, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCfD', symObjAddr: 0x520, symBinAddr: 0x35D80, symSize: 0x40 }
  - { offset: 0xE45B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwxx', symObjAddr: 0x90, symBinAddr: 0x35DE0, symSize: 0x40 }
  - { offset: 0xE45CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwcp', symObjAddr: 0x130, symBinAddr: 0x35E20, symSize: 0xC0 }
  - { offset: 0xE45E0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwca', symObjAddr: 0x1F0, symBinAddr: 0x35EE0, symSize: 0xE0 }
  - { offset: 0xE45F4, size: 0x8, addend: 0x0, symName: ___swift_memcpy65_8, symObjAddr: 0x2D0, symBinAddr: 0x35FC0, symSize: 0x30 }
  - { offset: 0xE4608, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwta', symObjAddr: 0x300, symBinAddr: 0x35FF0, symSize: 0x70 }
  - { offset: 0xE461C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwet', symObjAddr: 0x370, symBinAddr: 0x36060, symSize: 0x50 }
  - { offset: 0xE4630, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwst', symObjAddr: 0x3C0, symBinAddr: 0x360B0, symSize: 0x60 }
  - { offset: 0xE4644, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwug', symObjAddr: 0x420, symBinAddr: 0x36110, symSize: 0x10 }
  - { offset: 0xE4658, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwui', symObjAddr: 0x440, symBinAddr: 0x36120, symSize: 0x10 }
  - { offset: 0xE466C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOMa', symObjAddr: 0x450, symBinAddr: 0x36130, symSize: 0x10 }
  - { offset: 0xE46F2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOb', symObjAddr: 0x5E0, symBinAddr: 0x362C0, symSize: 0x30 }
  - { offset: 0xE4744, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeO3URLSSyF', symObjAddr: 0x460, symBinAddr: 0x36140, symSize: 0x180 }
  - { offset: 0xE4A15, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSON_WZ', symObjAddr: 0x0, symBinAddr: 0x362F0, symSize: 0x240 }
  - { offset: 0xE4A39, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSONSDys11AnyHashableVypGvpZ', symObjAddr: 0x2C68, symBinAddr: 0xB6370, symSize: 0x0 }
  - { offset: 0xE4A94, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSON_WZ', symObjAddr: 0x0, symBinAddr: 0x362F0, symSize: 0x240 }
  - { offset: 0xE4DE0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelC8delegate24analyticsUtilityInstance11checkoutURL13otpelfEnabled7options9publicKey17isDataDelegateSet13timeStampOpen0stU6Millis26arrExternalPaymentEntitiesAcA0aB10VCDelegate_pSg_AA13AnalyticsUtilCSSSbSDys11AnyHashableVypGSSSbS2iSayAA06PluginyQ0_pGSgtcfc', symObjAddr: 0x0, symBinAddr: 0x36530, symSize: 0xD0 }
  - { offset: 0xE4E79, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelCMa', symObjAddr: 0x130, symBinAddr: 0x36660, symSize: 0x20 }
  - { offset: 0xE4F14, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelC8delegate24analyticsUtilityInstance11checkoutURL13otpelfEnabled7options9publicKey17isDataDelegateSet13timeStampOpen0stU6Millis26arrExternalPaymentEntitiesAcA0aB10VCDelegate_pSg_AA13AnalyticsUtilCSSSbSDys11AnyHashableVypGSSSbS2iSayAA06PluginyQ0_pGSgtcfc', symObjAddr: 0x0, symBinAddr: 0x36530, symSize: 0xD0 }
  - { offset: 0xE5003, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelCfD', symObjAddr: 0xD0, symBinAddr: 0x36600, symSize: 0x60 }
  - { offset: 0xE53A6, size: 0x8, addend: 0x0, symName: '_$ss30_dictionaryDownCastConditionalySDyq0_q1_GSgSDyxq_GSHRzSHR0_r2_lFs11AnyHashableV_ypSSypTg5', symObjAddr: 0xF30, symBinAddr: 0x375B0, symSize: 0x4D0 }
  - { offset: 0xE55BC, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE6starts4withSbqd___tSTRd__AAQyd__ABRSlFSS_SSTg5', symObjAddr: 0x38E0, symBinAddr: 0x39F60, symSize: 0x1A0 }
  - { offset: 0xE573F, size: 0x8, addend: 0x0, symName: '_$sSo18NSLayoutConstraintC4item9attribute9relatedBy6toItemAD10multiplier8constantAByp_So0A9AttributeVSo0A8RelationVypSgAJ12CoreGraphics7CGFloatVAPtcfCTO', symObjAddr: 0x0, symBinAddr: 0x36680, symSize: 0x140 }
  - { offset: 0xE5EB5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfETo', symObjAddr: 0x2860, symBinAddr: 0x38EE0, symSize: 0xB0 }
  - { offset: 0xE5EE5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCMa', symObjAddr: 0x2C30, symBinAddr: 0x392B0, symSize: 0x20 }
  - { offset: 0xE5F1A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16sendCredResponse33_813001DEEE20F6D789A6CA33DA760B43LL4withySi_tF', symObjAddr: 0x2C50, symBinAddr: 0x392D0, symSize: 0x3E0 }
  - { offset: 0xE61CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15publishCredData33_813001DEEE20F6D789A6CA33DA760B43LLyySo14NSNotificationCF', symObjAddr: 0x3030, symBinAddr: 0x396B0, symSize: 0x250 }
  - { offset: 0xE62B1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15publishCredData33_813001DEEE20F6D789A6CA33DA760B43LLyySo14NSNotificationCFTo', symObjAddr: 0x3280, symBinAddr: 0x39900, symSize: 0x50 }
  - { offset: 0xE62CD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSF', symObjAddr: 0x32D0, symBinAddr: 0x39950, symSize: 0x610 }
  - { offset: 0xE660D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_Tf2in_n', symObjAddr: 0x3A80, symBinAddr: 0x3A100, symSize: 0xE0 }
  - { offset: 0xE6659, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU_', symObjAddr: 0x3B60, symBinAddr: 0x3A1E0, symSize: 0x260 }
  - { offset: 0xE679D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU0_', symObjAddr: 0x3DC0, symBinAddr: 0x3A440, symSize: 0x320 }
  - { offset: 0xE69CB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17gestureRecognizer_33shouldRecognizeSimultaneouslyWithSbSo09UIGestureE0C_AGtFTo', symObjAddr: 0x40E0, symBinAddr: 0x3A760, symSize: 0x10 }
  - { offset: 0xE6A17, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_Tf2in_nTA', symObjAddr: 0x41C0, symBinAddr: 0x3A7A0, symSize: 0x20 }
  - { offset: 0xE6A2B, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x41E0, symBinAddr: 0x3A7C0, symSize: 0x20 }
  - { offset: 0xE6A3F, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4200, symBinAddr: 0x3A7E0, symSize: 0x10 }
  - { offset: 0xE6A53, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU_TA', symObjAddr: 0x4340, symBinAddr: 0x3A820, symSize: 0x10 }
  - { offset: 0xE6A67, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU0_TA', symObjAddr: 0x4350, symBinAddr: 0x3A830, symSize: 0x10 }
  - { offset: 0xE6B39, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x59A0, symBinAddr: 0x3BD80, symSize: 0x10 }
  - { offset: 0xE6B4D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_TA', symObjAddr: 0x59C0, symBinAddr: 0x3BDA0, symSize: 0x20 }
  - { offset: 0xE6B61, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_yyScMYccfU_TA', symObjAddr: 0x5A30, symBinAddr: 0x3BE10, symSize: 0x20 }
  - { offset: 0xE6B75, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFySo13UIAlertActionCcfU_TA', symObjAddr: 0x5B50, symBinAddr: 0x3BE30, symSize: 0x20 }
  - { offset: 0xE6BC1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17activityIndicatorAA014CustomActivityE0CSgvgTo', symObjAddr: 0x140, symBinAddr: 0x367C0, symSize: 0x20 }
  - { offset: 0xE6C21, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17activityIndicatorAA014CustomActivityE0CSgvsTo', symObjAddr: 0x160, symBinAddr: 0x367E0, symSize: 0x20 }
  - { offset: 0xE6D1E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadViewyyF', symObjAddr: 0x180, symBinAddr: 0x36800, symSize: 0x160 }
  - { offset: 0xE6E05, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadViewyyFTo', symObjAddr: 0x2E0, symBinAddr: 0x36960, symSize: 0x30 }
  - { offset: 0xE6E45, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC11viewDidLoadyyF', symObjAddr: 0x310, symBinAddr: 0x36990, symSize: 0x140 }
  - { offset: 0xE6EC4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC11viewDidLoadyyFTo', symObjAddr: 0x450, symBinAddr: 0x36AD0, symSize: 0x30 }
  - { offset: 0xE6EEC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15dismissKeyboardyyFTo', symObjAddr: 0x480, symBinAddr: 0x36B00, symSize: 0x70 }
  - { offset: 0xE6F93, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14viewWillAppearyySbFTo', symObjAddr: 0x4F0, symBinAddr: 0x36B70, symSize: 0x80 }
  - { offset: 0xE704B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13viewDidAppearyySbF', symObjAddr: 0x570, symBinAddr: 0x36BF0, symSize: 0x2A0 }
  - { offset: 0xE71BA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13viewDidAppearyySbFTo', symObjAddr: 0x810, symBinAddr: 0x36E90, symSize: 0x30 }
  - { offset: 0xE71E2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17viewWillDisappearyySbF', symObjAddr: 0x840, symBinAddr: 0x36EC0, symSize: 0xC0 }
  - { offset: 0xE7233, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17viewWillDisappearyySbFTo', symObjAddr: 0x900, symBinAddr: 0x36F80, symSize: 0x30 }
  - { offset: 0xE724E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFTo', symObjAddr: 0x930, symBinAddr: 0x36FB0, symSize: 0x50 }
  - { offset: 0xE72A8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyF', symObjAddr: 0x980, symBinAddr: 0x37000, symSize: 0x540 }
  - { offset: 0xE7517, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0xEC0, symBinAddr: 0x37540, symSize: 0x70 }
  - { offset: 0xE7562, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_', symObjAddr: 0x1400, symBinAddr: 0x37A80, symSize: 0x210 }
  - { offset: 0xE75BF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_yyScMYccfU_', symObjAddr: 0x1610, symBinAddr: 0x37C90, symSize: 0x60 }
  - { offset: 0xE7702, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC29payWithExternalPaymentManager7options9publicKey03arrfG8EntitiesySDys11AnyHashableVypG_SSSayAA06PluginG8Delegate_pGtF', symObjAddr: 0x1670, symBinAddr: 0x37CF0, symSize: 0x120 }
  - { offset: 0xE7888, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13cancelPayment33_813001DEEE20F6D789A6CA33DA760B43LLyyF', symObjAddr: 0x1790, symBinAddr: 0x37E10, symSize: 0x460 }
  - { offset: 0xE7ACF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19addWebViewAsSubview33_813001DEEE20F6D789A6CA33DA760B43LL03webF0ySo05WKWebF0C_tF', symObjAddr: 0x1BF0, symBinAddr: 0x38270, symSize: 0x730 }
  - { offset: 0xE7D75, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC24sendUserAgentToAnalytics33_813001DEEE20F6D789A6CA33DA760B43LL15webViewResponse5erroryypSg_s5Error_pSgtF', symObjAddr: 0x2320, symBinAddr: 0x389A0, symSize: 0x390 }
  - { offset: 0xE7F71, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12closeWebViewyyF', symObjAddr: 0x26B0, symBinAddr: 0x38D30, symSize: 0x120 }
  - { offset: 0xE7FD7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfD', symObjAddr: 0x27D0, symBinAddr: 0x38E50, symSize: 0x40 }
  - { offset: 0xE8009, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfDTo', symObjAddr: 0x2810, symBinAddr: 0x38E90, symSize: 0x50 }
  - { offset: 0xE803D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x2910, symBinAddr: 0x38F90, symSize: 0x170 }
  - { offset: 0xE807E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x2A80, symBinAddr: 0x39100, symSize: 0x50 }
  - { offset: 0xE80FA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x2AD0, symBinAddr: 0x39150, symSize: 0x130 }
  - { offset: 0xE812B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2C00, symBinAddr: 0x39280, symSize: 0x30 }
  - { offset: 0xE81D5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15getMobileNumber33_813001DEEE20F6D789A6CA33DA760B43LL11fromPayloadSSSgSDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x43A0, symBinAddr: 0x3A840, symSize: 0x1D0 }
  - { offset: 0xE823E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC10getOrderId33_813001DEEE20F6D789A6CA33DA760B43LL11fromPayloadSSSgSDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x4570, symBinAddr: 0x3AA10, symSize: 0x100 }
  - { offset: 0xE837A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC23proceedToInitialization_7webviewyAA0aB5ModelC_AA0aB7WebViewCtFTf4nnd_n', symObjAddr: 0x4670, symBinAddr: 0x3AB10, symSize: 0xFB0 }
  - { offset: 0xE8A98, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFTf4dn_n', symObjAddr: 0x5620, symBinAddr: 0x3BAC0, symSize: 0x230 }
  - { offset: 0xE8B3F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15CredURIResponseO8rawValueAESgSS_tcfCTf4nd_n', symObjAddr: 0x5850, symBinAddr: 0x3BCF0, symSize: 0x70 }
  - { offset: 0xE8D26, size: 0x8, addend: 0x0, symName: '_$sSo8UIDeviceC8RazorpayE9modelNameSSvgTf4d_n', symObjAddr: 0x0, symBinAddr: 0x3BEF0, symSize: 0x23E0 }
  - { offset: 0xE8DD7, size: 0x8, addend: 0x0, symName: '_$sSo8UIDeviceC8RazorpayE9modelNameSSvgTf4d_n', symObjAddr: 0x0, symBinAddr: 0x3BEF0, symSize: 0x23E0 }
  - { offset: 0xE975A, size: 0x8, addend: 0x0, symName: '_$sSSSg5label_yp5valuetWOh', symObjAddr: 0x2470, symBinAddr: 0x3E2F0, symSize: 0x30 }
  - { offset: 0xE99E2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6LoggerCMa', symObjAddr: 0x20, symBinAddr: 0x3E320, symSize: 0x20 }
  - { offset: 0xE9B90, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC16internalInstance33_E1A96831138ED3F3E0A51B461F187E33LLACvpZ', symObjAddr: 0x1630, symBinAddr: 0xA38D8, symSize: 0x0 }
  - { offset: 0xE9C0B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC26startNSUncaughtExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFySo11NSExceptionCcfU_To', symObjAddr: 0x7A0, symBinAddr: 0x3EAE0, symSize: 0x30 }
  - { offset: 0xE9CD3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC16internalInstance33_E1A96831138ED3F3E0A51B461F187E33LL_WZ', symObjAddr: 0x0, symBinAddr: 0x3E340, symSize: 0x40 }
  - { offset: 0xE9E8E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterCMa', symObjAddr: 0xDA0, symBinAddr: 0x3F0E0, symSize: 0x20 }
  - { offset: 0xE9EA2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_TA', symObjAddr: 0xDE0, symBinAddr: 0x3F120, symSize: 0x20 }
  - { offset: 0xE9EB6, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xE00, symBinAddr: 0x3F140, symSize: 0x20 }
  - { offset: 0xE9ECA, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xE20, symBinAddr: 0x3F160, symSize: 0x10 }
  - { offset: 0xE9F47, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_yyScMYcXEfU_TA', symObjAddr: 0x1580, symBinAddr: 0x3F7D0, symSize: 0x20 }
  - { offset: 0xE9F5B, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x15C0, symBinAddr: 0x3F810, symSize: 0x20 }
  - { offset: 0xEA173, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_', symObjAddr: 0x40, symBinAddr: 0x3E380, symSize: 0x1E0 }
  - { offset: 0xEA28C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_yyScMYcXEfU_', symObjAddr: 0x220, symBinAddr: 0x3E560, symSize: 0x1D0 }
  - { offset: 0xEA49F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC26startNSUncaughtExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFySo11NSExceptionCcfU_', symObjAddr: 0x3F0, symBinAddr: 0x3E730, symSize: 0x3B0 }
  - { offset: 0xEA7CB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyF', symObjAddr: 0x7D0, symBinAddr: 0x3EB10, symSize: 0x270 }
  - { offset: 0xEA83D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_', symObjAddr: 0xA40, symBinAddr: 0x3ED80, symSize: 0x1C0 }
  - { offset: 0xEA8DD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU_To', symObjAddr: 0xC00, symBinAddr: 0x3EF40, symSize: 0x30 }
  - { offset: 0xEA943, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU0_To', symObjAddr: 0xC30, symBinAddr: 0x3EF70, symSize: 0x30 }
  - { offset: 0xEA9A9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU1_To', symObjAddr: 0xC60, symBinAddr: 0x3EFA0, symSize: 0x30 }
  - { offset: 0xEAA0F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU2_To', symObjAddr: 0xC90, symBinAddr: 0x3EFD0, symSize: 0x30 }
  - { offset: 0xEAA75, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU3_To', symObjAddr: 0xCC0, symBinAddr: 0x3F000, symSize: 0x30 }
  - { offset: 0xEAADB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU4_To', symObjAddr: 0xCF0, symBinAddr: 0x3F030, symSize: 0x30 }
  - { offset: 0xEAB41, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU5_To', symObjAddr: 0xD20, symBinAddr: 0x3F060, symSize: 0x30 }
  - { offset: 0xEABA7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU6_To', symObjAddr: 0xD50, symBinAddr: 0x3F090, symSize: 0x30 }
  - { offset: 0xEAC15, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterCfD', symObjAddr: 0xD80, symBinAddr: 0x3F0C0, symSize: 0x20 }
  - { offset: 0xEAC3A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFTf4d_n', symObjAddr: 0xEF0, symBinAddr: 0x3F170, symSize: 0x200 }
  - { offset: 0xEAC8C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC5startyyFZTf4d_n', symObjAddr: 0x10F0, symBinAddr: 0x3F370, symSize: 0xD0 }
  - { offset: 0xEAD2E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC29handleSwiftUnhandledException33_E1A96831138ED3F3E0A51B461F187E33LL16havingSignalCode03andR4Nameys5Int32V_SStFZTf4nnd_n', symObjAddr: 0x11C0, symBinAddr: 0x3F440, symSize: 0x370 }
  - { offset: 0xEB314, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC0B7DefaultSDySSypGvpZ', symObjAddr: 0x126D0, symBinAddr: 0xB6378, symSize: 0x0 }
  - { offset: 0xEB4D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC0B7Default_WZ', symObjAddr: 0x3A0, symBinAddr: 0x3FC10, symSize: 0x1CA0 }
  - { offset: 0xECDC9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigCMa', symObjAddr: 0x33B0, symBinAddr: 0x42C20, symSize: 0x20 }
  - { offset: 0xECE01, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x3420, symBinAddr: 0x42C50, symSize: 0x120 }
  - { offset: 0xECE4D, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSs_SiTg5', symObjAddr: 0x3540, symBinAddr: 0x42D70, symSize: 0x130 }
  - { offset: 0xECE87, size: 0x8, addend: 0x0, symName: '_$sSS8UTF8ViewV32withContiguousStorageIfAvailableyxSgxSRys5UInt8VGKXEKlFSiSg_Tg5', symObjAddr: 0x38E0, symBinAddr: 0x43110, symSize: 0x100 }
  - { offset: 0xECEDA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_AM4DataVSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x3A20, symBinAddr: 0x43250, symSize: 0x40 }
  - { offset: 0xECEEE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_TA', symObjAddr: 0x3A80, symBinAddr: 0x432B0, symSize: 0x20 }
  - { offset: 0xECF02, size: 0x8, addend: 0x0, symName: '_$sS2sSTsWl', symObjAddr: 0x3B60, symBinAddr: 0x432D0, symSize: 0x30 }
  - { offset: 0xECF16, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOwet', symObjAddr: 0x3BB0, symBinAddr: 0x43300, symSize: 0x80 }
  - { offset: 0xECF2A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOwst', symObjAddr: 0x3C30, symBinAddr: 0x43380, symSize: 0xD0 }
  - { offset: 0xECF3E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOMa', symObjAddr: 0x3D30, symBinAddr: 0x43450, symSize: 0x10 }
  - { offset: 0xECF52, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASQWb', symObjAddr: 0x3D40, symBinAddr: 0x43460, symSize: 0x10 }
  - { offset: 0xECF66, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOACSQAAWl', symObjAddr: 0x3D50, symBinAddr: 0x43470, symSize: 0x30 }
  - { offset: 0xECFCB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1F0, symBinAddr: 0x3FA60, symSize: 0x80 }
  - { offset: 0xED09E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH9hashValueSivgTW', symObjAddr: 0x270, symBinAddr: 0x3FAE0, symSize: 0x50 }
  - { offset: 0xED11C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2C0, symBinAddr: 0x3FB30, symSize: 0x30 }
  - { offset: 0xED16E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x2F0, symBinAddr: 0x3FB60, symSize: 0x50 }
  - { offset: 0xED343, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsEyxSgSScfCSi_Tgm5', symObjAddr: 0x30A0, symBinAddr: 0x42910, symSize: 0x220 }
  - { offset: 0xED552, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x3F870, symSize: 0x1F0 }
  - { offset: 0xED593, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x340, symBinAddr: 0x3FBB0, symSize: 0x30 }
  - { offset: 0xED5BC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x370, symBinAddr: 0x3FBE0, symSize: 0x30 }
  - { offset: 0xED658, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctF', symObjAddr: 0x2040, symBinAddr: 0x418B0, symSize: 0x2A0 }
  - { offset: 0xED749, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_AM4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x22E0, symBinAddr: 0x41B50, symSize: 0xDC0 }
  - { offset: 0xEDD07, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x32C0, symBinAddr: 0x42B30, symSize: 0xB0 }
  - { offset: 0xEDEC6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigCfD', symObjAddr: 0x3370, symBinAddr: 0x42BE0, symSize: 0x40 }
  - { offset: 0xEDF05, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x3D80, symBinAddr: 0x434A0, symSize: 0x70 }
  - { offset: 0xEE109, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tF', symObjAddr: 0x0, symBinAddr: 0x43510, symSize: 0x380 }
  - { offset: 0xEE197, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tFyyScMYccfU_Tf2ni_n', symObjAddr: 0x610, symBinAddr: 0x43B20, symSize: 0x120 }
  - { offset: 0xEE2BA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17paymentSuccessful7orderID16dictVerificationySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x380, symBinAddr: 0x43890, symSize: 0x290 }
  - { offset: 0xEE48B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGSgtFTf4dnnn_n', symObjAddr: 0x730, symBinAddr: 0x43C40, symSize: 0x370 }
  - { offset: 0xEE6BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tFyyScMYccfU_Tf2ni_nTA', symObjAddr: 0xBD0, symBinAddr: 0x43FE0, symSize: 0x20 }
  - { offset: 0xEE6D2, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xBF0, symBinAddr: 0x44000, symSize: 0x20 }
  - { offset: 0xEE6E6, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xC10, symBinAddr: 0x44020, symSize: 0x10 }
  - { offset: 0xEEA0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDict_WZ', symObjAddr: 0x0, symBinAddr: 0x44030, symSize: 0x150 }
  - { offset: 0xEEA31, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDictSDySSypGvpZ', symObjAddr: 0x3028, symBinAddr: 0xB6380, symSize: 0x0 }
  - { offset: 0xEEAB8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDict_WZ', symObjAddr: 0x0, symBinAddr: 0x44030, symSize: 0x150 }
  - { offset: 0xEEC04, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsCMa', symObjAddr: 0x170, symBinAddr: 0x44180, symSize: 0x20 }
  - { offset: 0xEEE57, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeWrapperCMa', symObjAddr: 0x30, symBinAddr: 0x441A0, symSize: 0x20 }
  - { offset: 0xEEFE3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x441C0, symSize: 0xF0 }
  - { offset: 0xEF007, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigC14sharedInstanceACvpZ', symObjAddr: 0xB1E8, symBinAddr: 0xB6388, symSize: 0x0 }
  - { offset: 0xEF18D, size: 0x8, addend: 0x0, symName: '_$sSD8RazorpayE5merge5otherySDyxq_G_tFSS_ypTg5', symObjAddr: 0xF0, symBinAddr: 0x442B0, symSize: 0x460 }
  - { offset: 0xEF365, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigC14sharedInstance_WZ', symObjAddr: 0x700, symBinAddr: 0x448C0, symSize: 0x40 }
  - { offset: 0xEF3AB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCfE', symObjAddr: 0x740, symBinAddr: 0x44900, symSize: 0x20 }
  - { offset: 0xEF411, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCMa', symObjAddr: 0x7B0, symBinAddr: 0x44970, symSize: 0x20 }
  - { offset: 0xEF596, size: 0x8, addend: 0x0, symName: '_$sSS3key_yp5valuetSgWOb', symObjAddr: 0x1F60, symBinAddr: 0x460B0, symSize: 0x40 }
  - { offset: 0xEF5AA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOwet', symObjAddr: 0x2030, symBinAddr: 0x460F0, symSize: 0x80 }
  - { offset: 0xEF5BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOwst', symObjAddr: 0x20B0, symBinAddr: 0x46170, symSize: 0xD0 }
  - { offset: 0xEF5D2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOMa', symObjAddr: 0x21B0, symBinAddr: 0x46240, symSize: 0x10 }
  - { offset: 0xEF5E6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASQWb', symObjAddr: 0x21C0, symBinAddr: 0x46250, symSize: 0x10 }
  - { offset: 0xEF5FA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOACSQAAWl', symObjAddr: 0x21D0, symBinAddr: 0x46260, symSize: 0x30 }
  - { offset: 0xEF68A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x550, symBinAddr: 0x44710, symSize: 0x80 }
  - { offset: 0xEF75D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH9hashValueSivgTW', symObjAddr: 0x5D0, symBinAddr: 0x44790, symSize: 0x50 }
  - { offset: 0xEF7DB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x620, symBinAddr: 0x447E0, symSize: 0x30 }
  - { offset: 0xEF82D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x650, symBinAddr: 0x44810, symSize: 0x50 }
  - { offset: 0xEF9D3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x441C0, symSize: 0xF0 }
  - { offset: 0xEFA27, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x6A0, symBinAddr: 0x44860, symSize: 0x30 }
  - { offset: 0xEFA50, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x6D0, symBinAddr: 0x44890, symSize: 0x30 }
  - { offset: 0xEFABB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCfD', symObjAddr: 0x760, symBinAddr: 0x44920, symSize: 0x50 }
  - { offset: 0xEFB60, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCACyc33_DF8BC766DBD29A885663B5F7C39C22B4LlfcTf4g_n', symObjAddr: 0x7D0, symBinAddr: 0x44990, symSize: 0x1720 }
  - { offset: 0xF0A5E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x2200, symBinAddr: 0x46290, symSize: 0x70 }
  - { offset: 0xF0C3D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11merchantKey33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLLSSvpZ', symObjAddr: 0x31B8, symBinAddr: 0xA3CF0, symSize: 0x0 }
  - { offset: 0xF0C57, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC14sharedInstance33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLLACvpZ', symObjAddr: 0x31C8, symBinAddr: 0xA3D00, symSize: 0x0 }
  - { offset: 0xF1112, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC14sharedInstance33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLL_WZ', symObjAddr: 0x200, symBinAddr: 0x46500, symSize: 0x30 }
  - { offset: 0xF1439, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCfETo', symObjAddr: 0x1270, symBinAddr: 0x47560, symSize: 0x40 }
  - { offset: 0xF1662, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCMa', symObjAddr: 0x2D20, symBinAddr: 0x49010, symSize: 0x20 }
  - { offset: 0xF1676, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFyyScMYccfU_TA', symObjAddr: 0x2DC0, symBinAddr: 0x49070, symSize: 0x20 }
  - { offset: 0xF168A, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2DE0, symBinAddr: 0x49090, symSize: 0x20 }
  - { offset: 0xF169E, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2E00, symBinAddr: 0x490B0, symSize: 0x10 }
  - { offset: 0xF16B2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocol_pSgWOd', symObjAddr: 0x2EF0, symBinAddr: 0x490C0, symSize: 0x40 }
  - { offset: 0xF16C6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwCP', symObjAddr: 0x2F60, symBinAddr: 0x49100, symSize: 0x30 }
  - { offset: 0xF16DA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwxx', symObjAddr: 0x2F90, symBinAddr: 0x49130, symSize: 0x10 }
  - { offset: 0xF16EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwca', symObjAddr: 0x2FA0, symBinAddr: 0x49140, symSize: 0x40 }
  - { offset: 0xF1702, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x2FE0, symBinAddr: 0x49180, symSize: 0x10 }
  - { offset: 0xF1716, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwta', symObjAddr: 0x2FF0, symBinAddr: 0x49190, symSize: 0x30 }
  - { offset: 0xF172A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwet', symObjAddr: 0x3020, symBinAddr: 0x491C0, symSize: 0x40 }
  - { offset: 0xF173E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwst', symObjAddr: 0x3060, symBinAddr: 0x49200, symSize: 0x40 }
  - { offset: 0xF1752, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVMa', symObjAddr: 0x30A0, symBinAddr: 0x49240, symSize: 0x10 }
  - { offset: 0xF1766, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFyyScMYccfU_TA', symObjAddr: 0x30E0, symBinAddr: 0x49280, symSize: 0x20 }
  - { offset: 0xF177A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFyyScMYccfU_yycfU_TA', symObjAddr: 0x3120, symBinAddr: 0x492C0, symSize: 0x10 }
  - { offset: 0xF1945, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCACyc33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLlfcTo', symObjAddr: 0x0, symBinAddr: 0x46300, symSize: 0x70 }
  - { offset: 0xF198E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvgTo', symObjAddr: 0x70, symBinAddr: 0x46370, symSize: 0x50 }
  - { offset: 0xF19C3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvg', symObjAddr: 0xC0, symBinAddr: 0x463C0, symSize: 0x40 }
  - { offset: 0xF1A0B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvsTo', symObjAddr: 0x100, symBinAddr: 0x46400, symSize: 0x60 }
  - { offset: 0xF1A46, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvs', symObjAddr: 0x160, symBinAddr: 0x46460, symSize: 0x50 }
  - { offset: 0xF1A6D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvM', symObjAddr: 0x1B0, symBinAddr: 0x464B0, symSize: 0x40 }
  - { offset: 0xF1A92, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvM.resume.0', symObjAddr: 0x1F0, symBinAddr: 0x464F0, symSize: 0x10 }
  - { offset: 0xF1B79, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_11andDelegate17forViewControllerySS_AA0A25PaymentCompletionProtocol_pSo06UIViewJ0CtFZTo', symObjAddr: 0x240, symBinAddr: 0x46530, symSize: 0x10 }
  - { offset: 0xF1B8D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_11andDelegateACSS_AA0A8Protocol_ptFZ', symObjAddr: 0x250, symBinAddr: 0x46540, symSize: 0x20 }
  - { offset: 0xF1BA1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZ', symObjAddr: 0x290, symBinAddr: 0x46580, symSize: 0x20 }
  - { offset: 0xF1BBC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTo', symObjAddr: 0x2B0, symBinAddr: 0x465A0, symSize: 0x90 }
  - { offset: 0xF1BE6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4DataACSS_AA0a25PaymentCompletionProtocoldH0_ptFZ', symObjAddr: 0x340, symBinAddr: 0x46630, symSize: 0x20 }
  - { offset: 0xF1BFA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZ', symObjAddr: 0x400, symBinAddr: 0x466F0, symSize: 0x20 }
  - { offset: 0xF1C15, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTo', symObjAddr: 0x420, symBinAddr: 0x46710, symSize: 0x80 }
  - { offset: 0xF1C4F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10publishUri4withySS_tFZ', symObjAddr: 0x4A0, symBinAddr: 0x46790, symSize: 0x50 }
  - { offset: 0xF1CC0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10publishUri4withySS_tFZTo', symObjAddr: 0x4F0, symBinAddr: 0x467E0, symSize: 0x60 }
  - { offset: 0xF1D40, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pF', symObjAddr: 0x550, symBinAddr: 0x46840, symSize: 0x40 }
  - { offset: 0xF1DC4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pFTo', symObjAddr: 0x590, symBinAddr: 0x46880, symSize: 0x40 }
  - { offset: 0xF1E8B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayControllerySDys11AnyHashableVypG_So06UIViewE0CtF', symObjAddr: 0x5D0, symBinAddr: 0x468C0, symSize: 0x60 }
  - { offset: 0xF1F2E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayControllerySDys11AnyHashableVypG_So06UIViewE0CtFTo', symObjAddr: 0x630, symBinAddr: 0x46920, symSize: 0xC0 }
  - { offset: 0xF202B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4openyySDys11AnyHashableVypGF', symObjAddr: 0x6F0, symBinAddr: 0x469E0, symSize: 0x170 }
  - { offset: 0xF2154, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4openyySDys11AnyHashableVypGFTo', symObjAddr: 0x860, symBinAddr: 0x46B50, symSize: 0x70 }
  - { offset: 0xF2168, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayController26arrExternalPaymentEntitiesySDys11AnyHashableVypG_So06UIViewE0CSayAA06PluginH8Delegate_pGtF', symObjAddr: 0x8D0, symBinAddr: 0x46BC0, symSize: 0x180 }
  - { offset: 0xF2290, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayController26arrExternalPaymentEntitiesySDys11AnyHashableVypG_So06UIViewE0CSayAA06PluginH8Delegate_pGtFTo', symObjAddr: 0xA50, symBinAddr: 0x46D40, symSize: 0xB0 }
  - { offset: 0xF22A4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZ', symObjAddr: 0xB00, symBinAddr: 0x46DF0, symSize: 0x10 }
  - { offset: 0xF22FA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZTo', symObjAddr: 0xB10, symBinAddr: 0x46E00, symSize: 0x30 }
  - { offset: 0xF2324, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_26arrExternalPaymentEntitiesySDys11AnyHashableVypG_SayAA06PluginF8Delegate_pGtF', symObjAddr: 0xB40, symBinAddr: 0x46E30, symSize: 0x70 }
  - { offset: 0xF23F4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_26arrExternalPaymentEntitiesySDys11AnyHashableVypG_SayAA06PluginF8Delegate_pGtFTo', symObjAddr: 0xBB0, symBinAddr: 0x46EA0, symSize: 0xE0 }
  - { offset: 0xF249D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptF', symObjAddr: 0xC90, symBinAddr: 0x46F80, symSize: 0x10 }
  - { offset: 0xF24B1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFyyScMYccfU_', symObjAddr: 0xCA0, symBinAddr: 0x46F90, symSize: 0x3B0 }
  - { offset: 0xF26C5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFTo', symObjAddr: 0x1050, symBinAddr: 0x47340, symSize: 0x90 }
  - { offset: 0xF26EF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC5closeyyF', symObjAddr: 0x10E0, symBinAddr: 0x473D0, symSize: 0x40 }
  - { offset: 0xF2737, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC5closeyyFTo', symObjAddr: 0x1120, symBinAddr: 0x47410, symSize: 0x60 }
  - { offset: 0xF278D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC13clearUserDatayyF', symObjAddr: 0x1180, symBinAddr: 0x47470, symSize: 0x50 }
  - { offset: 0xF280B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC13clearUserDatayyFTo', symObjAddr: 0x11D0, symBinAddr: 0x474C0, symSize: 0x70 }
  - { offset: 0xF2885, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCfD', symObjAddr: 0x1240, symBinAddr: 0x47530, symSize: 0x30 }
  - { offset: 0xF28FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtF', symObjAddr: 0x12B0, symBinAddr: 0x475A0, symSize: 0x2B0 }
  - { offset: 0xF2964, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFyyScMYccfU_', symObjAddr: 0x1560, symBinAddr: 0x47850, symSize: 0x2C0 }
  - { offset: 0xF2AB0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFyyScMYccfU_yycfU_', symObjAddr: 0x1820, symBinAddr: 0x47B10, symSize: 0xC0 }
  - { offset: 0xF2BCA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVAA17SDKChecksProtocolA2aDP17validationResults14sdkCheckPoints17continueToPaymentySayAA0jK0VG_SbtFTW', symObjAddr: 0x18E0, symBinAddr: 0x47BD0, symSize: 0x20 }
  - { offset: 0xF2C2A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC13sdkValidation17continueToPaymentAcA17SDKChecksProtocol_p_SbSgtcfcTf4enn_nAA08InternalA0C_Tg5', symObjAddr: 0x1900, symBinAddr: 0x47BF0, symSize: 0x5E0 }
  - { offset: 0xF2FFE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC13sdkValidation17continueToPaymentAcA17SDKChecksProtocol_p_SbSgtcfcTf4enn_nAA0bC11InitializerV_Tg5', symObjAddr: 0x1EE0, symBinAddr: 0x481D0, symSize: 0x5D0 }
  - { offset: 0xF33EB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTf4nnnd_g', symObjAddr: 0x2520, symBinAddr: 0x48810, symSize: 0xD0 }
  - { offset: 0xF3459, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTf4nnnd_g', symObjAddr: 0x26C0, symBinAddr: 0x489B0, symSize: 0x1B0 }
  - { offset: 0xF34D5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZTf4nd_n', symObjAddr: 0x2870, symBinAddr: 0x48B60, symSize: 0x280 }
  - { offset: 0xF351C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFTf4nnnd_n', symObjAddr: 0x2AF0, symBinAddr: 0x48DE0, symSize: 0x230 }
  - { offset: 0xF3AEE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperCMa', symObjAddr: 0x1B0, symBinAddr: 0x494B0, symSize: 0x20 }
  - { offset: 0xF3B9B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_TA', symObjAddr: 0xA60, symBinAddr: 0x49D60, symSize: 0x80 }
  - { offset: 0xF3BDB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_TA', symObjAddr: 0xB30, symBinAddr: 0x49DE0, symSize: 0x80 }
  - { offset: 0xF3C1B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_TA', symObjAddr: 0xBB0, symBinAddr: 0x49E60, symSize: 0x80 }
  - { offset: 0xF3C6F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_ApRXEfU_TA', symObjAddr: 0xC30, symBinAddr: 0x49EE0, symSize: 0x50 }
  - { offset: 0xF3C83, size: 0x8, addend: 0x0, symName: '_$ss5UInt8VABSzsWl', symObjAddr: 0xCD0, symBinAddr: 0x49F30, symSize: 0x30 }
  - { offset: 0xF3C97, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZySPyytGXEfU_TA', symObjAddr: 0xD30, symBinAddr: 0x49F60, symSize: 0x20 }
  - { offset: 0xF3CAB, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGMa', symObjAddr: 0xDC0, symBinAddr: 0x49F80, symSize: 0x30 }
  - { offset: 0xF3CDB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZySPyytGXEfU_', symObjAddr: 0x0, symBinAddr: 0x49320, symSize: 0xD0 }
  - { offset: 0xF3E52, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_ApRXEfU_', symObjAddr: 0xD0, symBinAddr: 0x493F0, symSize: 0xC0 }
  - { offset: 0xF3F8A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZTf4nd_n', symObjAddr: 0x1D0, symBinAddr: 0x494D0, symSize: 0x180 }
  - { offset: 0xF4079, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC22convertDataToHexString4dataSS10Foundation0E0V_tFZTf4nd_n', symObjAddr: 0x350, symBinAddr: 0x49650, symSize: 0x230 }
  - { offset: 0xF4118, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC15validateKeySize33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL3keySb10Foundation4DataV_tFZTf4nd_n', symObjAddr: 0x580, symBinAddr: 0x49880, symSize: 0x120 }
  - { offset: 0xF418B, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV5countACSi_tcfCTf4nd_n', symObjAddr: 0x6A0, symBinAddr: 0x499A0, symSize: 0xC0 }
  - { offset: 0xF41D5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZTf4nnnnnd_n', symObjAddr: 0x760, symBinAddr: 0x49A60, symSize: 0x300 }
  - { offset: 0xF4750, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageV05buildbcdE0012fromWKScriptE0ACSgSo0hE0C_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x49FB0, symSize: 0x2B0 }
  - { offset: 0xF4819, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwxx', symObjAddr: 0x380, symBinAddr: 0x4A260, symSize: 0x30 }
  - { offset: 0xF482D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwcp', symObjAddr: 0x3B0, symBinAddr: 0x4A290, symSize: 0x60 }
  - { offset: 0xF4841, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwca', symObjAddr: 0x410, symBinAddr: 0x4A2F0, symSize: 0x80 }
  - { offset: 0xF4855, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x490, symBinAddr: 0x4A370, symSize: 0x20 }
  - { offset: 0xF4869, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwta', symObjAddr: 0x4B0, symBinAddr: 0x4A390, symSize: 0x50 }
  - { offset: 0xF487D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwet', symObjAddr: 0x500, symBinAddr: 0x4A3E0, symSize: 0x50 }
  - { offset: 0xF4891, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwst', symObjAddr: 0x550, symBinAddr: 0x4A430, symSize: 0x50 }
  - { offset: 0xF48A5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVMa', symObjAddr: 0x5A0, symBinAddr: 0x4A480, symSize: 0x10 }
  - { offset: 0xF492B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageV05buildbcdE0012fromWKScriptE0ACSgSo0hE0C_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x49FB0, symSize: 0x2B0 }
  - { offset: 0xF4C25, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xCD0, symBinAddr: 0x4B120, symSize: 0x20 }
  - { offset: 0xF4C39, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xCF0, symBinAddr: 0x4B140, symSize: 0x10 }
  - { offset: 0xF4C4D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_TA', symObjAddr: 0xD00, symBinAddr: 0x4B150, symSize: 0x20 }
  - { offset: 0xF4C61, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_yycfU_TA', symObjAddr: 0xD50, symBinAddr: 0x4B1A0, symSize: 0x30 }
  - { offset: 0xF4C93, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_TA', symObjAddr: 0xDC0, symBinAddr: 0x4B210, symSize: 0x20 }
  - { offset: 0xF4CA7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_ySbcfU0_TA', symObjAddr: 0xE40, symBinAddr: 0x4B290, symSize: 0x40 }
  - { offset: 0xF4DB0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_', symObjAddr: 0x0, symBinAddr: 0x4A490, symSize: 0x190 }
  - { offset: 0xF4E01, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_', symObjAddr: 0x190, symBinAddr: 0x4A620, symSize: 0x190 }
  - { offset: 0xF4EF6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZTf4nnnd_n', symObjAddr: 0x360, symBinAddr: 0x4A7B0, symSize: 0x900 }
  - { offset: 0xF55D6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsCMa', symObjAddr: 0x640, symBinAddr: 0x4B9C0, symSize: 0x20 }
  - { offset: 0xF6060, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath03althI0011withDefaultF00k10LiveReloadF008fallBacklC0xSgAA0bC7OptionsO_ALSgAJS2btlFZSS_Tgm5', symObjAddr: 0x0, symBinAddr: 0x4B3A0, symSize: 0x310 }
  - { offset: 0xF6247, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson12ofDictionarySDySSypGSDys11AnyHashableVypG_tFZ', symObjAddr: 0x310, symBinAddr: 0x4B6B0, symSize: 0x10 }
  - { offset: 0xF625B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34appropriatePaymentEntityDictionary011arrExternalE8EntitiesSDys11AnyHashableVypGSayAA06PluginE8Delegate_pGSg_tFZyAaI_pXEfU_', symObjAddr: 0x320, symBinAddr: 0x4B6C0, symSize: 0x300 }
  - { offset: 0xF63C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson7ofArraySayypGSayypSgG_tFZTf4nd_n', symObjAddr: 0x660, symBinAddr: 0x4B9E0, symSize: 0x680 }
  - { offset: 0xF67F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson12ofDictionarySDySSypGSDys11AnyHashableVypG_tFZTf4nd_n', symObjAddr: 0xCE0, symBinAddr: 0x4C060, symSize: 0x990 }
  - { offset: 0xF6B53, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC10filterInfoySDys11AnyHashableVypGAGFZTf4nd_n', symObjAddr: 0x1670, symBinAddr: 0x4C9F0, symSize: 0x830 }
  - { offset: 0xF6F7A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC22isCurrentVersionLatest07currentF0SbSS_tKFZTf4nd_n', symObjAddr: 0x1FC0, symBinAddr: 0x4D220, symSize: 0x670 }
  - { offset: 0xF7487, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34checkAndShowUpdateSDKToastForDebug8onWindow15withMerchantKeyySo8UIWindowCSg_SStFZTf4nnd_n', symObjAddr: 0x2630, symBinAddr: 0x4D890, symSize: 0x3F0 }
  - { offset: 0xF76F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC18isMessageAuthentic7messageSbSo08WKScriptE0C_tFZTf4nd_n', symObjAddr: 0x2A20, symBinAddr: 0x4DC80, symSize: 0x410 }
  - { offset: 0xF77D3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC21convertColorToUIColor9hexStringSo0G0CSgSS_tFZTf4nd_n', symObjAddr: 0x2E30, symBinAddr: 0x4E090, symSize: 0x290 }
  - { offset: 0xF7974, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34appropriatePaymentEntityDictionary011arrExternalE8EntitiesSDys11AnyHashableVypGSayAA06PluginE8Delegate_pGSg_tFZTf4nd_n', symObjAddr: 0x30C0, symBinAddr: 0x4E320, symSize: 0x280 }
  - { offset: 0xF7DA2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24SwiftCompatibilityHelperCMa', symObjAddr: 0x20, symBinAddr: 0x4E5A0, symSize: 0x20 }
  - { offset: 0xF7F42, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOwet', symObjAddr: 0x20, symBinAddr: 0x4E5C0, symSize: 0x80 }
  - { offset: 0xF7F56, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOwst', symObjAddr: 0xA0, symBinAddr: 0x4E640, symSize: 0xD0 }
  - { offset: 0xF7F6A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOMa', symObjAddr: 0x180, symBinAddr: 0x4E710, symSize: 0x10 }
  - { offset: 0xF7F7E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOMa', symObjAddr: 0x310, symBinAddr: 0x4E720, symSize: 0x10 }
  - { offset: 0xF8093, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASQWb', symObjAddr: 0x1070, symBinAddr: 0x4F480, symSize: 0x10 }
  - { offset: 0xF80A7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOACSQAAWl', symObjAddr: 0x1080, symBinAddr: 0x4F490, symSize: 0x30 }
  - { offset: 0xF80D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASQWb', symObjAddr: 0x12C0, symBinAddr: 0x4F670, symSize: 0x10 }
  - { offset: 0xF80E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOACSQAAWl', symObjAddr: 0x12D0, symBinAddr: 0x4F680, symSize: 0x30 }
  - { offset: 0xF8107, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xE40, symBinAddr: 0x4F250, symSize: 0x10 }
  - { offset: 0xF816E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH9hashValueSivgTW', symObjAddr: 0xE50, symBinAddr: 0x4F260, symSize: 0x90 }
  - { offset: 0xF8219, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xEE0, symBinAddr: 0x4F2F0, symSize: 0x70 }
  - { offset: 0xF8280, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xF50, symBinAddr: 0x4F360, symSize: 0x90 }
  - { offset: 0xF832D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1110, symBinAddr: 0x4F4C0, symSize: 0x80 }
  - { offset: 0xF8400, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH9hashValueSivgTW', symObjAddr: 0x1190, symBinAddr: 0x4F540, symSize: 0x50 }
  - { offset: 0xF847E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x11E0, symBinAddr: 0x4F590, symSize: 0x30 }
  - { offset: 0xF84D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1210, symBinAddr: 0x4F5C0, symSize: 0x50 }
  - { offset: 0xF8542, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventO8rawValueSSvg', symObjAddr: 0x320, symBinAddr: 0x4E730, symSize: 0xB20 }
  - { offset: 0xF859A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0xFE0, symBinAddr: 0x4F3F0, symSize: 0x30 }
  - { offset: 0xF85C3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x1010, symBinAddr: 0x4F420, symSize: 0x60 }
  - { offset: 0xF8606, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x1260, symBinAddr: 0x4F610, symSize: 0x30 }
  - { offset: 0xF862F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x1290, symBinAddr: 0x4F640, symSize: 0x30 }
  - { offset: 0xF8649, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x1300, symBinAddr: 0x4F6B0, symSize: 0x70 }
  - { offset: 0xF8680, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x1370, symBinAddr: 0x4F720, symSize: 0x580 }
  - { offset: 0xF888C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12OtpelfBridgeCMa', symObjAddr: 0x20, symBinAddr: 0x4FCC0, symSize: 0x20 }
  - { offset: 0xF88A0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeCMa', symObjAddr: 0x40, symBinAddr: 0x4FCE0, symSize: 0x20 }
  - { offset: 0xF8E87, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC14setValueForKey33_8B3A70AEAD4AD830A8BC74BF45B8FC5FLL10fromParams9classNameySayypG_SStFTf4nnd_n', symObjAddr: 0x60, symBinAddr: 0x4FD00, symSize: 0x6A0 }
  - { offset: 0xF9208, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC8getValue33_8B3A70AEAD4AD830A8BC74BF45B8FC5FLL10fromParams9className10andWebView0S10CallBackIdySayypG_SSSo05WKWebU0CSSSgtFTf4nnnnd_n', symObjAddr: 0x700, symBinAddr: 0x503A0, symSize: 0x9E0 }
  - { offset: 0xF98DB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC15executeFunction_10withParams16havingCallBackId8sentFromySS_SayypGSgSSSgSo9WKWebViewCtFTf4nnnnd_n', symObjAddr: 0x10E0, symBinAddr: 0x50D80, symSize: 0x4D0 }
  - { offset: 0xF9B1A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12OtpelfBridgeC15executeFunction_10withParams16havingCallBackId8sentFromySS_SayypGSgSSSgSo9WKWebViewCtFTf4nnddd_n', symObjAddr: 0x15B0, symBinAddr: 0x51250, symSize: 0xEC0 }
  - { offset: 0xFA702, size: 0x8, addend: 0x0, symName: '_$sypSgs5Error_pSgIeghng_yXlSgSo7NSErrorCSgIeyBhyy_TR', symObjAddr: 0x0, symBinAddr: 0x52120, symSize: 0xA0 }
  - { offset: 0xFA729, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_7didFail9withErrorySo05WKWebE0C_So12WKNavigationCSgs0I0_ptFTo', symObjAddr: 0xB0, symBinAddr: 0x521D0, symSize: 0x80 }
  - { offset: 0xFA76A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_28didFailProvisionalNavigation9withErrorySo05WKWebE0C_So12WKNavigationCSgs0K0_ptFTo', symObjAddr: 0x130, symBinAddr: 0x52250, symSize: 0x80 }
  - { offset: 0xFA79C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0x1B0, symBinAddr: 0x522D0, symSize: 0x290 }
  - { offset: 0xFA83A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0x440, symBinAddr: 0x52560, symSize: 0x70 }
  - { offset: 0xFA87B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_15decidePolicyFor15decisionHandlerySo05WKWebE0C_So18WKNavigationActionCySo0lmG0VctFTo', symObjAddr: 0x4B0, symBinAddr: 0x525D0, symSize: 0xA0 }
  - { offset: 0xFA8AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo11UITextFieldCcfU_', symObjAddr: 0x680, symBinAddr: 0x527A0, symSize: 0x50 }
  - { offset: 0xFA982, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU0_', symObjAddr: 0x730, symBinAddr: 0x52840, symSize: 0x1A0 }
  - { offset: 0xFAB2F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFTo', symObjAddr: 0x8D0, symBinAddr: 0x529E0, symSize: 0x110 }
  - { offset: 0xFAB61, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgIeyBy_SSSgIegg_TR', symObjAddr: 0x9E0, symBinAddr: 0x52AF0, symSize: 0x40 }
  - { offset: 0xFAB79, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgIeyBy_SSSgIegg_TRTA', symObjAddr: 0xA40, symBinAddr: 0x52B50, symSize: 0x10 }
  - { offset: 0xFAD0B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_7didFail9withErrorySo05WKWebE0C_So12WKNavigationCSgs0I0_ptFTf4ddnn_n', symObjAddr: 0xA50, symBinAddr: 0x52B60, symSize: 0x1B30 }
  - { offset: 0xFB4B2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_28didFailProvisionalNavigation9withErrorySo05WKWebE0C_So12WKNavigationCSgs0K0_ptFTf4ndnn_n', symObjAddr: 0x2580, symBinAddr: 0x54690, symSize: 0x5D0 }
  - { offset: 0xFB785, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x2B50, symBinAddr: 0x54C60, symSize: 0xA10 }
  - { offset: 0xFBB83, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_15decidePolicyFor15decisionHandlerySo05WKWebE0C_So18WKNavigationActionCySo0lmG0VctF06$sSo24lmG16VIeyBy_ABIegy_TRALIeyBy_Tf1nncn_nTf4dnng_n', symObjAddr: 0x3560, symBinAddr: 0x55670, symSize: 0x4E0 }
  - { offset: 0xFBDE5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_34runJavaScriptAlertPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCyyctFTf4dndnn_n', symObjAddr: 0x3A40, symBinAddr: 0x55B50, symSize: 0x1A0 }
  - { offset: 0xFBE65, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_36runJavaScriptConfirmPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCySbctFTf4dndnn_n', symObjAddr: 0x3BE0, symBinAddr: 0x55CF0, symSize: 0x280 }
  - { offset: 0xFBEF2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFTf4dnndnn_n', symObjAddr: 0x3E60, symBinAddr: 0x55F70, symSize: 0x340 }
  - { offset: 0xFBF8F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo11UITextFieldCcfU_TA', symObjAddr: 0x41C0, symBinAddr: 0x562D0, symSize: 0x20 }
  - { offset: 0xFBFA3, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x41E0, symBinAddr: 0x562F0, symSize: 0x20 }
  - { offset: 0xFBFB7, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4200, symBinAddr: 0x56310, symSize: 0x10 }
  - { offset: 0xFBFCB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU0_TA', symObjAddr: 0x4250, symBinAddr: 0x56360, symSize: 0x20 }
  - { offset: 0xFC005, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU1_TA', symObjAddr: 0x4290, symBinAddr: 0x563A0, symSize: 0x30 }
  - { offset: 0xFC039, size: 0x8, addend: 0x0, symName: '_$sSo11UITextFieldCMa', symObjAddr: 0x42C0, symBinAddr: 0x563D0, symSize: 0x30 }
  - { offset: 0xFC058, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x42F0, symBinAddr: 0x56400, symSize: 0x20 }
  - { offset: 0xFC08C, size: 0x8, addend: 0x0, symName: '_$sIeyB_Ieg_TRTA', symObjAddr: 0x4370, symBinAddr: 0x56480, symSize: 0x10 }
  - { offset: 0xFC0DB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_34runJavaScriptAlertPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCyyctFySo13UIAlertActionCcfU_TA', symObjAddr: 0x4380, symBinAddr: 0x56490, symSize: 0x20 }
  - { offset: 0xFC10F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x4480, symBinAddr: 0x564E0, symSize: 0x10 }
  - { offset: 0xFC73C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC14sharedInstanceACvpZ', symObjAddr: 0x2F1A0, symBinAddr: 0xB6390, symSize: 0x0 }
  - { offset: 0xFCB42, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC14sharedInstance_WZ', symObjAddr: 0x2390, symBinAddr: 0x58990, symSize: 0x40 }
  - { offset: 0xFCC07, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataCMa', symObjAddr: 0x3A20, symBinAddr: 0x5A020, symSize: 0x20 }
  - { offset: 0xFCC1B, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x3A80, symBinAddr: 0x5A040, symSize: 0x50 }
  - { offset: 0xFCC50, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x3B40, symBinAddr: 0x5A090, symSize: 0xF0 }
  - { offset: 0xFCCA8, size: 0x8, addend: 0x0, symName: '_$ss32_copyCollectionToContiguousArrayys0dE0Vy7ElementQzGxSlRzlFSs8UTF8ViewV_Tgq5', symObjAddr: 0x3C30, symBinAddr: 0x5A180, symSize: 0xA0 }
  - { offset: 0xFCCE9, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x3CD0, symBinAddr: 0x5A220, symSize: 0x100 }
  - { offset: 0xFCD0E, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgmq5', symObjAddr: 0x3DD0, symBinAddr: 0x5A320, symSize: 0x70 }
  - { offset: 0xFCD44, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x3E40, symBinAddr: 0x5A390, symSize: 0x2D0 }
  - { offset: 0xFCD7D, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x4110, symBinAddr: 0x5A660, symSize: 0xA0 }
  - { offset: 0xFCD95, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x41E0, symBinAddr: 0x5A730, symSize: 0x30 }
  - { offset: 0xFCDA9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_TA', symObjAddr: 0x4280, symBinAddr: 0x5A7A0, symSize: 0x20 }
  - { offset: 0xFCDBD, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x42A0, symBinAddr: 0x5A7C0, symSize: 0x20 }
  - { offset: 0xFCDD1, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x42C0, symBinAddr: 0x5A7E0, symSize: 0x10 }
  - { offset: 0xFCE8F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_yyScMYcXEfU_TA', symObjAddr: 0x4CD0, symBinAddr: 0x5B170, symSize: 0x20 }
  - { offset: 0xFCEA3, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x4D10, symBinAddr: 0x5B1B0, symSize: 0x20 }
  - { offset: 0xFCEB7, size: 0x8, addend: 0x0, symName: '_$sS2sSysWl', symObjAddr: 0x4D80, symBinAddr: 0x5B1D0, symSize: 0x30 }
  - { offset: 0xFCECB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyFy10Foundation10URLRequestV_AF0C0VSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x4F00, symBinAddr: 0x5B250, symSize: 0x20 }
  - { offset: 0xFD0D2, size: 0x8, addend: 0x0, symName: '_$sSlsSQ7ElementRpzrlE10firstIndex2of0C0QzSgAB_tFSS_Tg5', symObjAddr: 0x2290, symBinAddr: 0x58890, symSize: 0x100 }
  - { offset: 0xFD28F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSS_Tg5', symObjAddr: 0x0, symBinAddr: 0x56600, symSize: 0x850 }
  - { offset: 0xFD64D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSb_Tg5', symObjAddr: 0x850, symBinAddr: 0x56E50, symSize: 0x8C0 }
  - { offset: 0xFDAAC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSi_Tg5', symObjAddr: 0x1110, symBinAddr: 0x57710, symSize: 0x850 }
  - { offset: 0xFDF1F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSS_Tgm5', symObjAddr: 0x1960, symBinAddr: 0x57F60, symSize: 0x310 }
  - { offset: 0xFE14A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSb_Tgm5', symObjAddr: 0x1C70, symBinAddr: 0x58270, symSize: 0x310 }
  - { offset: 0xFE375, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSi_Tgm5', symObjAddr: 0x1F80, symBinAddr: 0x58580, symSize: 0x310 }
  - { offset: 0xFE5AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC5startyyF', symObjAddr: 0x23D0, symBinAddr: 0x589D0, symSize: 0x140 }
  - { offset: 0xFE675, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC5startyyF6$deferL_yyF', symObjAddr: 0x2510, symBinAddr: 0x58B10, symSize: 0x120 }
  - { offset: 0xFE7CB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyF', symObjAddr: 0x2630, symBinAddr: 0x58C30, symSize: 0x410 }
  - { offset: 0xFE9F9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyFy10Foundation10URLRequestV_AF0C0VSo13NSURLResponseCSgtcfU_', symObjAddr: 0x2A40, symBinAddr: 0x59040, symSize: 0x4A0 }
  - { offset: 0xFEB26, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tF', symObjAddr: 0x2F20, symBinAddr: 0x59520, symSize: 0x3E0 }
  - { offset: 0xFED4B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_', symObjAddr: 0x3300, symBinAddr: 0x59900, symSize: 0x330 }
  - { offset: 0xFEE09, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_', symObjAddr: 0x3630, symBinAddr: 0x59C30, symSize: 0x240 }
  - { offset: 0xFEEC4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_yyScMYcXEfU_', symObjAddr: 0x3870, symBinAddr: 0x59E70, symSize: 0xC0 }
  - { offset: 0xFEFA2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataCfD', symObjAddr: 0x3A00, symBinAddr: 0x5A000, symSize: 0x20 }
  - { offset: 0xFEFF8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC014fetchEncryptedB7AndHash33_C969CD0145D9EE45921CEF1FF36E8712LLSS_SStyFTf4d_n', symObjAddr: 0x4350, symBinAddr: 0x5A7F0, symSize: 0x290 }
  - { offset: 0xFF10B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC07decryptB6String8inBase64S2S_tFZTf4nd_n', symObjAddr: 0x45E0, symBinAddr: 0x5AA80, symSize: 0x5F0 }
  - { offset: 0xFF36C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC03getB2JsSSyFTf4n_g', symObjAddr: 0x4BD0, symBinAddr: 0x5B070, symSize: 0xD0 }
  - { offset: 0xFF89A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCMa', symObjAddr: 0x4F0, symBinAddr: 0x5B780, symSize: 0x20 }
  - { offset: 0xFF8C8, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo19WKWebsiteDataRecordC_Tgm5', symObjAddr: 0x5B0, symBinAddr: 0x5B840, symSize: 0x60 }
  - { offset: 0xFF8F5, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlF8Razorpay21PluginPaymentDelegate_p_Tgm5', symObjAddr: 0x610, symBinAddr: 0x5B8A0, symSize: 0x20 }
  - { offset: 0xFF90D, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo18NSLayoutConstraintC_Tgm5', symObjAddr: 0x630, symBinAddr: 0x5B8C0, symSize: 0x60 }
  - { offset: 0xFF945, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0x900, symBinAddr: 0x5BB90, symSize: 0x1F0 }
  - { offset: 0xFF9BA, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFypSg_Tg5', symObjAddr: 0xB10, symBinAddr: 0x5BDA0, symSize: 0x20 }
  - { offset: 0xFF9D2, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0xB30, symBinAddr: 0x5BDC0, symSize: 0x20 }
  - { offset: 0xFFA4D, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFypSg_Tg5', symObjAddr: 0xB50, symBinAddr: 0x5BDE0, symSize: 0x130 }
  - { offset: 0xFFBB0, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0xC80, symBinAddr: 0x5BF10, symSize: 0x140 }
  - { offset: 0xFFE15, size: 0x8, addend: 0x0, symName: '_$ss14_ArrayProtocolPsE6filterySay7ElementQzGSbAEKXEKFSay8Razorpay21PluginPaymentDelegate_pG_Tg5013$s8Razorpay20fg30ManagerC07processC0yyFSbAA0bC8H7_pXEfU_AG0fG5ModelCTf1cn_nTf4gg_n', symObjAddr: 0xDC0, symBinAddr: 0x5C050, symSize: 0x1D0 }
  - { offset: 0x100020, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC17analyticsInstanceAA13AnalyticsUtilCSgvg', symObjAddr: 0x0, symBinAddr: 0x5B290, symSize: 0xC0 }
  - { offset: 0x1000CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC07processC0yyF', symObjAddr: 0xC0, symBinAddr: 0x5B350, symSize: 0x2D0 }
  - { offset: 0x100401, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC10trackEvent5event14withPropertiesySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x390, symBinAddr: 0x5B620, symSize: 0x110 }
  - { offset: 0x100507, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCfD', symObjAddr: 0x4A0, symBinAddr: 0x5B730, symSize: 0x50 }
  - { offset: 0x100571, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP17paymentSuccessful7orderID16dictVerificationySS_SDys11AnyHashableVypGSgtFTW', symObjAddr: 0x510, symBinAddr: 0x5B7A0, symSize: 0x70 }
  - { offset: 0x1005CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGtFTW', symObjAddr: 0x580, symBinAddr: 0x5B810, symSize: 0x20 }
  - { offset: 0x1005F6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP10trackEvent5event14withPropertiesySS_SDys11AnyHashableVypGSgtFTW', symObjAddr: 0x5A0, symBinAddr: 0x5B830, symSize: 0x10 }
  - { offset: 0x100616, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGtFTf4dnnn_n', symObjAddr: 0xF90, symBinAddr: 0x5C220, symSize: 0x70 }
  - { offset: 0x100868, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC5track_16havingProperties6sentByySS_SDySSypGSgAA16RzpWebViewBridgeOtF', symObjAddr: 0x0, symBinAddr: 0x5C290, symSize: 0x260 }
  - { offset: 0x100954, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC5track_16havingProperties6sentByySS_SDySSypGSgAA16RzpWebViewBridgeOtF', symObjAddr: 0x0, symBinAddr: 0x5C290, symSize: 0x260 }
  - { offset: 0x100ED5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCfETo', symObjAddr: 0x950, symBinAddr: 0x5CE40, symSize: 0x80 }
  - { offset: 0x100F04, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCMa', symObjAddr: 0x9D0, symBinAddr: 0x5CEC0, symSize: 0x20 }
  - { offset: 0x100F27, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC17gestureRecognizer_13shouldReceiveSbSo09UIGestureF0C_So7UITouchCtFTo', symObjAddr: 0x9F0, symBinAddr: 0x5CEE0, symSize: 0x70 }
  - { offset: 0x100FA5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_21numberOfRowsInSectionSiSo07UITableF0C_SitFTo', symObjAddr: 0xA60, symBinAddr: 0x5CF50, symSize: 0x20 }
  - { offset: 0x101063, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_12cellForRowAtSo07UITableF4CellCSo0kF0C_10Foundation9IndexPathVtF', symObjAddr: 0xA80, symBinAddr: 0x5CF70, symSize: 0x540 }
  - { offset: 0x101311, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_12cellForRowAtSo07UITableF4CellCSo0kF0C_10Foundation9IndexPathVtFTo', symObjAddr: 0xFC0, symBinAddr: 0x5D4B0, symSize: 0xB0 }
  - { offset: 0x10133C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_14didSelectRowAtySo07UITableF0C_10Foundation9IndexPathVtFTo', symObjAddr: 0x1070, symBinAddr: 0x5D560, symSize: 0xA0 }
  - { offset: 0x10136E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC17gestureRecognizer_13shouldReceiveSbSo09UIGestureF0C_So7UITouchCtFTf4dnn_n', symObjAddr: 0x1110, symBinAddr: 0x5D600, symSize: 0xD0 }
  - { offset: 0x1013AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_14didSelectRowAtySo07UITableF0C_10Foundation9IndexPathVtFTf4dnn_n', symObjAddr: 0x11E0, symBinAddr: 0x5D6D0, symSize: 0x280 }
  - { offset: 0x101453, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaABSHSCWl', symObjAddr: 0x14D0, symBinAddr: 0x5D950, symSize: 0x40 }
  - { offset: 0x101467, size: 0x8, addend: 0x0, symName: '_$sSo6UIViewCMa', symObjAddr: 0x1510, symBinAddr: 0x5D990, symSize: 0x30 }
  - { offset: 0x10147B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypFyycfU_TA', symObjAddr: 0x1570, symBinAddr: 0x5D9F0, symSize: 0x20 }
  - { offset: 0x10148F, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1590, symBinAddr: 0x5DA10, symSize: 0x20 }
  - { offset: 0x1014A3, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x15B0, symBinAddr: 0x5DA30, symSize: 0x10 }
  - { offset: 0x1014B7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC13dismissDailogyyFyycfU_TA', symObjAddr: 0x15E0, symBinAddr: 0x5DA40, symSize: 0x20 }
  - { offset: 0x1014CB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC011autoDismissC0yyFyycfU_TA', symObjAddr: 0x1600, symBinAddr: 0x5DA60, symSize: 0x20 }
  - { offset: 0x1016C1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableViewSo07UITableF0CSgvgTo', symObjAddr: 0x0, symBinAddr: 0x5C4F0, symSize: 0x20 }
  - { offset: 0x101721, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableViewSo07UITableF0CSgvsTo', symObjAddr: 0x20, symBinAddr: 0x5C510, symSize: 0x20 }
  - { offset: 0x101873, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC11viewDidLoadyyF', symObjAddr: 0x40, symBinAddr: 0x5C530, symSize: 0x260 }
  - { offset: 0x101A0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC11viewDidLoadyyFTo', symObjAddr: 0x2A0, symBinAddr: 0x5C790, symSize: 0x30 }
  - { offset: 0x101A21, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC011autoDismissC0yyFTo', symObjAddr: 0x300, symBinAddr: 0x5C7F0, symSize: 0x30 }
  - { offset: 0x101A35, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC13dismissDailogyyFTo', symObjAddr: 0x420, symBinAddr: 0x5C910, symSize: 0x30 }
  - { offset: 0x101A93, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypF', symObjAddr: 0x450, symBinAddr: 0x5C940, symSize: 0x1B0 }
  - { offset: 0x101B31, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypFTo', symObjAddr: 0x640, symBinAddr: 0x5CB30, symSize: 0x60 }
  - { offset: 0x101B45, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x6A0, symBinAddr: 0x5CB90, symSize: 0x120 }
  - { offset: 0x101B86, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x7C0, symBinAddr: 0x5CCB0, symSize: 0x50 }
  - { offset: 0x101B9A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x810, symBinAddr: 0x5CD00, symSize: 0xE0 }
  - { offset: 0x101BCB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x8F0, symBinAddr: 0x5CDE0, symSize: 0x30 }
  - { offset: 0x101BDF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCfD', symObjAddr: 0x920, symBinAddr: 0x5CE10, symSize: 0x30 }
  - { offset: 0x101E36, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LL_WZ', symObjAddr: 0x0, symBinAddr: 0x5DAE0, symSize: 0x40 }
  - { offset: 0x101E5A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC05_baseC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0x990, symBinAddr: 0xA44B8, symSize: 0x0 }
  - { offset: 0x101E74, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC8_baseCdn33_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0x9A0, symBinAddr: 0xA44C8, symSize: 0x0 }
  - { offset: 0x101E8E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC10_staticCdn33_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0x9B0, symBinAddr: 0xA44D8, symSize: 0x0 }
  - { offset: 0x101EA8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC06_trackC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0x9C0, symBinAddr: 0xA44E8, symSize: 0x0 }
  - { offset: 0x101EC2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0x9D0, symBinAddr: 0xA44F8, symSize: 0x0 }
  - { offset: 0x101EDC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC07_butlerdC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0x9E0, symBinAddr: 0xA4508, symSize: 0x0 }
  - { offset: 0x101EEA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LL_WZ', symObjAddr: 0x0, symBinAddr: 0x5DAE0, symSize: 0x40 }
  - { offset: 0x101F8F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigCMa', symObjAddr: 0x60, symBinAddr: 0x5DB20, symSize: 0x20 }
  - { offset: 0x10206D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC10initialize6configySDyS2SG_tFZTf4nd_n', symObjAddr: 0x80, symBinAddr: 0x5DB40, symSize: 0x450 }
  - { offset: 0x1022EB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04baseC8CheckoutSSvgZTf4d_n', symObjAddr: 0x4D0, symBinAddr: 0x5DF90, symSize: 0x270 }
  - { offset: 0x102781, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilCMa', symObjAddr: 0x20, symBinAddr: 0x5E200, symSize: 0x20 }
  - { offset: 0x1027CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilC14getStringValue6forKey011withDefaultG0S2S_SSSgtFZTf4nnd_n', symObjAddr: 0x40, symBinAddr: 0x5E220, symSize: 0x140 }
  - { offset: 0x10281E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilC11resetValuesyyFZTf4d_n', symObjAddr: 0x230, symBinAddr: 0x5E360, symSize: 0x140 }
  - { offset: 0x1029C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19ResourceBundleClass06_E20D2F25E2878EA12AB6F7C669BA135C5LLCMa', symObjAddr: 0x20, symBinAddr: 0x5E4A0, symSize: 0x20 }
  - { offset: 0x102B66, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_magic, symObjAddr: 0x30, symBinAddr: 0x5E4C0, symSize: 0x10 }
  - { offset: 0x102B78, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_padding_bytes, symObjAddr: 0x50, symBinAddr: 0x5E4D0, symSize: 0x20 }
  - { offset: 0x102B8A, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_version, symObjAddr: 0x70, symBinAddr: 0x5E4F0, symSize: 0x10 }
  - { offset: 0x102BCD, size: 0x8, addend: 0x0, symName: _lprofProfileDumped, symObjAddr: 0x0, symBinAddr: 0x5E500, symSize: 0x10 }
  - { offset: 0x102BDB, size: 0x8, addend: 0x0, symName: _lprofProfileDumped, symObjAddr: 0x0, symBinAddr: 0x5E500, symSize: 0x10 }
  - { offset: 0x102C17, size: 0x8, addend: 0x0, symName: _getValueProfRecordHeaderSize, symObjAddr: 0x0, symBinAddr: 0x5E510, symSize: 0x10 }
  - { offset: 0x102C25, size: 0x8, addend: 0x0, symName: _getValueProfRecordHeaderSize, symObjAddr: 0x0, symBinAddr: 0x5E510, symSize: 0x10 }
  - { offset: 0x102C3E, size: 0x8, addend: 0x0, symName: _getValueProfRecordValueData, symObjAddr: 0x30, symBinAddr: 0x5E520, symSize: 0x20 }
  - { offset: 0x102C75, size: 0x8, addend: 0x0, symName: _getValueProfRecordNext, symObjAddr: 0xF0, symBinAddr: 0x5E540, symSize: 0xB0 }
  - { offset: 0x102CCB, size: 0x8, addend: 0x0, symName: _getFirstValueProfRecord, symObjAddr: 0x1A0, symBinAddr: 0x5E5F0, symSize: 0x10 }
  - { offset: 0x102CF9, size: 0x8, addend: 0x0, symName: _lprofSetupValueProfiler, symObjAddr: 0x620, symBinAddr: 0x5E600, symSize: 0x60 }
  - { offset: 0x102D65, size: 0x8, addend: 0x0, symName: ___llvm_profile_instrument_target_value, symObjAddr: 0x8E0, symBinAddr: 0x5E660, symSize: 0x240 }
  - { offset: 0x102DE9, size: 0x8, addend: 0x0, symName: _lprofGetVPDataReader, symObjAddr: 0xB80, symBinAddr: 0x5E8A0, symSize: 0x10 }
  - { offset: 0x102DFB, size: 0x8, addend: 0x0, symName: _initializeValueProfRuntimeRecord, symObjAddr: 0xB90, symBinAddr: 0x5E8B0, symSize: 0x260 }
  - { offset: 0x102E14, size: 0x8, addend: 0x0, symName: _getNumValueDataForSiteWrapper, symObjAddr: 0xDF0, symBinAddr: 0x5EB10, symSize: 0x20 }
  - { offset: 0x102E3D, size: 0x8, addend: 0x0, symName: _getValueProfDataSizeWrapper, symObjAddr: 0xE10, symBinAddr: 0x5EB30, symSize: 0x80 }
  - { offset: 0x102EBC, size: 0x8, addend: 0x0, symName: _getNumValueSitesRT, symObjAddr: 0xF40, symBinAddr: 0x5EC60, symSize: 0x10 }
  - { offset: 0x102ECE, size: 0x8, addend: 0x0, symName: _getNumValueDataRT, symObjAddr: 0xF50, symBinAddr: 0x5EC70, symSize: 0xB0 }
  - { offset: 0x102EE0, size: 0x8, addend: 0x0, symName: _getNextNValueData, symObjAddr: 0xE90, symBinAddr: 0x5EBB0, symSize: 0xB0 }
  - { offset: 0x102F1C, size: 0x8, addend: 0x0, symName: ___llvm_profile_is_continuous_mode_enabled, symObjAddr: 0x0, symBinAddr: 0x5ED20, symSize: 0x20 }
  - { offset: 0x102F2A, size: 0x8, addend: 0x0, symName: ___llvm_profile_is_continuous_mode_enabled, symObjAddr: 0x0, symBinAddr: 0x5ED20, symSize: 0x20 }
  - { offset: 0x102F3C, size: 0x8, addend: 0x0, symName: ___llvm_profile_enable_continuous_mode, symObjAddr: 0x20, symBinAddr: 0x5ED40, symSize: 0x10 }
  - { offset: 0x102F4E, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_page_size, symObjAddr: 0x30, symBinAddr: 0x5ED50, symSize: 0x10 }
  - { offset: 0x102F60, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_size_for_buffer, symObjAddr: 0x40, symBinAddr: 0x5ED60, symSize: 0x60 }
  - { offset: 0x103005, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_size_for_buffer_internal, symObjAddr: 0xA0, symBinAddr: 0x5EDC0, symSize: 0x160 }
  - { offset: 0x103179, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_data, symObjAddr: 0x200, symBinAddr: 0x5EF20, symSize: 0x30 }
  - { offset: 0x10318B, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_data_size, symObjAddr: 0x230, symBinAddr: 0x5EF50, symSize: 0x30 }
  - { offset: 0x1031B3, size: 0x8, addend: 0x0, symName: ___llvm_profile_counter_entry_size, symObjAddr: 0x260, symBinAddr: 0x5EF80, symSize: 0x20 }
  - { offset: 0x1031D4, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_counters, symObjAddr: 0x280, symBinAddr: 0x5EFA0, symSize: 0x50 }
  - { offset: 0x10321D, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_counters_size, symObjAddr: 0x2D0, symBinAddr: 0x5EFF0, symSize: 0x60 }
  - { offset: 0x103297, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_padding_sizes_for_counters, symObjAddr: 0x330, symBinAddr: 0x5F050, symSize: 0xE0 }
  - { offset: 0x1032FD, size: 0x8, addend: 0x0, symName: _initBufferWriter, symObjAddr: 0x410, symBinAddr: 0x5F130, symSize: 0x20 }
  - { offset: 0x10335D, size: 0x8, addend: 0x0, symName: _getCurFilenameLength, symObjAddr: 0x180, symBinAddr: 0x5F150, symSize: 0x1C0 }
  - { offset: 0x1033A8, size: 0x8, addend: 0x0, symName: _getCurFilename, symObjAddr: 0x340, symBinAddr: 0x5F310, symSize: 0x300 }
  - { offset: 0x1034C2, size: 0x8, addend: 0x0, symName: _parseAndSetFilename, symObjAddr: 0x720, symBinAddr: 0x5F610, symSize: 0x5D0 }
  - { offset: 0x103741, size: 0x8, addend: 0x0, symName: _truncateCurrentFile, symObjAddr: 0x1AC0, symBinAddr: 0x604D0, symSize: 0xB0 }
  - { offset: 0x1037B6, size: 0x8, addend: 0x0, symName: _initializeProfileForContinuousMode, symObjAddr: 0x1B70, symBinAddr: 0x60580, symSize: 0x270 }
  - { offset: 0x1039CF, size: 0x8, addend: 0x0, symName: ___llvm_profile_initialize, symObjAddr: 0xCF0, symBinAddr: 0x5FBE0, symSize: 0xA0 }
  - { offset: 0x103A87, size: 0x8, addend: 0x0, symName: ___llvm_profile_write_file, symObjAddr: 0xE00, symBinAddr: 0x5FC80, symSize: 0x170 }
  - { offset: 0x103B85, size: 0x8, addend: 0x0, symName: _writeFile, symObjAddr: 0xF70, symBinAddr: 0x5FDF0, symSize: 0x260 }
  - { offset: 0x103DF6, size: 0x8, addend: 0x0, symName: _createProfileDir, symObjAddr: 0x1DE0, symBinAddr: 0x607F0, symSize: 0x80 }
  - { offset: 0x103E3B, size: 0x8, addend: 0x0, symName: _getProfileFileSizeForMerging, symObjAddr: 0x1650, symBinAddr: 0x60060, symSize: 0xD0 }
  - { offset: 0x103EBE, size: 0x8, addend: 0x0, symName: _mmapProfileForMerging, symObjAddr: 0x1720, symBinAddr: 0x60130, symSize: 0xB0 }
  - { offset: 0x103F9D, size: 0x8, addend: 0x0, symName: _writeFileWithoutReturn, symObjAddr: 0x1480, symBinAddr: 0x60050, symSize: 0x10 }
  - { offset: 0x103FC5, size: 0x8, addend: 0x0, symName: _mmapForContinuousMode, symObjAddr: 0x17D0, symBinAddr: 0x601E0, symSize: 0x1A0 }
  - { offset: 0x10411B, size: 0x8, addend: 0x0, symName: _fileWriter, symObjAddr: 0x1970, symBinAddr: 0x60380, symSize: 0x150 }
  - { offset: 0x104177, size: 0x8, addend: 0x0, symName: _exitSignalHandler, symObjAddr: 0x1E60, symBinAddr: 0x60870, symSize: 0xB }
  - { offset: 0x1041CA, size: 0x8, addend: 0x0, symName: _lprofGetLoadModuleSignature, symObjAddr: 0x0, symBinAddr: 0x60880, symSize: 0xE0 }
  - { offset: 0x1041D8, size: 0x8, addend: 0x0, symName: _lprofGetLoadModuleSignature, symObjAddr: 0x0, symBinAddr: 0x60880, symSize: 0xE0 }
  - { offset: 0x104305, size: 0x8, addend: 0x0, symName: ___llvm_profile_check_compatibility, symObjAddr: 0xE0, symBinAddr: 0x60960, symSize: 0x160 }
  - { offset: 0x1043C9, size: 0x8, addend: 0x0, symName: ___llvm_profile_merge_from_buffer, symObjAddr: 0x240, symBinAddr: 0x60AC0, symSize: 0x20D }
  - { offset: 0x104484, size: 0x8, addend: 0x0, symName: _lprofMergeValueProfData, symObjAddr: 0x0, symBinAddr: 0x60CD0, symSize: 0x106 }
  - { offset: 0x104492, size: 0x8, addend: 0x0, symName: _lprofMergeValueProfData, symObjAddr: 0x0, symBinAddr: 0x60CD0, symSize: 0x106 }
  - { offset: 0x10452A, size: 0x8, addend: 0x0, symName: _lprofBufferWriter, symObjAddr: 0x0, symBinAddr: 0x60DE0, symSize: 0x80 }
  - { offset: 0x104538, size: 0x8, addend: 0x0, symName: _lprofBufferWriter, symObjAddr: 0x0, symBinAddr: 0x60DE0, symSize: 0x80 }
  - { offset: 0x10455F, size: 0x8, addend: 0x0, symName: _lprofBufferIOWrite, symObjAddr: 0x120, symBinAddr: 0x60E60, symSize: 0x110 }
  - { offset: 0x1045CA, size: 0x8, addend: 0x0, symName: _lprofWriteData, symObjAddr: 0x2C0, symBinAddr: 0x60F70, symSize: 0x80 }
  - { offset: 0x10466F, size: 0x8, addend: 0x0, symName: _lprofWriteDataImpl, symObjAddr: 0x340, symBinAddr: 0x60FF0, symSize: 0x400 }
  - { offset: 0x1047CA, size: 0x8, addend: 0x0, symName: _createHeader, symObjAddr: 0x740, symBinAddr: 0x613F0, symSize: 0x140 }
  - { offset: 0x104874, size: 0x8, addend: 0x0, symName: _writeOneValueProfData, symObjAddr: 0xE30, symBinAddr: 0x61530, symSize: 0x2CE }
  - { offset: 0x1049B2, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_data, symObjAddr: 0x0, symBinAddr: 0x61800, symSize: 0x10 }
  - { offset: 0x1049C0, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_data, symObjAddr: 0x0, symBinAddr: 0x61800, symSize: 0x10 }
  - { offset: 0x1049D2, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_data, symObjAddr: 0x10, symBinAddr: 0x61810, symSize: 0x10 }
  - { offset: 0x1049E4, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_names, symObjAddr: 0x20, symBinAddr: 0x61820, symSize: 0x10 }
  - { offset: 0x1049F6, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_names, symObjAddr: 0x30, symBinAddr: 0x61830, symSize: 0x10 }
  - { offset: 0x104A08, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_counters, symObjAddr: 0x40, symBinAddr: 0x61840, symSize: 0x10 }
  - { offset: 0x104A1A, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_counters, symObjAddr: 0x50, symBinAddr: 0x61850, symSize: 0x10 }
  - { offset: 0x104A2C, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_vnodes, symObjAddr: 0x70, symBinAddr: 0x61860, symSize: 0x10 }
  - { offset: 0x104A3E, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_vnodes, symObjAddr: 0x80, symBinAddr: 0x61870, symSize: 0x10 }
  - { offset: 0x104A50, size: 0x8, addend: 0x0, symName: ___llvm_write_binary_ids, symObjAddr: 0x90, symBinAddr: 0x61880, symSize: 0x8 }
  - { offset: 0x104A8C, size: 0x8, addend: 0x0, symName: ___llvm_profile_recursive_mkdir, symObjAddr: 0x0, symBinAddr: 0x618B0, symSize: 0x40 }
  - { offset: 0x104AA1, size: 0x8, addend: 0x0, symName: ___llvm_profile_recursive_mkdir, symObjAddr: 0x0, symBinAddr: 0x618B0, symSize: 0x40 }
  - { offset: 0x104AE0, size: 0x8, addend: 0x0, symName: _lprofGetHostName, symObjAddr: 0x60, symBinAddr: 0x618F0, symSize: 0x70 }
  - { offset: 0x104B2A, size: 0x8, addend: 0x0, symName: _lprofLockFileHandle, symObjAddr: 0x1B0, symBinAddr: 0x61960, symSize: 0x80 }
  - { offset: 0x104BA8, size: 0x8, addend: 0x0, symName: _lprofUnlockFileHandle, symObjAddr: 0x230, symBinAddr: 0x619E0, symSize: 0x80 }
  - { offset: 0x104C16, size: 0x8, addend: 0x0, symName: _lprofOpenFileEx, symObjAddr: 0x2B0, symBinAddr: 0x61A60, symSize: 0xC0 }
  - { offset: 0x104CBA, size: 0x8, addend: 0x0, symName: _lprofFindFirstDirSeparator, symObjAddr: 0x480, symBinAddr: 0x61B20, symSize: 0x10 }
  - { offset: 0x104CE3, size: 0x8, addend: 0x0, symName: _lprofInstallSignalHandler, symObjAddr: 0x4A0, symBinAddr: 0x61B30, symSize: 0x50 }
...
