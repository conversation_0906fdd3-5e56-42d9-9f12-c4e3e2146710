---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/DerivedData/iphonesimulator/Build/Products/Release-iphonesimulator/Razorpay.framework/Razorpay'
relocations:
  - { offset: 0xBC516, size: 0x8, addend: 0x0, symName: _RazorpayVersionString, symObjAddr: 0x0, symBinAddr: 0x649A0, symSize: 0x0 }
  - { offset: 0xBC54B, size: 0x8, addend: 0x0, symName: _RazorpayVersionNumber, symObjAddr: 0x30, symBinAddr: 0x649D0, symSize: 0x0 }
  - { offset: 0xBC5B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC14sharedInstanceACvpZ', symObjAddr: 0x10DB0, symBinAddr: 0xBA1C0, symSize: 0x0 }
  - { offset: 0xBC6C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC14sharedInstance_WZ', symObjAddr: 0x1A0, symBinAddr: 0x4824, symSize: 0x38 }
  - { offset: 0xBC760, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCMa', symObjAddr: 0x474, symBinAddr: 0x4AF8, symSize: 0x20 }
  - { offset: 0xBC831, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCSQWb', symObjAddr: 0x938, symBinAddr: 0x4FA0, symSize: 0x2C }
  - { offset: 0xBC845, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCs25ExpressibleByArrayLiteralPWb', symObjAddr: 0x964, symBinAddr: 0x4FCC, symSize: 0x2C }
  - { offset: 0xBC859, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCSYWb', symObjAddr: 0x9D0, symBinAddr: 0x5038, symSize: 0x2C }
  - { offset: 0xBC86D, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCs0E7AlgebraPWb', symObjAddr: 0x9FC, symBinAddr: 0x5064, symSize: 0x2C }
  - { offset: 0xBC8FA, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0xBE4, symBinAddr: 0x5240, symSize: 0x48 }
  - { offset: 0xBC90E, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xC2C, symBinAddr: 0x5288, symSize: 0x40 }
  - { offset: 0xBC922, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0xC6C, symBinAddr: 0x52C8, symSize: 0x40 }
  - { offset: 0xBC936, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0xCC0, symBinAddr: 0x531C, symSize: 0x2C }
  - { offset: 0xBC94A, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0xCEC, symBinAddr: 0x5348, symSize: 0x2C }
  - { offset: 0xBC95E, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSQWb', symObjAddr: 0xD18, symBinAddr: 0x5374, symSize: 0x2C }
  - { offset: 0xBC972, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_4, symObjAddr: 0xD44, symBinAddr: 0x53A0, symSize: 0xC }
  - { offset: 0xBC986, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0xD50, symBinAddr: 0x53AC, symSize: 0x4 }
  - { offset: 0xBC99A, size: 0x8, addend: 0x0, symName: '_$sSo11sockaddr_inVwet', symObjAddr: 0xD54, symBinAddr: 0x53B0, symSize: 0x20 }
  - { offset: 0xBC9AE, size: 0x8, addend: 0x0, symName: '_$sSo11sockaddr_inVwst', symObjAddr: 0xD74, symBinAddr: 0x53D0, symSize: 0x28 }
  - { offset: 0xBCA49, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo38UIApplicationOpenExternalURLOptionsKeya_Tgmq5', symObjAddr: 0x4BC, symBinAddr: 0x4B40, symSize: 0x84 }
  - { offset: 0xBCAE0, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo38UIApplicationOpenExternalURLOptionsKeya_Tgmq5', symObjAddr: 0x540, symBinAddr: 0x4BC4, symSize: 0x8C }
  - { offset: 0xBCB86, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x5E0, symBinAddr: 0x4C5C, symSize: 0x14 }
  - { offset: 0xBCBCA, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACPxycfCTW', symObjAddr: 0x600, symBinAddr: 0x4C7C, symSize: 0x8 }
  - { offset: 0xBCC13, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP5unionyxxnFTW', symObjAddr: 0x608, symBinAddr: 0x4C84, symSize: 0x14 }
  - { offset: 0xBCCB0, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP12intersectionyxxFTW', symObjAddr: 0x61C, symBinAddr: 0x4C98, symSize: 0x14 }
  - { offset: 0xBCD45, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP19symmetricDifferenceyxxnFTW', symObjAddr: 0x630, symBinAddr: 0x4CAC, symSize: 0x14 }
  - { offset: 0xBCDE2, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6insertySb8inserted_7ElementQz17memberAfterInserttAHnFTW', symObjAddr: 0x644, symBinAddr: 0x4CC0, symSize: 0x30 }
  - { offset: 0xBCF55, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6removey7ElementQzSgAGFTW', symObjAddr: 0x674, symBinAddr: 0x4CF0, symSize: 0x2C }
  - { offset: 0xBD0E2, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP6update4with7ElementQzSgAHn_tFTW', symObjAddr: 0x6A0, symBinAddr: 0x4D1C, symSize: 0x24 }
  - { offset: 0xBD1CE, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP9formUnionyyxnFTW', symObjAddr: 0x6C4, symBinAddr: 0x4D40, symSize: 0x14 }
  - { offset: 0xBD234, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP16formIntersectionyyxFTW', symObjAddr: 0x6D8, symBinAddr: 0x4D54, symSize: 0x14 }
  - { offset: 0xBD29A, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP23formSymmetricDifferenceyyxnFTW', symObjAddr: 0x6EC, symBinAddr: 0x4D68, symSize: 0x14 }
  - { offset: 0xBD30F, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP11subtractingyxxFTW', symObjAddr: 0x700, symBinAddr: 0x4D7C, symSize: 0x14 }
  - { offset: 0xBD3B8, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP8isSubset2ofSbx_tFTW', symObjAddr: 0x714, symBinAddr: 0x4D90, symSize: 0x14 }
  - { offset: 0xBD42F, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP10isDisjoint4withSbx_tFTW', symObjAddr: 0x728, symBinAddr: 0x4DA4, symSize: 0x14 }
  - { offset: 0xBD4E7, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP10isSuperset2ofSbx_tFTW', symObjAddr: 0x73C, symBinAddr: 0x4DB8, symSize: 0x14 }
  - { offset: 0xBD581, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP7isEmptySbvgTW', symObjAddr: 0x750, symBinAddr: 0x4DCC, symSize: 0x10 }
  - { offset: 0xBD5E9, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACPyxqd__ncSTRd__7ElementQyd__AERtzlufCTW', symObjAddr: 0x760, symBinAddr: 0x4DDC, symSize: 0x18 }
  - { offset: 0xBD605, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs10SetAlgebraSCsACP8subtractyyxFTW', symObjAddr: 0x778, symBinAddr: 0x4DF4, symSize: 0x14 }
  - { offset: 0xBD681, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromF1C_6resulty01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x7A8, symBinAddr: 0x4E24, symSize: 0x4 }
  - { offset: 0xBD69D, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromF1C_6resultSb01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x7AC, symBinAddr: 0x4E28, symSize: 0x4 }
  - { offset: 0xBD6C8, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromF1Cyx01_F5CTypeQzSgFZTW', symObjAddr: 0x7B0, symBinAddr: 0x4E2C, symSize: 0x40 }
  - { offset: 0xBD746, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x838, symBinAddr: 0x4EB4, symSize: 0x40 }
  - { offset: 0xBD7CA, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x878, symBinAddr: 0x4EF4, symSize: 0x70 }
  - { offset: 0xBD848, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs25ExpressibleByArrayLiteralSCsACP05arrayG0x0fG7ElementQzd_tcfCTW', symObjAddr: 0x8FC, symBinAddr: 0x4F64, symSize: 0x3C }
  - { offset: 0xBD897, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0xA28, symBinAddr: 0x5090, symSize: 0x88 }
  - { offset: 0xBD941, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0xB28, symBinAddr: 0x5184, symSize: 0x84 }
  - { offset: 0xBD999, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBAC, symBinAddr: 0x5208, symSize: 0x38 }
  - { offset: 0xBD9B9, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBAC, symBinAddr: 0x5208, symSize: 0x38 }
  - { offset: 0xBD9CD, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBAC, symBinAddr: 0x5208, symSize: 0x38 }
  - { offset: 0xBD9ED, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBAC, symBinAddr: 0x5208, symSize: 0x38 }
  - { offset: 0xBDA01, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBAC, symBinAddr: 0x5208, symSize: 0x38 }
  - { offset: 0xBDA15, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBAC, symBinAddr: 0x5208, symSize: 0x38 }
  - { offset: 0xBDA29, size: 0x8, addend: 0x0, symName: '_$ss10SetAlgebraPs7ElementQz012ArrayLiteralC0RtzrlE05arrayE0xAFd_tcfCSo26SCNetworkReachabilityFlagsV_Tgq5Tf4gd_n', symObjAddr: 0xBAC, symBinAddr: 0x5208, symSize: 0x38 }
  - { offset: 0xBDB26, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCACyc33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7Llfc', symObjAddr: 0x0, symBinAddr: 0x4684, symSize: 0x1A0 }
  - { offset: 0xBDC27, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC013latestFetchedC033_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LLSDySSypGvW', symObjAddr: 0x1D8, symBinAddr: 0x485C, symSize: 0x1EC }
  - { offset: 0xBDCEA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC05fetchC0yyySDySSypGSgcFyAA0bC13FetchResponseO_AFtcfU_', symObjAddr: 0x3C4, symBinAddr: 0x4A48, symSize: 0x84 }
  - { offset: 0xBDD86, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperCfD', symObjAddr: 0x448, symBinAddr: 0x4ACC, symSize: 0x2C }
  - { offset: 0xBDE8C, size: 0x8, addend: 0x0, symName: '_$sSo21CLAuthorizationStatusVSYSCSY8rawValue03RawD0QzvgTW', symObjAddr: 0x5D4, symBinAddr: 0x4C50, symSize: 0xC }
  - { offset: 0xBDF0C, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVs9OptionSetSCsACP8rawValuex03RawG0Qz_tcfCTW', symObjAddr: 0x5F4, symBinAddr: 0x4C70, symSize: 0xC }
  - { offset: 0xBDF34, size: 0x8, addend: 0x0, symName: '_$sSo26SCNetworkReachabilityFlagsVSYSCSY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x78C, symBinAddr: 0x4E08, symSize: 0x10 }
  - { offset: 0xBDF56, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSYSCSY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0xABC, symBinAddr: 0x5118, symSize: 0x44 }
  - { offset: 0xBDF7F, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSYSCSY8rawValue03RawG0QzvgTW', symObjAddr: 0xB00, symBinAddr: 0x515C, symSize: 0x28 }
  - { offset: 0xBE11E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LL_WZ', symObjAddr: 0x0, symBinAddr: 0x546C, symSize: 0x38 }
  - { offset: 0xBE142, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LLACvpZ', symObjAddr: 0xA50, symBinAddr: 0xA2DD0, symSize: 0x0 }
  - { offset: 0xBE24C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC08razorpaybC033_E8D744B561759FFCD77083C4471D61B5LL_WZ', symObjAddr: 0x0, symBinAddr: 0x546C, symSize: 0x38 }
  - { offset: 0xBE29B, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIeghggg_So6NSDataCSgAGSo7NSErrorCSgIeyBhyyy_TR', symObjAddr: 0x2AC, symBinAddr: 0x5718, symSize: 0xC8 }
  - { offset: 0xBE2B3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsCMa', symObjAddr: 0x398, symBinAddr: 0x5804, symSize: 0x20 }
  - { offset: 0xBE2D8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFyAKSg_ANs5Error_pSgtYbcfU_TA', symObjAddr: 0x74C, symBinAddr: 0x5BB8, symSize: 0x90 }
  - { offset: 0xBE2EC, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x7DC, symBinAddr: 0x5C48, symSize: 0x10 }
  - { offset: 0xBE300, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x7EC, symBinAddr: 0x5C58, symSize: 0x8 }
  - { offset: 0xBE314, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x7F4, symBinAddr: 0x5C60, symSize: 0x14 }
  - { offset: 0xBE328, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x808, symBinAddr: 0x5C74, symSize: 0x44 }
  - { offset: 0xBE33C, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x84C, symBinAddr: 0x5CB8, symSize: 0x44 }
  - { offset: 0xBE350, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC16getStatusCodeFor10urlRequest14requestHandlery10Foundation10URLRequestV_ySo13NSURLResponseCSgctFyAG4DataVSg_ALs5Error_pSgtYbcfU_TA', symObjAddr: 0x9F0, symBinAddr: 0x5E5C, symSize: 0x34 }
  - { offset: 0xBE499, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFyAKSg_ANs5Error_pSgtYbcfU_', symObjAddr: 0x38, symBinAddr: 0x54A4, symSize: 0x274 }
  - { offset: 0xBE590, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsCfD', symObjAddr: 0x374, symBinAddr: 0x57E0, symSize: 0x24 }
  - { offset: 0xBE666, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC14sharedInstanceACyFZTf4d_g', symObjAddr: 0x3B8, symBinAddr: 0x5824, symSize: 0x128 }
  - { offset: 0xBE6DC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC11sendRequest_11withSuccess0F7Failurey10Foundation10URLRequestV_yAI_AG4DataVSo13NSURLResponseCSgtcyAI_So7NSErrorCSgSStctFTf4nnnd_n', symObjAddr: 0x4E0, symBinAddr: 0x594C, symSize: 0x1D8 }
  - { offset: 0xBE744, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13InternetUtilsC16getStatusCodeFor10urlRequest14requestHandlery10Foundation10URLRequestV_ySo13NSURLResponseCSgctFTf4nnd_n', symObjAddr: 0x890, symBinAddr: 0x5CFC, symSize: 0x13C }
  - { offset: 0xBE977, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11isSetupDone33_97E3ACE2739C3B14C03A312B86CD9A70LLSbvpZ', symObjAddr: 0x1B50, symBinAddr: 0xA2EA8, symSize: 0x0 }
  - { offset: 0xBE991, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC14sharedInstance33_97E3ACE2739C3B14C03A312B86CD9A70LLACvpZ', symObjAddr: 0x1B58, symBinAddr: 0xA2EB0, symSize: 0x0 }
  - { offset: 0xBEB10, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19networkConnectivityAA08InternetE0OvpZ', symObjAddr: 0x1B60, symBinAddr: 0xA2EB8, symSize: 0x0 }
  - { offset: 0xBEB2A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC14sharedInstance33_97E3ACE2739C3B14C03A312B86CD9A70LL_WZ', symObjAddr: 0x0, symBinAddr: 0x5E98, symSize: 0x38 }
  - { offset: 0xBEE76, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCMa', symObjAddr: 0x1338, symBinAddr: 0x71D0, symSize: 0x20 }
  - { offset: 0xBEE8A, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x1358, symBinAddr: 0x71F0, symSize: 0x10 }
  - { offset: 0xBEEE0, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x1A04, symBinAddr: 0x785C, symSize: 0x24 }
  - { offset: 0xBEEF4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVWOr', symObjAddr: 0x1A28, symBinAddr: 0x7880, symSize: 0x44 }
  - { offset: 0xBEF08, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x1ACC, symBinAddr: 0x78E4, symSize: 0x3C }
  - { offset: 0xBF188, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCACyc33_97E3ACE2739C3B14C03A312B86CD9A70Llfc', symObjAddr: 0x38, symBinAddr: 0x5ED0, symSize: 0x10C }
  - { offset: 0xBF25B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19resetPaymentSessionyyF', symObjAddr: 0x144, symBinAddr: 0x5FDC, symSize: 0x140 }
  - { offset: 0xBF2E8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC10trackEvent_16havingProperties02cxG0yAA0bE0O_SDys11AnyHashableVypGAKSgtF', symObjAddr: 0x284, symBinAddr: 0x611C, symSize: 0x140 }
  - { offset: 0xBF3DC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11addProperty9havingKey03andE0ySS_AA0bE0CtF', symObjAddr: 0x3C4, symBinAddr: 0x625C, symSize: 0xCC }
  - { offset: 0xBF46F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC11reportError_11havingLevel16andCustomMessage11errorSourceySo7NSErrorCSg_AA0aeG0OS2SSgtF', symObjAddr: 0x490, symBinAddr: 0x6328, symSize: 0x8C0 }
  - { offset: 0xBF88D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC34removeReachabilityNotifierObserveryyF', symObjAddr: 0xD50, symBinAddr: 0x6BE8, symSize: 0x1B8 }
  - { offset: 0xBF937, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC31setReachabilityNotifierObserveryyF', symObjAddr: 0xF08, symBinAddr: 0x6DA0, symSize: 0x27C }
  - { offset: 0xBFA07, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19reachabilityChanged4notey10Foundation12NotificationV_tF', symObjAddr: 0x1184, symBinAddr: 0x701C, symSize: 0xF8 }
  - { offset: 0xBFA9C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC19reachabilityChanged4notey10Foundation12NotificationV_tFTo', symObjAddr: 0x127C, symBinAddr: 0x7114, symSize: 0x90 }
  - { offset: 0xBFAC4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCfD', symObjAddr: 0x130C, symBinAddr: 0x71A4, symSize: 0x2C }
  - { offset: 0xBFB41, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC21generateCXErrorObject7cxErrorSDySSypGAA0dE8Protocol_p_tFTf4en_nAA0D0V_TB5Tf4nd_n', symObjAddr: 0x13A8, symBinAddr: 0x7200, symSize: 0x628 }
  - { offset: 0xC0132, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x7920, symSize: 0xC }
  - { offset: 0xC014D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x7920, symSize: 0xC }
  - { offset: 0xC01C0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH9hashValueSivgTW', symObjAddr: 0xC, symBinAddr: 0x792C, symSize: 0x80 }
  - { offset: 0xC0274, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x8C, symBinAddr: 0x79AC, symSize: 0x5C }
  - { offset: 0xC02E6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xE8, symBinAddr: 0x7A08, symSize: 0x7C }
  - { offset: 0xC0393, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs28CustomDebugStringConvertibleAAsAHP16debugDescriptionSSvgTW', symObjAddr: 0x2DC, symBinAddr: 0x7BFC, symSize: 0x28 }
  - { offset: 0xC03AF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs23CustomStringConvertibleAAsAHP11descriptionSSvgTW', symObjAddr: 0x304, symBinAddr: 0x7C24, symSize: 0x28 }
  - { offset: 0xC043C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x62C, symBinAddr: 0x7F4C, symSize: 0x3C }
  - { offset: 0xC04D8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0x720, symBinAddr: 0x8034, symSize: 0x28 }
  - { offset: 0xC04F4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0x748, symBinAddr: 0x805C, symSize: 0x28 }
  - { offset: 0xC08D9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCMa', symObjAddr: 0x7A8, symBinAddr: 0x80BC, symSize: 0x20 }
  - { offset: 0xC08ED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VwCP', symObjAddr: 0x8BC, symBinAddr: 0x81D0, symSize: 0x30 }
  - { offset: 0xC0901, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwxx', symObjAddr: 0x8EC, symBinAddr: 0x8200, symSize: 0x28 }
  - { offset: 0xC0915, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwcp', symObjAddr: 0x914, symBinAddr: 0x8228, symSize: 0x3C }
  - { offset: 0xC0929, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwca', symObjAddr: 0x950, symBinAddr: 0x8264, symSize: 0x6C }
  - { offset: 0xC093D, size: 0x8, addend: 0x0, symName: ___swift_memcpy32_8, symObjAddr: 0x9BC, symBinAddr: 0x82D0, symSize: 0xC }
  - { offset: 0xC0951, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwta', symObjAddr: 0x9C8, symBinAddr: 0x82DC, symSize: 0x44 }
  - { offset: 0xC0965, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwet', symObjAddr: 0xA0C, symBinAddr: 0x8320, symSize: 0x48 }
  - { offset: 0xC0979, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0Vwst', symObjAddr: 0xA54, symBinAddr: 0x8368, symSize: 0x40 }
  - { offset: 0xC098D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VMa', symObjAddr: 0xA94, symBinAddr: 0x83A8, symSize: 0x10 }
  - { offset: 0xC09A1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs0F3KeyAAWl', symObjAddr: 0xCC4, symBinAddr: 0x8574, symSize: 0x44 }
  - { offset: 0xC09B5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VAESeAAWl', symObjAddr: 0xD28, symBinAddr: 0x85B8, symSize: 0x44 }
  - { offset: 0xC09C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs0F3KeyAAWl', symObjAddr: 0xF08, symBinAddr: 0x8798, symSize: 0x44 }
  - { offset: 0xC09DD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VAESEAAWl', symObjAddr: 0xF4C, symBinAddr: 0x87DC, symSize: 0x44 }
  - { offset: 0xC09F1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCACSEAAWl', symObjAddr: 0xF90, symBinAddr: 0x8820, symSize: 0x44 }
  - { offset: 0xC0A05, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x1018, symBinAddr: 0x8864, symSize: 0xC }
  - { offset: 0xC0A19, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwet', symObjAddr: 0x1028, symBinAddr: 0x8870, symSize: 0x90 }
  - { offset: 0xC0A2D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwst', symObjAddr: 0x10B8, symBinAddr: 0x8900, symSize: 0xBC }
  - { offset: 0xC0A41, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwug', symObjAddr: 0x1174, symBinAddr: 0x89BC, symSize: 0x8 }
  - { offset: 0xC0A55, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwup', symObjAddr: 0x117C, symBinAddr: 0x89C4, symSize: 0x4 }
  - { offset: 0xC0A69, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOwui', symObjAddr: 0x1180, symBinAddr: 0x89C8, symSize: 0xC }
  - { offset: 0xC0A7D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOMa', symObjAddr: 0x118C, symBinAddr: 0x89D4, symSize: 0x10 }
  - { offset: 0xC0A91, size: 0x8, addend: 0x0, symName: ___swift_memcpy0_1, symObjAddr: 0x119C, symBinAddr: 0x89E4, symSize: 0x4 }
  - { offset: 0xC0AA5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwet', symObjAddr: 0x11A0, symBinAddr: 0x89E8, symSize: 0x50 }
  - { offset: 0xC0AB9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwst', symObjAddr: 0x11F0, symBinAddr: 0x8A38, symSize: 0x8C }
  - { offset: 0xC0ACD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOwug', symObjAddr: 0x127C, symBinAddr: 0x8AC4, symSize: 0x8 }
  - { offset: 0xC0AE1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOMa', symObjAddr: 0x128C, symBinAddr: 0x8ACC, symSize: 0x10 }
  - { offset: 0xC0AF5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASQWb', symObjAddr: 0x129C, symBinAddr: 0x8ADC, symSize: 0x4 }
  - { offset: 0xC0B09, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFSQAAWl', symObjAddr: 0x12A0, symBinAddr: 0x8AE0, symSize: 0x44 }
  - { offset: 0xC0B1D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSHAASQWb', symObjAddr: 0x12E4, symBinAddr: 0x8B24, symSize: 0x4 }
  - { offset: 0xC0B31, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGSQAAWl', symObjAddr: 0x12E8, symBinAddr: 0x8B28, symSize: 0x44 }
  - { offset: 0xC0B45, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x132C, symBinAddr: 0x8B6C, symSize: 0x4 }
  - { offset: 0xC0B59, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x1330, symBinAddr: 0x8B70, symSize: 0x44 }
  - { offset: 0xC0B6D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x1374, symBinAddr: 0x8BB4, symSize: 0x4 }
  - { offset: 0xC0B81, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOAGs23CustomStringConvertibleAAWl', symObjAddr: 0x1378, symBinAddr: 0x8BB8, symSize: 0x44 }
  - { offset: 0xC0B95, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x13BC, symBinAddr: 0x8BFC, symSize: 0x4 }
  - { offset: 0xC0BA9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x13C0, symBinAddr: 0x8C00, symSize: 0x44 }
  - { offset: 0xC0BBD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x1404, symBinAddr: 0x8C44, symSize: 0x4 }
  - { offset: 0xC0BD1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x1408, symBinAddr: 0x8C48, symSize: 0x44 }
  - { offset: 0xC0C3A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSYAASY8rawValuexSg03RawI0Qz_tcfCTW', symObjAddr: 0x164, symBinAddr: 0x7A84, symSize: 0x70 }
  - { offset: 0xC0C87, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOSYAASY8rawValue03RawI0QzvgTW', symObjAddr: 0x1D4, symBinAddr: 0x7AF4, symSize: 0x40 }
  - { offset: 0xC0CCD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP11stringValueSSvgTW', symObjAddr: 0x214, symBinAddr: 0x7B34, symSize: 0x3C }
  - { offset: 0xC0D32, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP11stringValuexSgSS_tcfCTW', symObjAddr: 0x250, symBinAddr: 0x7B70, symSize: 0x74 }
  - { offset: 0xC0D96, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP8intValueSiSgvgTW', symObjAddr: 0x2C4, symBinAddr: 0x7BE4, symSize: 0xC }
  - { offset: 0xC0DAA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V10CodingKeysOs0F3KeyAAsAHP8intValuexSgSi_tcfCTW', symObjAddr: 0x2D0, symBinAddr: 0x7BF0, symSize: 0xC }
  - { offset: 0xC0DBE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V6encode2toys7Encoder_p_tKF', symObjAddr: 0x32C, symBinAddr: 0x7C4C, symSize: 0x118 }
  - { offset: 0xC0E02, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x444, symBinAddr: 0x7D64, symSize: 0x2C }
  - { offset: 0xC0E2B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0VSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x470, symBinAddr: 0x7D90, symSize: 0x1C }
  - { offset: 0xC0E76, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10jsonStringSSSgyF', symObjAddr: 0x48C, symBinAddr: 0x7DAC, symSize: 0x134 }
  - { offset: 0xC0ECD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x5C0, symBinAddr: 0x7EE0, symSize: 0x8 }
  - { offset: 0xC0F08, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH9hashValueSivgTW', symObjAddr: 0x5C8, symBinAddr: 0x7EE8, symSize: 0x40 }
  - { offset: 0xC0FCE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x608, symBinAddr: 0x7F28, symSize: 0x24 }
  - { offset: 0xC102A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0x668, symBinAddr: 0x7F88, symSize: 0x14 }
  - { offset: 0xC105D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0x67C, symBinAddr: 0x7F9C, symSize: 0x8C }
  - { offset: 0xC10BF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC10CodingKeys33_17BD83B7D35A4094460369732B091861LLOs0F3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0x714, symBinAddr: 0x8028, symSize: 0xC }
  - { offset: 0xC10FC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCfD', symObjAddr: 0x770, symBinAddr: 0x8084, symSize: 0x38 }
  - { offset: 0xC113F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC6encode2toys7Encoder_p_tKF', symObjAddr: 0x7C8, symBinAddr: 0x80DC, symSize: 0xF4 }
  - { offset: 0xC1170, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC4fromACs7Decoder_p_tKcfc', symObjAddr: 0xAA4, symBinAddr: 0x83B8, symSize: 0x148 }
  - { offset: 0xC11AE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0xBEC, symBinAddr: 0x8500, symSize: 0x54 }
  - { offset: 0xC11D7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseCSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0xC40, symBinAddr: 0x8554, symSize: 0x20 }
  - { offset: 0xC11EB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay27CheckoutBridgeErrorResponseC0bD0V4fromAEs7Decoder_p_tKcfCTf4nd_n', symObjAddr: 0xD6C, symBinAddr: 0x85FC, symSize: 0x19C }
  - { offset: 0xC144C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCMa', symObjAddr: 0xF4, symBinAddr: 0x8D80, symSize: 0x20 }
  - { offset: 0xC1476, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCfETo', symObjAddr: 0x1BC, symBinAddr: 0x8E48, symSize: 0x14 }
  - { offset: 0xC14A5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24UPITurboPrefetchProtocolPAAE26prefetchAndLinkUpiAccounts27linkAccountWithUPIPinNotSet0J14ActionDelegateySb_yptF', symObjAddr: 0x1D0, symBinAddr: 0x8E5C, symSize: 0x6C }
  - { offset: 0xC14FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay30UPITurboPrefetchWithUIProtocolPAAE026prefetchAndLinkUpiAccountsD2UI17completionHandleryyypSg_AFtc_tF', symObjAddr: 0x260, symBinAddr: 0x8EC8, symSize: 0xA8 }
  - { offset: 0xC1547, size: 0x8, addend: 0x0, symName: '_$sypSgAAIegnn_yXlSgABIeyByy_TR', symObjAddr: 0x308, symBinAddr: 0x8F70, symSize: 0xB0 }
  - { offset: 0xC155F, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3B8, symBinAddr: 0x9020, symSize: 0x10 }
  - { offset: 0xC1573, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3C8, symBinAddr: 0x9030, symSize: 0x8 }
  - { offset: 0xC1588, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenSSvg', symObjAddr: 0x0, symBinAddr: 0x8C8C, symSize: 0x38 }
  - { offset: 0xC15DD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenACSS_tcfC', symObjAddr: 0x38, symBinAddr: 0x8CC4, symSize: 0x6C }
  - { offset: 0xC161C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionC5tokenACSS_tcfc', symObjAddr: 0xA4, symBinAddr: 0x8D30, symSize: 0x50 }
  - { offset: 0xC1645, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfC', symObjAddr: 0x114, symBinAddr: 0x8DA0, symSize: 0x20 }
  - { offset: 0xC165F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfc', symObjAddr: 0x134, symBinAddr: 0x8DC0, symSize: 0x2C }
  - { offset: 0xC16B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCACycfcTo', symObjAddr: 0x160, symBinAddr: 0x8DEC, symSize: 0x2C }
  - { offset: 0xC1717, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7SessionCfD', symObjAddr: 0x18C, symBinAddr: 0x8E18, symSize: 0x30 }
  - { offset: 0xC192B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x9084, symSize: 0x14 }
  - { offset: 0xC1A14, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsCMa', symObjAddr: 0xE0, symBinAddr: 0x9164, symSize: 0x20 }
  - { offset: 0xC1A28, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwet', symObjAddr: 0x110, symBinAddr: 0x9184, symSize: 0x90 }
  - { offset: 0xC1A3C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwst', symObjAddr: 0x1A0, symBinAddr: 0x9214, symSize: 0xBC }
  - { offset: 0xC1A50, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOwui', symObjAddr: 0x268, symBinAddr: 0x92D0, symSize: 0x8 }
  - { offset: 0xC1A64, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOMa', symObjAddr: 0x270, symBinAddr: 0x92D8, symSize: 0x10 }
  - { offset: 0xC1A78, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASQWb', symObjAddr: 0x280, symBinAddr: 0x92E8, symSize: 0x4 }
  - { offset: 0xC1A8C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOAESQAAWl', symObjAddr: 0x284, symBinAddr: 0x92EC, symSize: 0x44 }
  - { offset: 0xC1ADB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x80, symBinAddr: 0x9104, symSize: 0x40 }
  - { offset: 0xC1B62, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP7_domainSSvgTW', symObjAddr: 0xC0, symBinAddr: 0x9144, symSize: 0x4 }
  - { offset: 0xC1B7E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP5_codeSivgTW', symObjAddr: 0xC4, symBinAddr: 0x9148, symSize: 0x4 }
  - { offset: 0xC1B9A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP9_userInfoyXlSgvgTW', symObjAddr: 0xC8, symBinAddr: 0x914C, symSize: 0x4 }
  - { offset: 0xC1BB6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOs0D0AAsAFP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0xCC, symBinAddr: 0x9150, symSize: 0x4 }
  - { offset: 0xC1C06, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x9084, symSize: 0x14 }
  - { offset: 0xC1C82, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH9hashValueSivgTW', symObjAddr: 0x14, symBinAddr: 0x9098, symSize: 0x44 }
  - { offset: 0xC1D48, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x58, symBinAddr: 0x90DC, symSize: 0x28 }
  - { offset: 0xC1DA5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsCfD', symObjAddr: 0xD0, symBinAddr: 0x9154, symSize: 0x10 }
  - { offset: 0xC1F68, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14sharedInstance33_CAD7F1E8D00B6203AC8D8943A778DF45LLACvpZ', symObjAddr: 0x35C8, symBinAddr: 0xA32A8, symSize: 0x0 }
  - { offset: 0xC21AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14sharedInstance33_CAD7F1E8D00B6203AC8D8943A778DF45LL_WZ', symObjAddr: 0x178, symBinAddr: 0x94A8, symSize: 0x2C }
  - { offset: 0xC2403, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCfETo', symObjAddr: 0x22E8, symBinAddr: 0xB618, symSize: 0x80 }
  - { offset: 0xC2432, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCMa', symObjAddr: 0x2368, symBinAddr: 0xB698, symSize: 0x20 }
  - { offset: 0xC2446, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLCMa', symObjAddr: 0x2388, symBinAddr: 0xB6B8, symSize: 0x20 }
  - { offset: 0xC245A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLCMa', symObjAddr: 0x23A8, symBinAddr: 0xB6D8, symSize: 0x20 }
  - { offset: 0xC2605, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x3468, symBinAddr: 0xC714, symSize: 0x8 }
  - { offset: 0xC2619, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3470, symBinAddr: 0xC71C, symSize: 0x10 }
  - { offset: 0xC262D, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3480, symBinAddr: 0xC72C, symSize: 0x8 }
  - { offset: 0xC269E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUserrS0C_So08WKScriptF0CtFTo', symObjAddr: 0x0, symBinAddr: 0x9330, symSize: 0x68 }
  - { offset: 0xC26CF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUsersT0C_So08WKScriptG0CtFTo', symObjAddr: 0x80, symBinAddr: 0x93B0, symSize: 0x68 }
  - { offset: 0xC29C6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCACyc33_CAD7F1E8D00B6203AC8D8943A778DF45Llfc', symObjAddr: 0x1A4, symBinAddr: 0x94D4, symSize: 0x33C }
  - { offset: 0xC2A79, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCACyc33_CAD7F1E8D00B6203AC8D8943A778DF45LlfcTo', symObjAddr: 0x4E0, symBinAddr: 0x9810, symSize: 0x20 }
  - { offset: 0xC2AC4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC12setupBridges33_CAD7F1E8D00B6203AC8D8943A778DF45LLyyF', symObjAddr: 0x500, symBinAddr: 0x9830, symSize: 0x264 }
  - { offset: 0xC2C1A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14setPaymentDatayySDys11AnyHashableVypGF', symObjAddr: 0x764, symBinAddr: 0x9A94, symSize: 0x448 }
  - { offset: 0xC2E48, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0xBAC, symBinAddr: 0x9EDC, symSize: 0x28C }
  - { offset: 0xC2F78, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC14getSDKMetadata33_CAD7F1E8D00B6203AC8D8943A778DF45LLyyF', symObjAddr: 0xE38, symBinAddr: 0xA168, symSize: 0x1244 }
  - { offset: 0xC36E1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC5closeyyF', symObjAddr: 0x207C, symBinAddr: 0xB3AC, symSize: 0x238 }
  - { offset: 0xC3737, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfCfD', symObjAddr: 0x22B4, symBinAddr: 0xB5E4, symSize: 0x34 }
  - { offset: 0xC37CD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC27StorageBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUserrS0C_So08WKScriptF0CtFTf4dnd_n', symObjAddr: 0x23C8, symBinAddr: 0xB6F8, symSize: 0x514 }
  - { offset: 0xC3ACF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC26OtpElfBridgeMessageHandler33_CAD7F1E8D00B6203AC8D8943A778DF45LLC21userContentController_10didReceiveySo06WKUsersT0C_So08WKScriptG0CtFTf4dnd_n', symObjAddr: 0x28DC, symBinAddr: 0xBC0C, symSize: 0x504 }
  - { offset: 0xC3DA3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC15initWithWebView_14andMerchantKeyySo05WKWebG0C_SSSgtFZTf4nnd_n', symObjAddr: 0x2E64, symBinAddr: 0xC110, symSize: 0x40C }
  - { offset: 0xC3EC6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14InternalOtpelfC7webView9didFinishySo12WKNavigationCSg_tFTf4dn_n', symObjAddr: 0x3270, symBinAddr: 0xC51C, symSize: 0x1D4 }
  - { offset: 0xC40BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14sharedInstanceACvpZ', symObjAddr: 0x1B3F0, symBinAddr: 0xBA1C8, symSize: 0x0 }
  - { offset: 0xC42C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C10lumberjack33_C2103DC0A7BEA85E64BF4B6378FF9B75LLAA10LumberjackCSgvpZ', symObjAddr: 0x4DC8, symBinAddr: 0xA3370, symSize: 0x0 }
  - { offset: 0xC4319, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE8URIEventABvpZ', symObjAddr: 0x1B3F8, symBinAddr: 0xBA1D0, symSize: 0x0 }
  - { offset: 0xC4327, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14sharedInstance_WZ', symObjAddr: 0x0, symBinAddr: 0xC778, symSize: 0x2C }
  - { offset: 0xC46CB, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x1600, symBinAddr: 0xDD78, symSize: 0x2C }
  - { offset: 0xC4786, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CfETo', symObjAddr: 0x1B9C, symBinAddr: 0xE314, symSize: 0x12C }
  - { offset: 0xC47B5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CMa', symObjAddr: 0x1CC8, symBinAddr: 0xE440, symSize: 0x20 }
  - { offset: 0xC47C9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14paymentSuccess3str16dictVerificationySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x1CE8, symBinAddr: 0xE460, symSize: 0x128 }
  - { offset: 0xC4865, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14paymentFailure4code11description4datays5Int32VSg_SSSgSDys11AnyHashableVypGSgtF', symObjAddr: 0x1E10, symBinAddr: 0xE588, symSize: 0x250 }
  - { offset: 0xC4A89, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFySaySo19WKWebsiteDataRecordCGYbScMYccfU_', symObjAddr: 0x2060, symBinAddr: 0xE7D8, symSize: 0x424 }
  - { offset: 0xC4C7F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFySaySo19WKWebsiteDataRecordCGYbScMYccfU_yAGXEfU_yyYbcfU_', symObjAddr: 0x2484, symBinAddr: 0xEBFC, symSize: 0x14 }
  - { offset: 0xC4C9A, size: 0x8, addend: 0x0, symName: '_$sSaySo19WKWebsiteDataRecordCGIeghg_So7NSArrayCIeyBhy_TR', symObjAddr: 0x2498, symBinAddr: 0xEC10, symSize: 0x6C }
  - { offset: 0xC4CB2, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE8URIEvent_WZ', symObjAddr: 0x2504, symBinAddr: 0xEC7C, symSize: 0x34 }
  - { offset: 0xC4CCD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtF', symObjAddr: 0x2538, symBinAddr: 0xECB0, symSize: 0x3A0 }
  - { offset: 0xC4E28, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtFyyScMYccfU_', symObjAddr: 0x28D8, symBinAddr: 0xF050, symSize: 0x2CC }
  - { offset: 0xC4F4C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtFyyScMYccfU_yycfU_', symObjAddr: 0x2BA4, symBinAddr: 0xF31C, symSize: 0x16C }
  - { offset: 0xC50C8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CAA17SDKChecksProtocolA2aDP17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFTW', symObjAddr: 0x2D10, symBinAddr: 0xF488, symSize: 0x20 }
  - { offset: 0xC50E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtFyyScMYccfU_TA', symObjAddr: 0x2D5C, symBinAddr: 0xF4D4, symSize: 0xC }
  - { offset: 0xC50F8, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2D68, symBinAddr: 0xF4E0, symSize: 0x10 }
  - { offset: 0xC510C, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2D78, symBinAddr: 0xF4F0, symSize: 0x8 }
  - { offset: 0xC5120, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x2D80, symBinAddr: 0xF4F8, symSize: 0x48 }
  - { offset: 0xC5134, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x2E08, symBinAddr: 0xF540, symSize: 0x4C }
  - { offset: 0xC5148, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x2E54, symBinAddr: 0xF58C, symSize: 0x44 }
  - { offset: 0xC5214, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctF', symObjAddr: 0x2E98, symBinAddr: 0xF5D0, symSize: 0x3FC }
  - { offset: 0xC543C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x3294, symBinAddr: 0xF9CC, symSize: 0xA1C }
  - { offset: 0xC57D4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x3CB0, symBinAddr: 0x103E8, symSize: 0x348 }
  - { offset: 0xC5B10, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C17validationResults14sdkCheckPoints17continueToPaymentySayAA0fG0VG_SbtFyyScMYccfU_yycfU_TA', symObjAddr: 0x401C, symBinAddr: 0x10754, symSize: 0xC }
  - { offset: 0xC5B24, size: 0x8, addend: 0x0, symName: '_$sIeg_SgWOe', symObjAddr: 0x4028, symBinAddr: 0x10760, symSize: 0x10 }
  - { offset: 0xC5B38, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x404C, symBinAddr: 0x10784, symSize: 0x34 }
  - { offset: 0xC5B4C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_TA', symObjAddr: 0x40AC, symBinAddr: 0x107E4, symSize: 0xC }
  - { offset: 0xC5B60, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0x40B8, symBinAddr: 0x107F0, symSize: 0x34 }
  - { offset: 0xC5B74, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C12startPolling7withUrl0E15SuccessCallback0E7FailureySS_ySDys11AnyHashableVypGcyAJctFy10Foundation10URLRequestV_AK4DataVSo13NSURLResponseCSgtcfU_yyScMYccfU_TA', symObjAddr: 0x41CC, symBinAddr: 0x108C8, symSize: 0x40 }
  - { offset: 0xC5BC6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOSgWOe', symObjAddr: 0x420C, symBinAddr: 0x10908, symSize: 0x18 }
  - { offset: 0xC5BDA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOe', symObjAddr: 0x4224, symBinAddr: 0x10920, symSize: 0x74 }
  - { offset: 0xC5BEE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A14ResultProtocol_pSgXwWOh', symObjAddr: 0x4298, symBinAddr: 0x10994, symSize: 0x24 }
  - { offset: 0xC5C44, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C10publishURI4withySS_tFTf4nd_n', symObjAddr: 0x4820, symBinAddr: 0x10F1C, symSize: 0x1A0 }
  - { offset: 0xC5D57, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5clean33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyFTf4d_n', symObjAddr: 0x49C0, symBinAddr: 0x110BC, symSize: 0x1CC }
  - { offset: 0xC5D76, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x4BC4, symBinAddr: 0x112C0, symSize: 0x44 }
  - { offset: 0xC5D8A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOy', symObjAddr: 0x4C08, symBinAddr: 0x11304, symSize: 0x74 }
  - { offset: 0xC5D9E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC05fetchC0yyySDySSypGSgcFyAA0bC13FetchResponseO_AFtcfU_TA', symObjAddr: 0x4CA8, symBinAddr: 0x113A4, symSize: 0xC }
  - { offset: 0xC5DB2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFyyScMYccfU0_TA', symObjAddr: 0x4CB4, symBinAddr: 0x113B0, symSize: 0xC }
  - { offset: 0xC61B4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CACyc33_C2103DC0A7BEA85E64BF4B6378FF9B75Llfc', symObjAddr: 0x2C, symBinAddr: 0xC7A4, symSize: 0x1FC }
  - { offset: 0xC61EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CACyc33_C2103DC0A7BEA85E64BF4B6378FF9B75LlfcTo', symObjAddr: 0x228, symBinAddr: 0xC9A0, symSize: 0x20 }
  - { offset: 0xC624A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C14setupAnalytics33_C2103DC0A7BEA85E64BF4B6378FF9B75LLyyF', symObjAddr: 0x248, symBinAddr: 0xC9C0, symSize: 0x194 }
  - { offset: 0xC650E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtF', symObjAddr: 0x3DC, symBinAddr: 0xCB54, symSize: 0xE68 }
  - { offset: 0xC6921, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFySDySSypGSgcfU_', symObjAddr: 0x1380, symBinAddr: 0xDAF8, symSize: 0x14 }
  - { offset: 0xC695F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C21performInitialization33_C2103DC0A7BEA85E64BF4B6378FF9B75LL_17displayController26arrExternalPaymentEntities6isTestySDys11AnyHashableVypG_So06UIViewO0CSgSayAA06PluginR8Delegate_pGSgSbtFyyScMYccfU0_', symObjAddr: 0x1394, symBinAddr: 0xDB0C, symSize: 0x26C }
  - { offset: 0xC6A78, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C33invokeDelegateWithFailureAndClose33_C2103DC0A7BEA85E64BF4B6378FF9B75LL5errorySS_tF', symObjAddr: 0x1244, symBinAddr: 0xD9BC, symSize: 0x13C }
  - { offset: 0xC6BD4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C5closeyyF', symObjAddr: 0x162C, symBinAddr: 0xDDA4, symSize: 0x394 }
  - { offset: 0xC6D1E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C13initAnalytics33_C2103DC0A7BEA85E64BF4B6378FF9B75LL15withMerchantKeyySS_tF', symObjAddr: 0x19C0, symBinAddr: 0xE138, symSize: 0x198 }
  - { offset: 0xC6DB5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CfD', symObjAddr: 0x1B58, symBinAddr: 0xE2D0, symSize: 0x44 }
  - { offset: 0xC7009, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_11andDelegateACSS_AA0A8Protocol_ptFZTf4nnd_g', symObjAddr: 0x42BC, symBinAddr: 0x109B8, symSize: 0x198 }
  - { offset: 0xC7080, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4DataACSS_AA0a25PaymentCompletionProtocoldH0_ptFZTf4nnd_g', symObjAddr: 0x4454, symBinAddr: 0x10B50, symSize: 0x114 }
  - { offset: 0xC70FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTf4nnnd_g', symObjAddr: 0x4568, symBinAddr: 0x10C64, symSize: 0x11C }
  - { offset: 0xC719A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0C11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTf4nnnd_g', symObjAddr: 0x4684, symBinAddr: 0x10D80, symSize: 0x19C }
  - { offset: 0xC73E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x113E0, symSize: 0x18 }
  - { offset: 0xC74D8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyCMa', symObjAddr: 0xE8, symBinAddr: 0x1141C, symSize: 0x20 }
  - { offset: 0xC74EC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOwst', symObjAddr: 0x1A8, symBinAddr: 0x1143C, symSize: 0xBC }
  - { offset: 0xC7500, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOMa', symObjAddr: 0x27C, symBinAddr: 0x114F8, symSize: 0x10 }
  - { offset: 0xC7514, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSHAASQWb', symObjAddr: 0x28C, symBinAddr: 0x11508, symSize: 0x4 }
  - { offset: 0xC7528, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOAESQAAWl', symObjAddr: 0x290, symBinAddr: 0x1150C, symSize: 0x44 }
  - { offset: 0xC756A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyC5ScopeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x113E0, symSize: 0x18 }
  - { offset: 0xC75D5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17AnalyticsPropertyCfD', symObjAddr: 0xC4, symBinAddr: 0x113F8, symSize: 0x24 }
  - { offset: 0xC7AAB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCfETo', symObjAddr: 0x754, symBinAddr: 0x11BD4, symSize: 0x60 }
  - { offset: 0xC7ADA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCMa', symObjAddr: 0x7B4, symBinAddr: 0x11C34, symSize: 0x20 }
  - { offset: 0xC7AEE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOwet', symObjAddr: 0x7E4, symBinAddr: 0x11C54, symSize: 0x90 }
  - { offset: 0xC7B02, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOwst', symObjAddr: 0x874, symBinAddr: 0x11CE4, symSize: 0xBC }
  - { offset: 0xC7B16, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOMa', symObjAddr: 0x944, symBinAddr: 0x11DA0, symSize: 0x10 }
  - { offset: 0xC7B2A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOSHAASQWb', symObjAddr: 0x954, symBinAddr: 0x11DB0, symSize: 0x4 }
  - { offset: 0xC7B3E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay29ShieldInformationFetcherErrorOACSQAAWl', symObjAddr: 0x958, symBinAddr: 0x11DB4, symSize: 0x44 }
  - { offset: 0xC7C0D, size: 0x8, addend: 0x0, symName: '_$sSo10CLLocationCMa', symObjAddr: 0xE64, symBinAddr: 0x121BC, symSize: 0x3C }
  - { offset: 0xC7E23, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCACycfc', symObjAddr: 0xD0, symBinAddr: 0x11550, symSize: 0xF0 }
  - { offset: 0xC7E6A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCACycfcTo', symObjAddr: 0x1C0, symBinAddr: 0x11640, symSize: 0x20 }
  - { offset: 0xC7EE2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC13createPayload33_435586993CD454125951701030FBBDD3LLSSSg_AA0bcD5ErrorOSgtyF', symObjAddr: 0x1E0, symBinAddr: 0x11660, symSize: 0x37C }
  - { offset: 0xC8072, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC20fetchCurrentLocation33_435586993CD454125951701030FBBDD3LLAA0bcD5ErrorOSgyF', symObjAddr: 0x55C, symBinAddr: 0x119DC, symSize: 0xDC }
  - { offset: 0xC80F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtFTo', symObjAddr: 0x638, symBinAddr: 0x11AB8, symSize: 0x78 }
  - { offset: 0xC8129, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_16didFailWithErrorySo010CLLocationF0C_s0J0_ptFTo', symObjAddr: 0x6B0, symBinAddr: 0x11B30, symSize: 0x64 }
  - { offset: 0xC8153, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherCfD', symObjAddr: 0x714, symBinAddr: 0x11B94, symSize: 0x40 }
  - { offset: 0xC82AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtFTf4dnn_n', symObjAddr: 0xA90, symBinAddr: 0x11DF8, symSize: 0x2F8 }
  - { offset: 0xC8617, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24ShieldInformationFetcherC15locationManager_16didFailWithErrorySo010CLLocationF0C_s0J0_ptFTf4ddn_n', symObjAddr: 0xD88, symBinAddr: 0x120F0, symSize: 0xCC }
  - { offset: 0xC8A82, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerCMa', symObjAddr: 0xC30, symBinAddr: 0x12E28, symSize: 0x20 }
  - { offset: 0xC8A96, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyFyyScMYccfU_TA', symObjAddr: 0xC88, symBinAddr: 0x12E48, symSize: 0x8 }
  - { offset: 0xC8AAA, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xC90, symBinAddr: 0x12E50, symSize: 0x10 }
  - { offset: 0xC8ABE, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xCA0, symBinAddr: 0x12E60, symSize: 0x8 }
  - { offset: 0xC8BFF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC17displayController10dataSource9isTestingACSo06UIViewE0CSg_AA0A13CheckoutModelCSbtcfc', symObjAddr: 0x0, symBinAddr: 0x121F8, symSize: 0x2BC }
  - { offset: 0xC8D7C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC15displayCheckoutyyF', symObjAddr: 0x2BC, symBinAddr: 0x124B4, symSize: 0x2F0 }
  - { offset: 0xC8FFE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyF', symObjAddr: 0x5AC, symBinAddr: 0x127A4, symSize: 0x1B8 }
  - { offset: 0xC904C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC24removeCheckoutControlleryyFyyScMYccfU_', symObjAddr: 0x764, symBinAddr: 0x1295C, symSize: 0x64 }
  - { offset: 0xC9111, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC030dismissControllerUsingRootViewE033_5FADD501D99B794A6E8B315B74C25A57LLyyF', symObjAddr: 0x7C8, symBinAddr: 0x129C0, symSize: 0x43C }
  - { offset: 0xC9377, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerCfD', symObjAddr: 0xC04, symBinAddr: 0x12DFC, symSize: 0x2C }
  - { offset: 0xC93B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC37displayCheckoutUsingDisplayController33_5FADD501D99B794A6E8B315B74C25A57LL0dH0011checkoutNavH00R2VCySo06UIViewH0C_So012UINavigationH0CAA0aeT0CtFTf4nndd_n', symObjAddr: 0xDC0, symBinAddr: 0x12E68, symSize: 0x160 }
  - { offset: 0xC9429, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC38displayCheckoutUsingRootViewController33_5FADD501D99B794A6E8B315B74C25A57LL011checkoutNavI00S2VCySo012UINavigationI0C_AA0aeU0CtFTf4ndn_n', symObjAddr: 0xF20, symBinAddr: 0x12FC8, symSize: 0x308 }
  - { offset: 0xC9504, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17NavigationManagerC029dismissControllerUsingDisplayE033_5FADD501D99B794A6E8B315B74C25A57LL07displayE0ySo06UIViewE0C_tFTf4nd_n', symObjAddr: 0x1228, symBinAddr: 0x132D0, symSize: 0x1EC }
  - { offset: 0xC96D8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11merchantKeySSvg', symObjAddr: 0x0, symBinAddr: 0x134BC, symSize: 0x38 }
  - { offset: 0xC97F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCMa', symObjAddr: 0x1C8, symBinAddr: 0x13664, symSize: 0x20 }
  - { offset: 0xC9807, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCfETo', symObjAddr: 0x1E8, symBinAddr: 0x13684, symSize: 0x4C }
  - { offset: 0xC984B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11merchantKeySSvg', symObjAddr: 0x0, symBinAddr: 0x134BC, symSize: 0x38 }
  - { offset: 0xC9870, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC04dictC11InformationSDys11AnyHashableVypGvg', symObjAddr: 0x38, symBinAddr: 0x134F4, symSize: 0x10 }
  - { offset: 0xC9893, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC8delegateAA0bC18CompletionDelegate_pvg', symObjAddr: 0x48, symBinAddr: 0x13504, symSize: 0x30 }
  - { offset: 0xC98B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC14getMerchantKeySSyF', symObjAddr: 0x78, symBinAddr: 0x13534, symSize: 0x48 }
  - { offset: 0xC98DD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC03getC8InfoDictSDys11AnyHashableVypGyF', symObjAddr: 0xC0, symBinAddr: 0x1357C, symSize: 0x20 }
  - { offset: 0xC9900, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelC11getDelegateAA0bc10CompletionF0_pyF', symObjAddr: 0xE0, symBinAddr: 0x1359C, symSize: 0x40 }
  - { offset: 0xC9931, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCACycfc', symObjAddr: 0x140, symBinAddr: 0x135DC, symSize: 0x2C }
  - { offset: 0xC998F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCACycfcTo', symObjAddr: 0x16C, symBinAddr: 0x13608, symSize: 0x2C }
  - { offset: 0xC99EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18PluginPaymentModelCfD', symObjAddr: 0x198, symBinAddr: 0x13634, symSize: 0x30 }
  - { offset: 0xC9B82, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelVMa', symObjAddr: 0x0, symBinAddr: 0x13730, symSize: 0x10 }
  - { offset: 0xC9B9A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelVMa', symObjAddr: 0x0, symBinAddr: 0x13730, symSize: 0x10 }
  - { offset: 0xC9C15, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x5D0, symBinAddr: 0x13C5C, symSize: 0x48 }
  - { offset: 0xC9CA8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelV03addB04name8observer8selector6objectSbSS_yp10ObjectiveC8SelectorVypSgtF', symObjAddr: 0x10, symBinAddr: 0x13740, symSize: 0x2AC }
  - { offset: 0xC9D3C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13ObserverModelV06removeB04name8observer6objectSbSS_ypypSgtF', symObjAddr: 0x2BC, symBinAddr: 0x139EC, symSize: 0x270 }
  - { offset: 0xC9F6D, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE19reachabilityChangedABvpZ', symObjAddr: 0x9FF8, symBinAddr: 0xBA1D8, symSize: 0x0 }
  - { offset: 0xCA0FA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC13startNotifieryyKF', symObjAddr: 0x0, symBinAddr: 0x13CA4, symSize: 0x338 }
  - { offset: 0xCA202, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea8RazorpayE19reachabilityChanged_WZ', symObjAddr: 0x348, symBinAddr: 0x13FDC, symSize: 0x34 }
  - { offset: 0xCA2AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyF', symObjAddr: 0x37C, symBinAddr: 0x14010, symSize: 0x348 }
  - { offset: 0xCA37A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyFyyScMYccfU_', symObjAddr: 0x1198, symBinAddr: 0x14BF4, symSize: 0x9C }
  - { offset: 0xCA46F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCMa', symObjAddr: 0xD94, symBinAddr: 0x14968, symSize: 0x20 }
  - { offset: 0xCA483, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOwst', symObjAddr: 0xE54, symBinAddr: 0x14988, symSize: 0xBC }
  - { offset: 0xCA497, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOMa', symObjAddr: 0xF24, symBinAddr: 0x14A44, symSize: 0x10 }
  - { offset: 0xCA4AB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOSHAASQWb', symObjAddr: 0xF34, symBinAddr: 0x14A54, symSize: 0x4 }
  - { offset: 0xCA4BF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOAESQAAWl', symObjAddr: 0xF38, symBinAddr: 0x14A58, symSize: 0x44 }
  - { offset: 0xCA4D3, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0xF7C, symBinAddr: 0x14A9C, symSize: 0x3C }
  - { offset: 0xCA53B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay8callback12reachability5flags4infoySo24SCNetworkReachabilityRefa_So0fG5FlagsVSvSgtFTo', symObjAddr: 0x103C, symBinAddr: 0x14AD8, symSize: 0x6C }
  - { offset: 0xCA5B2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC13startNotifieryyKFyyYbcfU_TA', symObjAddr: 0x10A8, symBinAddr: 0x14B44, symSize: 0x14 }
  - { offset: 0xCA5CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC13startNotifieryyKFyyYbcfU_TA', symObjAddr: 0x10A8, symBinAddr: 0x14B44, symSize: 0x14 }
  - { offset: 0xCA5E4, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x10BC, symBinAddr: 0x14B58, symSize: 0x10 }
  - { offset: 0xCA5F8, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x10CC, symBinAddr: 0x14B68, symSize: 0x8 }
  - { offset: 0xCA60C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOACs0C0AAWl', symObjAddr: 0x1154, symBinAddr: 0x14BB0, symSize: 0x44 }
  - { offset: 0xCA631, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCIegg_SgWOy', symObjAddr: 0x1234, symBinAddr: 0x14C90, symSize: 0x10 }
  - { offset: 0xCA645, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC19reachabilityChanged33_DADB470212B16C113D34EDAFA5D2D206LLyyFyyScMYccfU_TA', symObjAddr: 0x1278, symBinAddr: 0x14CD4, symSize: 0xC }
  - { offset: 0xCA659, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOWOy', symObjAddr: 0x1298, symBinAddr: 0x14CE4, symSize: 0x18 }
  - { offset: 0xCA66D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwxx', symObjAddr: 0x12B0, symBinAddr: 0x14CFC, symSize: 0x10 }
  - { offset: 0xCA681, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOWOe', symObjAddr: 0x12C0, symBinAddr: 0x14D0C, symSize: 0x18 }
  - { offset: 0xCA695, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwca', symObjAddr: 0x1324, symBinAddr: 0x14D6C, symSize: 0x54 }
  - { offset: 0xCA6A9, size: 0x8, addend: 0x0, symName: ___swift_memcpy17_8, symObjAddr: 0x1378, symBinAddr: 0x14DC0, symSize: 0x14 }
  - { offset: 0xCA6BD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwta', symObjAddr: 0x138C, symBinAddr: 0x14DD4, symSize: 0x44 }
  - { offset: 0xCA6D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwet', symObjAddr: 0x13D0, symBinAddr: 0x14E18, symSize: 0x48 }
  - { offset: 0xCA6E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwst', symObjAddr: 0x1418, symBinAddr: 0x14E60, symSize: 0x44 }
  - { offset: 0xCA6F9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwug', symObjAddr: 0x145C, symBinAddr: 0x14EA4, symSize: 0x18 }
  - { offset: 0xCA70D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOwui', symObjAddr: 0x1478, symBinAddr: 0x14EBC, symSize: 0x18 }
  - { offset: 0xCA721, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17ReachabilityErrorOMa', symObjAddr: 0x1490, symBinAddr: 0x14ED4, symSize: 0x10 }
  - { offset: 0xCA8D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10ConnectionOs23CustomStringConvertibleAAsAFP11descriptionSSvgTW', symObjAddr: 0x784, symBinAddr: 0x14358, symSize: 0x98 }
  - { offset: 0xCA8FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC10connectionAC10ConnectionOvg', symObjAddr: 0x81C, symBinAddr: 0x143F0, symSize: 0x288 }
  - { offset: 0xCAB3F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityC15reachabilityRefACSo09SCNetworkbD0a_tcfc', symObjAddr: 0xAA4, symBinAddr: 0x14678, symSize: 0x254 }
  - { offset: 0xCABF2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12ReachabilityCfD', symObjAddr: 0xCF8, symBinAddr: 0x148CC, symSize: 0x9C }
  - { offset: 0xCB17E, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x4E00, symBinAddr: 0x19CEC, symSize: 0xC4 }
  - { offset: 0xCB51D, size: 0x8, addend: 0x0, symName: '_$sSlsE6suffix4from11SubSequenceQz5IndexQz_tFSS_Tg5Tf4ng_n', symObjAddr: 0xA590, symBinAddr: 0x1F338, symSize: 0x4C }
  - { offset: 0xCB8B1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14isRetryEnabled33_C52A9CB3C4396E63C4FE51721B47A244LL2inSbSDys11AnyHashableVypG_tF', symObjAddr: 0x0, symBinAddr: 0x14EEC, symSize: 0x268 }
  - { offset: 0xCBBAC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC6onLoad33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x268, symBinAddr: 0x15154, symSize: 0xB24 }
  - { offset: 0xCC369, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12onCompletion33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0xD8C, symBinAddr: 0x15C78, symSize: 0x108 }
  - { offset: 0xCC4D4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7onFault33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0xE94, symBinAddr: 0x15D80, symSize: 0xC30 }
  - { offset: 0xCCAE8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC9onDismiss33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x1AC4, symBinAddr: 0x169B0, symSize: 0x80C }
  - { offset: 0xCCFEC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x22D0, symBinAddr: 0x171BC, symSize: 0x102C }
  - { offset: 0xCD731, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tFyyScMYccfU_', symObjAddr: 0x6FBC, symBinAddr: 0x1BEA8, symSize: 0x7C }
  - { offset: 0xCD793, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC9onSuccess33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x32FC, symBinAddr: 0x181E8, symSize: 0x840 }
  - { offset: 0xCDB15, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC33modifyActivityIndicatorAppearance33_C52A9CB3C4396E63C4FE51721B47A244LL13withColorDictySDySS12CoreGraphics7CGFloatVG_tF', symObjAddr: 0x3B3C, symBinAddr: 0x18A28, symSize: 0x244 }
  - { offset: 0xCDD83, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tF', symObjAddr: 0x3D80, symBinAddr: 0x18C6C, symSize: 0x354 }
  - { offset: 0xCDFC9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tFySSSg_AA29ShieldInformationFetcherErrorOSgtcfU_Tf4ndnn_n', symObjAddr: 0xA454, symBinAddr: 0x1F1FC, symSize: 0x13C }
  - { offset: 0xCE151, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18downloadChallanPdf33_C52A9CB3C4396E63C4FE51721B47A244LL4form11andFileNameySS_SStF', symObjAddr: 0x40D4, symBinAddr: 0x18FC0, symSize: 0x5C0 }
  - { offset: 0xCE4AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12downloadFile33_C52A9CB3C4396E63C4FE51721B47A244LL4form03andE4Name0P9ExtensionySS_S2StF', symObjAddr: 0x4694, symBinAddr: 0x19580, symSize: 0x2CC }
  - { offset: 0xCE615, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tF', symObjAddr: 0x4960, symBinAddr: 0x1984C, symSize: 0x438 }
  - { offset: 0xCE75F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tFyypSg_AItcfU_', symObjAddr: 0x72D4, symBinAddr: 0x1C184, symSize: 0x81C }
  - { offset: 0xCEC89, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21userContentController_10didReceiveySo06WKUsereF0C_So15WKScriptMessageCtFTo', symObjAddr: 0x4D98, symBinAddr: 0x19C84, symSize: 0x68 }
  - { offset: 0xCED75, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtF', symObjAddr: 0x4EC4, symBinAddr: 0x19DB0, symSize: 0x644 }
  - { offset: 0xCEE68, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU_', symObjAddr: 0x7038, symBinAddr: 0x1BF24, symSize: 0x90 }
  - { offset: 0xCEEBE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU2_', symObjAddr: 0x71C0, symBinAddr: 0x1C0AC, symSize: 0xC4 }
  - { offset: 0xCEF37, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18saveDocumentAsFile33_C52A9CB3C4396E63C4FE51721B47A244LL4name13fileExtension19base64EncodedStringySS_SS10Foundation4DataVtF', symObjAddr: 0x5508, symBinAddr: 0x1A3F4, symSize: 0x484 }
  - { offset: 0xCF295, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFTo', symObjAddr: 0x598C, symBinAddr: 0x1A878, symSize: 0x68 }
  - { offset: 0xCF391, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadFormyyF', symObjAddr: 0x59F4, symBinAddr: 0x1A8E0, symSize: 0x1240 }
  - { offset: 0xCFDDC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31saveAppropriateEventToAnalytics4dict16eventWithoutData0j4WithL0ySDys11AnyHashableVypG_AA0hF0OALtF', symObjAddr: 0x6C34, symBinAddr: 0x1BB20, symSize: 0x12C }
  - { offset: 0xCFEF7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16isExternalWallet33_C52A9CB3C4396E63C4FE51721B47A244LL6walletSbSS_tF', symObjAddr: 0x6D60, symBinAddr: 0x1BC4C, symSize: 0x25C }
  - { offset: 0xCFFF2, size: 0x8, addend: 0x0, symName: '_$sSo21PHAuthorizationStatusVIegy_ABIeyBy_TR', symObjAddr: 0x70C8, symBinAddr: 0x1BFB4, symSize: 0x3C }
  - { offset: 0xD000A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14openURISchemes4withySS_tFySbYbScMYccfU_', symObjAddr: 0x7284, symBinAddr: 0x1C170, symSize: 0x14 }
  - { offset: 0xD022D, size: 0x8, addend: 0x0, symName: '_$sSlsE5split9maxSplits25omittingEmptySubsequences14whereSeparatorSay11SubSequenceQzGSi_S2b7ElementQzKXEtKFSS_Tg5', symObjAddr: 0x7AF0, symBinAddr: 0x1C9A0, symSize: 0x418 }
  - { offset: 0xD05AD, size: 0x8, addend: 0x0, symName: '_$sSlsE5split9maxSplits25omittingEmptySubsequences14whereSeparatorSay11SubSequenceQzGSi_S2b7ElementQzKXEtKF17appendSubsequenceL_3endSb5IndexQz_tSlRzlFSS_Tg5', symObjAddr: 0x7F08, symBinAddr: 0x1CDB8, symSize: 0x10C }
  - { offset: 0xD0719, size: 0x8, addend: 0x0, symName: '_$sSlsSQ7ElementRpzrlE5split9separator9maxSplits25omittingEmptySubsequencesSay11SubSequenceQzGAB_SiSbtFSbABXEfU_SS_TG5', symObjAddr: 0x8014, symBinAddr: 0x1CEC4, symSize: 0x54 }
  - { offset: 0xD07F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17getSelectedObject4with9shortcodeSDyS2SGSgSayAGG_SStFTf4nnd_n', symObjAddr: 0x8068, symBinAddr: 0x1CF18, symSize: 0x15C }
  - { offset: 0xD0989, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12getUriScheme3for6andUrlSSSgSDyS2SG_SStFTf4nnd_n', symObjAddr: 0x81C4, symBinAddr: 0x1D074, symSize: 0x13C }
  - { offset: 0xD0A41, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14openURISchemes4withySS_tFTf4nd_n', symObjAddr: 0x8300, symBinAddr: 0x1D1B0, symSize: 0x220 }
  - { offset: 0xD0B6D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16callNativeIntent33_C52A9CB3C4396E63C4FE51721B47A244LL4dictySDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x8520, symBinAddr: 0x1D3D0, symSize: 0x78C }
  - { offset: 0xD1045, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16isRetryAvailable33_C52A9CB3C4396E63C4FE51721B47A244LL2inSDys11AnyHashableVypGSgAI_tFTf4nd_n', symObjAddr: 0x8CAC, symBinAddr: 0x1DB5C, symSize: 0x118 }
  - { offset: 0xD10EF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19getAvailableUriDataSaySDyS2SGGyFTf4d_n', symObjAddr: 0x8DC4, symBinAddr: 0x1DC74, symSize: 0x6D8 }
  - { offset: 0xD15B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19getAvailableAppListSaySDyS2SGGyFTf4d_n', symObjAddr: 0x949C, symBinAddr: 0x1E34C, symSize: 0x834 }
  - { offset: 0xD1B7D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22retrieveContactDetailsSDys11AnyHashableVypGyFTf4d_n', symObjAddr: 0x9CD0, symBinAddr: 0x1EB80, symSize: 0x170 }
  - { offset: 0xD1CA1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18saveContactDetails33_C52A9CB3C4396E63C4FE51721B47A244LLyySDyS2SGFTf4nd_n', symObjAddr: 0x9F94, symBinAddr: 0x1ED3C, symSize: 0x368 }
  - { offset: 0xD1DF8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12sendErrorObj33_C52A9CB3C4396E63C4FE51721B47A244LL8withData07messageF0SDys11AnyHashableVypGSgAJ_AJtFTf4nnd_n', symObjAddr: 0xA2FC, symBinAddr: 0x1F0A4, symSize: 0x158 }
  - { offset: 0xD1F43, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21userContentController_10didReceiveySo06WKUsereF0C_So15WKScriptMessageCtFTf4dnn_n', symObjAddr: 0xA5DC, symBinAddr: 0x1F384, symSize: 0x2500 }
  - { offset: 0xD2BA3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFTf4dndn_n', symObjAddr: 0xCADC, symBinAddr: 0x21884, symSize: 0x41C }
  - { offset: 0xD2D0A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5image33_C52A9CB3C4396E63C4FE51721B47A244LL_24didFinishSavingWithError11contextInfoySo7UIImageC_s0R0_pSgSVtFyyScMYccfU_TA', symObjAddr: 0xCF58, symBinAddr: 0x21CC4, symSize: 0x28 }
  - { offset: 0xD2D3C, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xCF80, symBinAddr: 0x21CEC, symSize: 0x10 }
  - { offset: 0xD2D50, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xCF90, symBinAddr: 0x21CFC, symSize: 0x8 }
  - { offset: 0xD2D64, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xD0E4, symBinAddr: 0x21D04, symSize: 0x3C }
  - { offset: 0xD2D78, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8onSubmit33_C52A9CB3C4396E63C4FE51721B47A244LL8withDataySDys11AnyHashableVypG_tFyyScMYccfU_TA', symObjAddr: 0xD1CC, symBinAddr: 0x21D94, symSize: 0x8 }
  - { offset: 0xD2D8C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC31sendExtraAnalyticsDataViaBridge33_C52A9CB3C4396E63C4FE51721B47A244LL7webViewySo05WKWebT0CSg_tFySSSg_AA29ShieldInformationFetcherErrorOSgtcfU_TA', symObjAddr: 0xD1FC, symBinAddr: 0x21DC4, symSize: 0x8 }
  - { offset: 0xD2DB5, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_0, symObjAddr: 0xD250, symBinAddr: 0x21DCC, symSize: 0x3C }
  - { offset: 0xD2DC9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU2_TA', symObjAddr: 0xD30C, symBinAddr: 0x21E44, symSize: 0x14 }
  - { offset: 0xD2DDD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC32handlePhotoAuthorizationResponse33_C52A9CB3C4396E63C4FE51721B47A244LL_5image4name13fileExtension4dataySo21PHAuthorizationStatusV_So7UIImageCS2S10Foundation4DataVtFyyScMYccfU_TA', symObjAddr: 0xD3E8, symBinAddr: 0x21F20, symSize: 0x8 }
  - { offset: 0xD2DF1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC22handleExternalSDKEvent8withDataySDys11AnyHashableVypG_tFyypSg_AItcfU_TA', symObjAddr: 0xD41C, symBinAddr: 0x21F54, symSize: 0xC }
  - { offset: 0xD37F1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5frame13configurationACSo6CGRectV_So05WKWebD13ConfigurationCtcfcTo', symObjAddr: 0x0, symBinAddr: 0x21F90, symSize: 0x84 }
  - { offset: 0xD38B9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewCMa', symObjAddr: 0x2DC, symBinAddr: 0x2226C, symSize: 0x20 }
  - { offset: 0xD38CD, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOh', symObjAddr: 0x33C, symBinAddr: 0x2228C, symSize: 0x40 }
  - { offset: 0xD3965, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5frame13configurationACSo6CGRectV_So05WKWebD13ConfigurationCtcfcTo', symObjAddr: 0x0, symBinAddr: 0x21F90, symSize: 0x84 }
  - { offset: 0xD39FF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x84, symBinAddr: 0x22014, symSize: 0x54 }
  - { offset: 0xD3A40, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewC7loadUrl10withStringySS_tF', symObjAddr: 0xD8, symBinAddr: 0x22068, symSize: 0x1C4 }
  - { offset: 0xD3AA3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A15CheckoutWebViewCfD', symObjAddr: 0x29C, symBinAddr: 0x2222C, symSize: 0x40 }
  - { offset: 0xD40DE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCfETo', symObjAddr: 0x147C, symBinAddr: 0x23748, symSize: 0x94 }
  - { offset: 0xD410D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCMa', symObjAddr: 0x1510, symBinAddr: 0x237DC, symSize: 0x20 }
  - { offset: 0xD412C, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOb', symObjAddr: 0x1AFC, symBinAddr: 0x23D30, symSize: 0x48 }
  - { offset: 0xD436F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC17activityIndicatorSo010UIActivityE4ViewCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x222CC, symSize: 0x20 }
  - { offset: 0xD43CF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC17activityIndicatorSo010UIActivityE4ViewCSgvsTo', symObjAddr: 0x20, symBinAddr: 0x222EC, symSize: 0x14 }
  - { offset: 0xD4524, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC11viewDidLoadyyF', symObjAddr: 0x34, symBinAddr: 0x22300, symSize: 0x280 }
  - { offset: 0xD4722, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC11viewDidLoadyyFTo', symObjAddr: 0x2B4, symBinAddr: 0x22580, symSize: 0x28 }
  - { offset: 0xD4779, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC13viewDidAppearyySbF', symObjAddr: 0x2DC, symBinAddr: 0x225A8, symSize: 0x2F0 }
  - { offset: 0xD493E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC13viewDidAppearyySbFTo', symObjAddr: 0x5CC, symBinAddr: 0x22898, symSize: 0x30 }
  - { offset: 0xD4966, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC18addUiViewAsSubviewyyF', symObjAddr: 0x5FC, symBinAddr: 0x228C8, symSize: 0x10C }
  - { offset: 0xD4A05, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC15dismissKeyboardyyFTo', symObjAddr: 0x708, symBinAddr: 0x229D4, symSize: 0x6C }
  - { offset: 0xD4AE6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC10initialize13storefrontUrl9itemsData12withDelegateySS_SSAA20MagicXResultProtocol_ptF', symObjAddr: 0x774, symBinAddr: 0x22A40, symSize: 0x15C }
  - { offset: 0xD4CBB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC19addWebViewAsSubview33_08CDC63A7B054143135D8C60DBB70CFALL03webF0ySo05WKWebF0C_tF', symObjAddr: 0x8D0, symBinAddr: 0x22B9C, symSize: 0x698 }
  - { offset: 0xD4ED5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_29didStartProvisionalNavigationySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0xF68, symBinAddr: 0x23234, symSize: 0x68 }
  - { offset: 0xD4F06, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0xFD0, symBinAddr: 0x2329C, symSize: 0x68 }
  - { offset: 0xD4F78, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC16handleBackActionyyypF', symObjAddr: 0x1038, symBinAddr: 0x23304, symSize: 0x110 }
  - { offset: 0xD5028, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC16handleBackActionyyypFTo', symObjAddr: 0x1148, symBinAddr: 0x23414, symSize: 0x64 }
  - { offset: 0xD503C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x11AC, symBinAddr: 0x23478, symSize: 0x12C }
  - { offset: 0xD507D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x12D8, symBinAddr: 0x235A4, symSize: 0x60 }
  - { offset: 0xD50C5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x1338, symBinAddr: 0x23604, symSize: 0xEC }
  - { offset: 0xD50F8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x1424, symBinAddr: 0x236F0, symSize: 0x28 }
  - { offset: 0xD510C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCCfD', symObjAddr: 0x144C, symBinAddr: 0x23718, symSize: 0x30 }
  - { offset: 0xD512F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_29didStartProvisionalNavigationySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x1588, symBinAddr: 0x237FC, symSize: 0x1DC }
  - { offset: 0xD51D4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8MagicxVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x1764, symBinAddr: 0x239D8, symSize: 0x358 }
  - { offset: 0xD542E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC28isIntegrationCheckInitilizedSbvpZ', symObjAddr: 0x151E2, symBinAddr: 0xBA1E0, symSize: 0x0 }
  - { offset: 0xD543C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay08InternalA0CAA17SDKChecksProtocolA2aDP03logC4Data14sdkCheckPointsySayAA0hI0VG_tFTW', symObjAddr: 0x20, symBinAddr: 0x23D78, symSize: 0x20 }
  - { offset: 0xD55AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocolPAAE03logB4Data14sdkCheckPointsySayAA0gH0VG_tFAA08InternalA0C_Tg5', symObjAddr: 0x40, symBinAddr: 0x23D98, symSize: 0x6B4 }
  - { offset: 0xD5B93, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocolPAAE03logB4Data14sdkCheckPointsySayAA0gH0VG_tFAA28OpinionatedChecksInitializerV_Tg5', symObjAddr: 0x6F4, symBinAddr: 0x2444C, symSize: 0x6AC }
  - { offset: 0xD618B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC18validateSDKVersion10sdkVersionySSSg_tF', symObjAddr: 0xDA0, symBinAddr: 0x24AF8, symSize: 0x2B0 }
  - { offset: 0xD6288, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC18validateSDKVersion10sdkVersionySSSg_tFySo13NSURLResponseCSgcfU_', symObjAddr: 0x2274, symBinAddr: 0x25F9C, symSize: 0x49C }
  - { offset: 0xD65D7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerCMa', symObjAddr: 0x11DC, symBinAddr: 0x24F34, symSize: 0x20 }
  - { offset: 0xD65EB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwxx', symObjAddr: 0x122C, symBinAddr: 0x24F54, symSize: 0x30 }
  - { offset: 0xD65FF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwcp', symObjAddr: 0x125C, symBinAddr: 0x24F84, symSize: 0x5C }
  - { offset: 0xD6613, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwca', symObjAddr: 0x12B8, symBinAddr: 0x24FE0, symSize: 0x94 }
  - { offset: 0xD6627, size: 0x8, addend: 0x0, symName: ___swift_memcpy56_8, symObjAddr: 0x134C, symBinAddr: 0x25074, symSize: 0x1C }
  - { offset: 0xD663B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwta', symObjAddr: 0x1368, symBinAddr: 0x25090, symSize: 0x5C }
  - { offset: 0xD664F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwet', symObjAddr: 0x13C4, symBinAddr: 0x250EC, symSize: 0x48 }
  - { offset: 0xD6663, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVwst', symObjAddr: 0x140C, symBinAddr: 0x25134, symSize: 0x4C }
  - { offset: 0xD6677, size: 0x8, addend: 0x0, symName: '_$s8Razorpay11CheckPointsVMa', symObjAddr: 0x1458, symBinAddr: 0x25180, symSize: 0x10 }
  - { offset: 0xD668B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMajorVersion02sdG0ySS_tF', symObjAddr: 0x1468, symBinAddr: 0x25190, symSize: 0x264 }
  - { offset: 0xD6771, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMajorVersion02sdG0ySS_tFySo13NSURLResponseCSgcfU_', symObjAddr: 0x16CC, symBinAddr: 0x253F4, symSize: 0x4A8 }
  - { offset: 0xD6A2F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMinorVersion03sdkG0ySS_tF', symObjAddr: 0x1B74, symBinAddr: 0x2589C, symSize: 0x264 }
  - { offset: 0xD6B15, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMinorVersion03sdkG0ySS_tFySo13NSURLResponseCSgcfU_', symObjAddr: 0x1DD8, symBinAddr: 0x25B00, symSize: 0x49C }
  - { offset: 0xD6DD3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVAA17SDKChecksProtocolA2aDP03logE4Data14sdkCheckPointsySayAA0jK0VG_tFTW', symObjAddr: 0x2710, symBinAddr: 0x26438, symSize: 0x8 }
  - { offset: 0xD6E47, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0x2758, symBinAddr: 0x26440, symSize: 0x32C }
  - { offset: 0xD6F5B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFs11AnyHashableV_ypTg5', symObjAddr: 0x2A84, symBinAddr: 0x2676C, symSize: 0x328 }
  - { offset: 0xD70B6, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0x2DAC, symBinAddr: 0x26A94, symSize: 0x32C }
  - { offset: 0xD71A0, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF8Razorpay11CheckPointsV_Tg5', symObjAddr: 0x30D8, symBinAddr: 0x26DC0, symSize: 0x14 }
  - { offset: 0xD7216, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsE_5radixxSgqd___SitcSyRd__lufcADSRys5UInt8VGXEfU_Si_SsTG5', symObjAddr: 0x30EC, symBinAddr: 0x26DD4, symSize: 0x27C }
  - { offset: 0xD734D, size: 0x8, addend: 0x0, symName: '_$ss5SliceV32withContiguousStorageIfAvailableyqd__Sgqd__SRy7ElementQzGKXEKlFqd__AGKXEfU_SS8UTF8ViewV_SiSgTG5', symObjAddr: 0x3368, symBinAddr: 0x27050, symSize: 0x44 }
  - { offset: 0xD7457, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC16getVersionUrlFor33_3C86DF60C77CDB1157F770B0FCB4331CLL03sdkF011versionTypeSSSgSS_AA0f11VallidationR0AELLOtFTf4nnd_n', symObjAddr: 0x3468, symBinAddr: 0x27094, symSize: 0x590 }
  - { offset: 0xD7AE5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC18validateSDKVersion10sdkVersionySSSg_tFySo13NSURLResponseCSgcfU_TA', symObjAddr: 0x3A38, symBinAddr: 0x27628, symSize: 0xC }
  - { offset: 0xD7AF9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocol_pSgWOc', symObjAddr: 0x3A44, symBinAddr: 0x27634, symSize: 0x48 }
  - { offset: 0xD7B0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocol_pWOc', symObjAddr: 0x3A8C, symBinAddr: 0x2767C, symSize: 0x44 }
  - { offset: 0xD7B21, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMinorVersion03sdkG0ySS_tFySo13NSURLResponseCSgcfU_TA', symObjAddr: 0x3B18, symBinAddr: 0x276C4, symSize: 0xC }
  - { offset: 0xD7B35, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC20validateMajorVersion02sdG0ySS_tFySo13NSURLResponseCSgcfU_TA', symObjAddr: 0x3B54, symBinAddr: 0x27700, symSize: 0xC }
  - { offset: 0xD7B49, size: 0x8, addend: 0x0, symName: '_$sSlsSQ7ElementRpzrlE5split9separator9maxSplits25omittingEmptySubsequencesSay11SubSequenceQzGAB_SiSbtFSbABXEfU_SS_TG5TA', symObjAddr: 0x3B60, symBinAddr: 0x2770C, symSize: 0x1C }
  - { offset: 0xD7B5D, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsE_5radixxSgqd___SitcSyRd__lufcADSRys5UInt8VGXEfU_Si_SsTG5TA', symObjAddr: 0x3B7C, symBinAddr: 0x27728, symSize: 0x18 }
  - { offset: 0xD7B71, size: 0x8, addend: 0x0, symName: '_$ss5SliceV32withContiguousStorageIfAvailableyqd__Sgqd__SRy7ElementQzGKXEKlFqd__AGKXEfU_SS8UTF8ViewV_SiSgTG5TA', symObjAddr: 0x3B94, symBinAddr: 0x27740, symSize: 0x1C }
  - { offset: 0xD7B9B, size: 0x8, addend: 0x0, symName: '_$ss5SliceV32withContiguousStorageIfAvailableyqd__Sgqd__SRy7ElementQzGKXEKlFqd__AGKXEfU_SS8UTF8ViewV_SiSgTg5Tf4xnn_n', symObjAddr: 0x3BB0, symBinAddr: 0x2775C, symSize: 0x1EC }
  - { offset: 0xD7FC8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC27getMinimumDeploymentVersionyyF', symObjAddr: 0x1050, symBinAddr: 0x24DA8, symSize: 0x140 }
  - { offset: 0xD8146, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerCfD', symObjAddr: 0x1190, symBinAddr: 0x24EE8, symSize: 0x4C }
  - { offset: 0xD83E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsCMa', symObjAddr: 0x10, symBinAddr: 0x27970, symSize: 0x20 }
  - { offset: 0xD84AA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC04doesB18ExistAtDefaultPathySbSSFZTf4nd_n', symObjAddr: 0x30, symBinAddr: 0x27990, symSize: 0x1D4 }
  - { offset: 0xD8609, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC07writeToB13AtDefaultPath_7contentySS_SStFZTf4nnd_n', symObjAddr: 0x204, symBinAddr: 0x27B64, symSize: 0x808 }
  - { offset: 0xD8949, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC08readFromB13AtDefaultPathyS2SFZTf4nd_n', symObjAddr: 0xAA8, symBinAddr: 0x2836C, symSize: 0x364 }
  - { offset: 0xD8A78, size: 0x8, addend: 0x0, symName: '_$s8Razorpay25FileHandlingUtilFunctionsC06removeB13AtDefaultPathyySSFZTf4nd_n', symObjAddr: 0xE0C, symBinAddr: 0x286D0, symSize: 0x4B8 }
  - { offset: 0xD8DF8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x28B88, symSize: 0x20 }
  - { offset: 0xD9027, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCfETo', symObjAddr: 0x2DC, symBinAddr: 0x28E64, symSize: 0x48 }
  - { offset: 0xD9056, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCMa', symObjAddr: 0x324, symBinAddr: 0x28EAC, symSize: 0x20 }
  - { offset: 0xD9142, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x28B88, symSize: 0x20 }
  - { offset: 0xD91A2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC16cehcksTitleLabelSo7UILabelCSgvsTo', symObjAddr: 0x20, symBinAddr: 0x28BA8, symSize: 0x14 }
  - { offset: 0xD91F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC17checksDetailLabelSo7UILabelCSgvgTo', symObjAddr: 0x34, symBinAddr: 0x28BBC, symSize: 0x20 }
  - { offset: 0xD9248, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC17checksDetailLabelSo7UILabelCSgvsTo', symObjAddr: 0x54, symBinAddr: 0x28BDC, symSize: 0x14 }
  - { offset: 0xD9299, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC18accessoryImageViewSo07UIImageG0CSgvgTo', symObjAddr: 0x68, symBinAddr: 0x28BF0, symSize: 0x20 }
  - { offset: 0xD92F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC18accessoryImageViewSo07UIImageG0CSgvsTo', symObjAddr: 0x88, symBinAddr: 0x28C10, symSize: 0x14 }
  - { offset: 0xD9344, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC12awakeFromNibyyFTo', symObjAddr: 0x9C, symBinAddr: 0x28C24, symSize: 0x4C }
  - { offset: 0xD93BC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC11setSelected_8animatedySb_SbtFTo', symObjAddr: 0xE8, symBinAddr: 0x28C70, symSize: 0x64 }
  - { offset: 0xD9421, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC5style15reuseIdentifierACSo011UITableViewD5StyleV_SSSgtcfcTo', symObjAddr: 0x14C, symBinAddr: 0x28CD4, symSize: 0xE0 }
  - { offset: 0xD94A9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x22C, symBinAddr: 0x28DB4, symSize: 0x80 }
  - { offset: 0xD94E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay21OpinionatedChecksCellCfD', symObjAddr: 0x2AC, symBinAddr: 0x28E34, symSize: 0x30 }
  - { offset: 0xD9684, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14sharedInstance020_D5B288FFF260912DDA9E11E37AC5A806BLLACvpZ', symObjAddr: 0x898, symBinAddr: 0xA3C68, symSize: 0x0 }
  - { offset: 0xD9820, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14sharedInstance020_D5B288FFF260912DDA9E11E37AC5A806BLL_WZ', symObjAddr: 0x4, symBinAddr: 0x28ED0, symSize: 0x2C }
  - { offset: 0xD98D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCfETo', symObjAddr: 0x54C, symBinAddr: 0x29418, symSize: 0x4 }
  - { offset: 0xD990D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCMa', symObjAddr: 0x7B4, symBinAddr: 0x29680, symSize: 0x20 }
  - { offset: 0xD9921, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A9ConstantsC13FunctionErrorOAEs0D0AAWl', symObjAddr: 0x844, symBinAddr: 0x29710, symSize: 0x44 }
  - { offset: 0xD99EC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZ', symObjAddr: 0x0, symBinAddr: 0x28ECC, symSize: 0x4 }
  - { offset: 0xD9A1B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCACyc020_D5B288FFF260912DDA9C11E37AC5A806BLlfcTo', symObjAddr: 0x30, symBinAddr: 0x28EFC, symSize: 0x4C }
  - { offset: 0xD9A87, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC15initWithWebView_14andMerchantKeyySo05WKWebF0C_SSSgtFZ', symObjAddr: 0x7C, symBinAddr: 0x28F48, symSize: 0x14 }
  - { offset: 0xD9B0A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC15initWithWebView_14andMerchantKeyySo05WKWebF0C_SSSgtFZTo', symObjAddr: 0x90, symBinAddr: 0x28F5C, symSize: 0x78 }
  - { offset: 0xD9B5D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZTo', symObjAddr: 0x108, symBinAddr: 0x28FD4, symSize: 0x14 }
  - { offset: 0xD9B9E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14setPaymentDatayySDys11AnyHashableVypGF', symObjAddr: 0x11C, symBinAddr: 0x28FE8, symSize: 0xC8 }
  - { offset: 0xD9C27, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC14setPaymentDatayySDys11AnyHashableVypGFTo', symObjAddr: 0x1E4, symBinAddr: 0x290B0, symSize: 0x11C }
  - { offset: 0xD9C79, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKF', symObjAddr: 0x300, symBinAddr: 0x291CC, symSize: 0x14 }
  - { offset: 0xD9CAF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKFTo', symObjAddr: 0x314, symBinAddr: 0x291E0, symSize: 0x6C }
  - { offset: 0xD9CE6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC5closeyyF', symObjAddr: 0x380, symBinAddr: 0x2924C, symSize: 0xC4 }
  - { offset: 0xD9D64, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC5closeyyFTo', symObjAddr: 0x444, symBinAddr: 0x29310, symSize: 0xD8 }
  - { offset: 0xD9DBD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfCfD', symObjAddr: 0x51C, symBinAddr: 0x293E8, symSize: 0x30 }
  - { offset: 0xD9DF4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZTf4d_n', symObjAddr: 0x550, symBinAddr: 0x2941C, symSize: 0x128 }
  - { offset: 0xD9E58, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKFTf4dd_n', symObjAddr: 0x678, symBinAddr: 0x29544, symSize: 0x13C }
  - { offset: 0xDA051, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC14sharedInstanceACvpZ', symObjAddr: 0x35A8, symBinAddr: 0xBA1E8, symSize: 0x0 }
  - { offset: 0xDA0C4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityC14sharedInstance_WZ', symObjAddr: 0x0, symBinAddr: 0x29754, symSize: 0x38 }
  - { offset: 0xDA113, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityCMa', symObjAddr: 0x50, symBinAddr: 0x29794, symSize: 0x20 }
  - { offset: 0xDA127, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwxx', symObjAddr: 0x124, symBinAddr: 0x297C0, symSize: 0x30 }
  - { offset: 0xDA13B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwcp', symObjAddr: 0x154, symBinAddr: 0x297F0, symSize: 0x5C }
  - { offset: 0xDA14F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwca', symObjAddr: 0x1B0, symBinAddr: 0x2984C, symSize: 0x94 }
  - { offset: 0xDA163, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0x244, symBinAddr: 0x298E0, symSize: 0x14 }
  - { offset: 0xDA177, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwta', symObjAddr: 0x258, symBinAddr: 0x298F4, symSize: 0x64 }
  - { offset: 0xDA18B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwet', symObjAddr: 0x2BC, symBinAddr: 0x29958, symSize: 0x48 }
  - { offset: 0xDA19F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVwst', symObjAddr: 0x304, symBinAddr: 0x299A0, symSize: 0x4C }
  - { offset: 0xDA1B3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7CXErrorVMa', symObjAddr: 0x350, symBinAddr: 0x299EC, symSize: 0x10 }
  - { offset: 0xDA1FE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14CXAvailabilityCfd', symObjAddr: 0x38, symBinAddr: 0x2978C, symSize: 0x8 }
  - { offset: 0xDA3B2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15TargetConstantsCMa', symObjAddr: 0x10, symBinAddr: 0x299FC, symSize: 0x20 }
  - { offset: 0xDA560, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC14sharedInstance33_C8B8A67CD7964AACF65D90AF0C57FAABLLACvpZ', symObjAddr: 0x9710, symBinAddr: 0xA3DD8, symSize: 0x0 }
  - { offset: 0xDA8B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11isSetupDone33_C8B8A67CD7964AACF65D90AF0C57FAABLLSbvpZ', symObjAddr: 0x9718, symBinAddr: 0xA3DE0, symSize: 0x0 }
  - { offset: 0xDA8D3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11base62chars33_C8B8A67CD7964AACF65D90AF0C57FAABLLSaySJGvpZ', symObjAddr: 0x9720, symBinAddr: 0xA3DE8, symSize: 0x0 }
  - { offset: 0xDAC73, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tF', symObjAddr: 0x2AFC, symBinAddr: 0x2C518, symSize: 0x7A4 }
  - { offset: 0xDB129, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tFy10Foundation10URLRequestV_AH4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x7AAC, symBinAddr: 0x3149C, symSize: 0x14 }
  - { offset: 0xDB175, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC24triggerSessionErroredApi14withErrorLevelyAA0ahI0O_tFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x7AC0, symBinAddr: 0x314B0, symSize: 0x14 }
  - { offset: 0xDB27A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC14sharedInstance33_C8B8A67CD7964AACF65D90AF0C57FAABLL_WZ', symObjAddr: 0x5160, symBinAddr: 0x2EB50, symSize: 0x38 }
  - { offset: 0xDB2FB, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TR', symObjAddr: 0x71B4, symBinAddr: 0x30BA4, symSize: 0x20 }
  - { offset: 0xDB30F, size: 0x8, addend: 0x0, symName: '_$sIeg_IyB_TR', symObjAddr: 0x71D4, symBinAddr: 0x30BC4, symSize: 0x20 }
  - { offset: 0xDB327, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyF', symObjAddr: 0x71F4, symBinAddr: 0x30BE4, symSize: 0x6B8 }
  - { offset: 0xDB74B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyFy10Foundation10URLRequestV_AE4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x7A84, symBinAddr: 0x31474, symSize: 0x14 }
  - { offset: 0xDB797, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC22triggerCreteSessionApiyyFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x7A98, symBinAddr: 0x31488, symSize: 0x14 }
  - { offset: 0xDB7E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCMa', symObjAddr: 0x79C4, symBinAddr: 0x313B4, symSize: 0x20 }
  - { offset: 0xDB80E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11base62chars33_C8B8A67CD7964AACF65D90AF0C57FAABLL_WZ', symObjAddr: 0x79E4, symBinAddr: 0x313D4, symSize: 0xA0 }
  - { offset: 0xDB90C, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFyp_Tg5', symObjAddr: 0x7BC4, symBinAddr: 0x315B4, symSize: 0x104 }
  - { offset: 0xDBA71, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSs_Tg5', symObjAddr: 0x7CC8, symBinAddr: 0x316B8, symSize: 0x100 }
  - { offset: 0xDBBD6, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF8Razorpay11CheckPointsV_Tg5', symObjAddr: 0x7F00, symBinAddr: 0x318F0, symSize: 0x11C }
  - { offset: 0xDBD3B, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x801C, symBinAddr: 0x31A0C, symSize: 0xE8 }
  - { offset: 0xDBEA0, size: 0x8, addend: 0x0, symName: '_$sSBss17FixedWidthInteger14RawSignificandRpzrlE6random2in5usingxSnyxG_qd__ztSGRd__lFZSd_s27SystemRandomNumberGeneratorVTgm5', symObjAddr: 0x8104, symBinAddr: 0x31AF4, symSize: 0xA8 }
  - { offset: 0xDBFAB, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSS_Tgq5', symObjAddr: 0x81AC, symBinAddr: 0x31B9C, symSize: 0xB8 }
  - { offset: 0xDBFD8, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSJ_Tgmq5', symObjAddr: 0x8264, symBinAddr: 0x31C54, symSize: 0x74 }
  - { offset: 0xDC00E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC5setup33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyFyyXEfU_TA', symObjAddr: 0x8920, symBinAddr: 0x32310, symSize: 0x20 }
  - { offset: 0xDC02D, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x8950, symBinAddr: 0x32340, symSize: 0x20 }
  - { offset: 0xDC056, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x8970, symBinAddr: 0x32360, symSize: 0x10 }
  - { offset: 0xDC06A, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x8980, symBinAddr: 0x32370, symSize: 0x8 }
  - { offset: 0xDC07E, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueC8DispatchE10AttributesVAEs10SetAlgebraACWl', symObjAddr: 0x8A50, symBinAddr: 0x32378, symSize: 0x48 }
  - { offset: 0xDC092, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16readBatchPayload3keyypSgSS_tFAFyXEfU_TA', symObjAddr: 0x8ADC, symBinAddr: 0x323C0, symSize: 0x1C }
  - { offset: 0xDC0A6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptFyyYbcfU_TA', symObjAddr: 0x8B4C, symBinAddr: 0x32410, symSize: 0x10 }
  - { offset: 0xDC0BA, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyS2S__GWOe', symObjAddr: 0x8B5C, symBinAddr: 0x32420, symSize: 0x8 }
  - { offset: 0xDC159, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0x930C, symBinAddr: 0x32B94, symSize: 0x48 }
  - { offset: 0xDC16D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17resetBatchPayload33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyFyyXEfU_TA', symObjAddr: 0x941C, symBinAddr: 0x32C1C, symSize: 0x20 }
  - { offset: 0xDC181, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16createNewSessionyyFyyXEfU_TA', symObjAddr: 0x944C, symBinAddr: 0x32C4C, symSize: 0x20 }
  - { offset: 0xDC195, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20readFullBatchPayloadSDySSypGyFAEyXEfU_TA', symObjAddr: 0x94C0, symBinAddr: 0x32C7C, symSize: 0x18 }
  - { offset: 0xDC2B6, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_yps11AnyHashableVypTg5', symObjAddr: 0x11D8, symBinAddr: 0x2ABF4, symSize: 0x47C }
  - { offset: 0xDC3F3, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_S2SypTg5', symObjAddr: 0x1654, symBinAddr: 0x2B070, symSize: 0x3DC }
  - { offset: 0xDC538, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SDyS2SGSSypTg5', symObjAddr: 0x1A30, symBinAddr: 0x2B44C, symSize: 0x3DC }
  - { offset: 0xDC67D, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SSSgSSypTg5', symObjAddr: 0x1E0C, symBinAddr: 0x2B828, symSize: 0x3E4 }
  - { offset: 0xDC7C2, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SDys11AnyHashableVypGAEypTg5', symObjAddr: 0x21F0, symBinAddr: 0x2BC0C, symSize: 0x480 }
  - { offset: 0xDC8ED, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SSs11AnyHashableVypTg5', symObjAddr: 0x2670, symBinAddr: 0x2C08C, symSize: 0x48C }
  - { offset: 0xDCAB4, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF8Razorpay27CheckoutBridgeErrorResponseC0eG0V10CodingKeysO_Tg5', symObjAddr: 0x4AC4, symBinAddr: 0x2E4E0, symSize: 0xA4 }
  - { offset: 0xDCB38, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF8Razorpay0D10ErrorLevelO_Tg5', symObjAddr: 0x4B68, symBinAddr: 0x2E584, symSize: 0x110 }
  - { offset: 0xDCC25, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x7AD4, symBinAddr: 0x314C4, symSize: 0xF0 }
  - { offset: 0xDCD61, size: 0x8, addend: 0x0, symName: '_$sSlsE6prefixy11SubSequenceQzSiFSS_Tg5Tf4ng_n', symObjAddr: 0x8D04, symBinAddr: 0x325C8, symSize: 0x88 }
  - { offset: 0xDCF63, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17updateBasePayloadyyF', symObjAddr: 0x0, symBinAddr: 0x29A1C, symSize: 0x160 }
  - { offset: 0xDD025, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16createNewSessionyyF', symObjAddr: 0x160, symBinAddr: 0x29B7C, symSize: 0x67C }
  - { offset: 0xDD1E7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC10trackEvent9eventNameySS_tF', symObjAddr: 0x7DC, symBinAddr: 0x2A1F8, symSize: 0x1C8 }
  - { offset: 0xDD383, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC10trackEvent9eventName16havingPropertiesySS_SDySSypGtF', symObjAddr: 0x9A4, symBinAddr: 0x2A3C0, symSize: 0x280 }
  - { offset: 0xDD573, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18addPaymentProperty9havingKey8andValueySS_yptF', symObjAddr: 0xC24, symBinAddr: 0x2A640, symSize: 0x1C4 }
  - { offset: 0xDD64A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16addOrderProperty9havingKey8andValueySS_yptF', symObjAddr: 0xDE8, symBinAddr: 0x2A804, symSize: 0x298 }
  - { offset: 0xDD757, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC9trackPage_9havingUrlySS_SStF', symObjAddr: 0x1080, symBinAddr: 0x2AA9C, symSize: 0x158 }
  - { offset: 0xDD96F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyF', symObjAddr: 0x32A0, symBinAddr: 0x2CCBC, symSize: 0x620 }
  - { offset: 0xDDAE2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyFy10Foundation10URLRequestV_AE4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x78AC, symBinAddr: 0x3129C, symSize: 0x14 }
  - { offset: 0xDDB2A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11submitBatchyyFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x78C0, symBinAddr: 0x312B0, symSize: 0x14 }
  - { offset: 0xDDB93, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC36gatherAnalyticsDataForCustomCheckoutSDySSypGyF', symObjAddr: 0x38C0, symBinAddr: 0x2D2DC, symSize: 0x56C }
  - { offset: 0xDDD28, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC19reachabilityChangedyyAA20InternetConnectivityOF', symObjAddr: 0x3E2C, symBinAddr: 0x2D848, symSize: 0xC98 }
  - { offset: 0xDE45F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC27networkAndLocaleInformationSS_S2StSgyF', symObjAddr: 0x4C78, symBinAddr: 0x2E694, symSize: 0x3A8 }
  - { offset: 0xDE59D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC23fetchBasePayloadContextSDySSypGSgyF', symObjAddr: 0x504C, symBinAddr: 0x2EA3C, symSize: 0x114 }
  - { offset: 0xDE662, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCACyc33_C8B8A67CD7964AACF65D90AF0C57FAABLlfc', symObjAddr: 0x5198, symBinAddr: 0x2EB88, symSize: 0x278 }
  - { offset: 0xDE741, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18inflateBasePayload33_C8B8A67CD7964AACF65D90AF0C57FAABLLyyF', symObjAddr: 0x5410, symBinAddr: 0x2EE00, symSize: 0x19B4 }
  - { offset: 0xDF35E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC16readBatchPayload3keyypSgSS_tFAFyXEfU_', symObjAddr: 0x6DC4, symBinAddr: 0x307B4, symSize: 0xF0 }
  - { offset: 0xDF3CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20readFullBatchPayloadSDySSypGyFAEyXEfU_', symObjAddr: 0x6EB4, symBinAddr: 0x308A4, symSize: 0x70 }
  - { offset: 0xDF404, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptF', symObjAddr: 0x6F24, symBinAddr: 0x30914, symSize: 0x194 }
  - { offset: 0xDF45D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC18updateBatchPayload3key5valueySS_yptFyyYbcfU_', symObjAddr: 0x70B8, symBinAddr: 0x30AA8, symSize: 0xFC }
  - { offset: 0xDF4CB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCfd', symObjAddr: 0x7950, symBinAddr: 0x31340, symSize: 0x54 }
  - { offset: 0xDF4EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackCfD', symObjAddr: 0x79A4, symBinAddr: 0x31394, symSize: 0x20 }
  - { offset: 0xDF548, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC20createBaseTrackEvent33_C8B8A67CD7964AACF65D90AF0C57FAABLLySDySSypGSSFTf4nd_n', symObjAddr: 0x82D8, symBinAddr: 0x31CC8, symSize: 0x1A8 }
  - { offset: 0xDF6B3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC11getDeviceID33_C8B8A67CD7964AACF65D90AF0C57FAABLLSSyFTf4d_n', symObjAddr: 0x8480, symBinAddr: 0x31E70, symSize: 0x2C4 }
  - { offset: 0xDF753, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC17getSharedInstance17havingMerchantKeyACSS_tFZTf4nd_g', symObjAddr: 0x8744, symBinAddr: 0x32134, symSize: 0x1DC }
  - { offset: 0xDF7E4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV8tobase626numberSSs5Int64V_tFZTf4nd_n', symObjAddr: 0x8B64, symBinAddr: 0x32428, symSize: 0x1A0 }
  - { offset: 0xDF906, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18ShortCodeGeneratorV11getUniqueIdySSSdFZTf4nd_n', symObjAddr: 0x8D8C, symBinAddr: 0x32650, symSize: 0x1E0 }
  - { offset: 0xDFA98, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10LumberjackC30gatherAnalyticsDataForCheckoutSDySSypGyFTf4d_n', symObjAddr: 0x8F6C, symBinAddr: 0x32830, symSize: 0x364 }
  - { offset: 0xDFE4A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC11mercahntKeySSSgvpZ', symObjAddr: 0x15090, symBinAddr: 0xBA1F0, symSize: 0x0 }
  - { offset: 0xE0110, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0xAE0, symBinAddr: 0x337BC, symSize: 0x64 }
  - { offset: 0xE0148, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0xB44, symBinAddr: 0x33820, symSize: 0x30 }
  - { offset: 0xE0175, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xB74, symBinAddr: 0x33850, symSize: 0x80 }
  - { offset: 0xE022A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0xBF4, symBinAddr: 0x338D0, symSize: 0x68 }
  - { offset: 0xE02AE, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFs11AnyHashableV_ypTg5', symObjAddr: 0xC5C, symBinAddr: 0x33938, symSize: 0x78 }
  - { offset: 0xE0348, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0xCD4, symBinAddr: 0x339B0, symSize: 0x48 }
  - { offset: 0xE03DB, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0xD1C, symBinAddr: 0x339F8, symSize: 0xE0 }
  - { offset: 0xE045E, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0xDFC, symBinAddr: 0x33AD8, symSize: 0xC4 }
  - { offset: 0xE048B, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xEC0, symBinAddr: 0x33B9C, symSize: 0x174 }
  - { offset: 0xE0566, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ypTg5', symObjAddr: 0x1034, symBinAddr: 0x33D10, symSize: 0xC8 }
  - { offset: 0xE05D0, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFs11AnyHashableV_ypTg5', symObjAddr: 0x10FC, symBinAddr: 0x33DD8, symSize: 0xBC }
  - { offset: 0xE0636, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFs11AnyHashableV_ypTg5', symObjAddr: 0x11B8, symBinAddr: 0x33E94, symSize: 0xE4 }
  - { offset: 0xE06DC, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFs11AnyHashableV_ypTg5', symObjAddr: 0x12B0, symBinAddr: 0x33F8C, symSize: 0xC8 }
  - { offset: 0xE0724, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x1474, symBinAddr: 0x34150, symSize: 0x1F8 }
  - { offset: 0xE07C1, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFs11AnyHashableV_ypTg5', symObjAddr: 0x166C, symBinAddr: 0x34348, symSize: 0x1F8 }
  - { offset: 0xE0872, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SDys11AnyHashableVypGTg5', symObjAddr: 0x1864, symBinAddr: 0x34540, symSize: 0x1C8 }
  - { offset: 0xE0925, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x1A2C, symBinAddr: 0x34708, symSize: 0x1D4 }
  - { offset: 0xE09C5, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFs11AnyHashableV_ypTg5', symObjAddr: 0x1C00, symBinAddr: 0x348DC, symSize: 0x1C8 }
  - { offset: 0xE0A6D, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRs11AnyHashableV_ypTg5070$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_s11cD7V_ypTg5Tf3nnpf_n', symObjAddr: 0x1DC8, symBinAddr: 0x34AA4, symSize: 0x30 }
  - { offset: 0xE0AE7, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV5merge_8isUnique16uniquingKeysWithyqd__n_Sbq_q__q_tKXEtKSTRd__x_q_t7ElementRtd__lFs11AnyHashableV_yps15LazyMapSequenceVySDyAIypGAI_yptGTg5079$s8Razorpay24SwiftCompatibilityHelperC19combineDictionaries7dictOne0G3TwoSDys11jK25VypGAI_AItFZypyp_yptXEfU_Tf1nncn_n', symObjAddr: 0x1DF8, symBinAddr: 0x34AD4, symSize: 0x288 }
  - { offset: 0xE0C76, size: 0x8, addend: 0x0, symName: '_$ss15LazyMapSequenceV8IteratorV4nextq_SgyFSDys11AnyHashableVypG_AH_yptTg5', symObjAddr: 0x2080, symBinAddr: 0x34D5C, symSize: 0x254 }
  - { offset: 0xE0FD6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCSgSgWOe', symObjAddr: 0x2DB8, symBinAddr: 0x359B4, symSize: 0x10 }
  - { offset: 0xE0FEA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCMa', symObjAddr: 0x2DC8, symBinAddr: 0x359C4, symSize: 0x20 }
  - { offset: 0xE0FFE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilCSgSgWOy', symObjAddr: 0x2F68, symBinAddr: 0x35A9C, symSize: 0x10 }
  - { offset: 0xE10D5, size: 0x8, addend: 0x0, symName: '_$sSD7merging_16uniquingKeysWithSDyxq_GACn_q_q__q_tKXEtKFs11AnyHashableV_ypTg5079$s8Razorpay24SwiftCompatibilityHelperC19combineDictionaries7dictOne0G3TwoSDys11eF25VypGAI_AItFZypyp_yptXEfU_Tf1ncn_n', symObjAddr: 0xA64, symBinAddr: 0x33740, symSize: 0x7C }
  - { offset: 0xE11D3, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x22D4, symBinAddr: 0x34FB0, symSize: 0x110 }
  - { offset: 0xE1314, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x23E4, symBinAddr: 0x350C0, symSize: 0xFC }
  - { offset: 0xE145B, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTgm5Tf4g_n', symObjAddr: 0x24E0, symBinAddr: 0x351BC, symSize: 0xE4 }
  - { offset: 0xE159C, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCs11AnyHashableV_ypTgm5Tf4g_n', symObjAddr: 0x25D0, symBinAddr: 0x352AC, symSize: 0x114 }
  - { offset: 0xE16E3, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_12CoreGraphics7CGFloatVTgm5Tf4g_n', symObjAddr: 0x27D0, symBinAddr: 0x353CC, symSize: 0xEC }
  - { offset: 0xE182A, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSSgTgm5Tf4g_n', symObjAddr: 0x28C8, symBinAddr: 0x354C4, symSize: 0xFC }
  - { offset: 0xE1971, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo38UIApplicationOpenExternalURLOptionsKeya_ypTgm5Tf4g_n', symObjAddr: 0x29C4, symBinAddr: 0x355C0, symSize: 0x100 }
  - { offset: 0xE1AB8, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SiTgm5Tf4g_n', symObjAddr: 0x2AD0, symBinAddr: 0x356CC, symSize: 0xE4 }
  - { offset: 0xE1BFF, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypSgTgm5Tf4g_n', symObjAddr: 0x2BB4, symBinAddr: 0x357B0, symSize: 0x110 }
  - { offset: 0xE1D66, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCACycfC', symObjAddr: 0x0, symBinAddr: 0x32CDC, symSize: 0x38 }
  - { offset: 0xE1D99, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCACycfc', symObjAddr: 0x38, symBinAddr: 0x32D14, symSize: 0x20 }
  - { offset: 0xE1DDB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC13analyticsUtilAA09AnalyticsF0CSgvg', symObjAddr: 0x58, symBinAddr: 0x32D34, symSize: 0xF8 }
  - { offset: 0xE1E4D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC12getSessionIdSSSgyF', symObjAddr: 0x150, symBinAddr: 0x32E2C, symSize: 0xB0 }
  - { offset: 0xE1EA2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC14getMercahntKeySSSgyF', symObjAddr: 0x200, symBinAddr: 0x32EDC, symSize: 0x44 }
  - { offset: 0xE1EBD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC17getHTMLLoaingPageSSyF', symObjAddr: 0x244, symBinAddr: 0x32F20, symSize: 0x2C }
  - { offset: 0xE1EDE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC16isCFBEnabledUserSbyF', symObjAddr: 0x270, symBinAddr: 0x32F4C, symSize: 0x18 }
  - { offset: 0xE1FA8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC26getBaseAnalyticsPropertiesSDys11AnyHashableVypGyF', symObjAddr: 0x288, symBinAddr: 0x32F64, symSize: 0x574 }
  - { offset: 0xE23A0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC10trackEvent9eventName7payloadySS_SDys11AnyHashableVypGtF', symObjAddr: 0x7FC, symBinAddr: 0x334D8, symSize: 0xEC }
  - { offset: 0xE246B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC21submitAnalyticsEventsyyF', symObjAddr: 0x8E8, symBinAddr: 0x335C4, symSize: 0x94 }
  - { offset: 0xE24C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerC27gatherAnalyticsDataCustomUISDySSypGSgyF', symObjAddr: 0x97C, symBinAddr: 0x33658, symSize: 0xA8 }
  - { offset: 0xE2514, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCfd', symObjAddr: 0xA24, symBinAddr: 0x33700, symSize: 0x1C }
  - { offset: 0xE2545, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18TurboPluginManagerCfD', symObjAddr: 0xA40, symBinAddr: 0x3371C, symSize: 0x24 }
  - { offset: 0xE2848, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorCMa', symObjAddr: 0x1EC, symBinAddr: 0x35C98, symSize: 0x20 }
  - { offset: 0xE2883, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorC12awakeFromNibyyFTo', symObjAddr: 0x0, symBinAddr: 0x35AAC, symSize: 0xEC }
  - { offset: 0xE28E8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0xF8, symBinAddr: 0x35BA4, symSize: 0x6C }
  - { offset: 0xE291D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23CustomActivityIndicatorCfD', symObjAddr: 0x1BC, symBinAddr: 0x35C68, symSize: 0x30 }
  - { offset: 0xE2AB1, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x35CB8, symSize: 0x268 }
  - { offset: 0xE2B45, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x35CB8, symSize: 0x268 }
  - { offset: 0xE2C1E, size: 0x8, addend: 0x0, symName: '_$sSo17UIAlertControllerC8RazorpayE24displayAlertWithOkButton7messageySS_tFZySo0A6ActionCcfU_TA', symObjAddr: 0x28C, symBinAddr: 0x35F44, symSize: 0x28 }
  - { offset: 0xE2C50, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2B4, symBinAddr: 0x35F6C, symSize: 0x10 }
  - { offset: 0xE2C64, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2C4, symBinAddr: 0x35F7C, symSize: 0x8 }
  - { offset: 0xE2E16, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCACycfc', symObjAddr: 0x8, symBinAddr: 0x35F84, symSize: 0x134 }
  - { offset: 0xE2F87, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCMa', symObjAddr: 0x608, symBinAddr: 0x36584, symSize: 0x20 }
  - { offset: 0xE2FF6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCACycfc', symObjAddr: 0x8, symBinAddr: 0x35F84, symSize: 0x134 }
  - { offset: 0xE30F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21startReceivingUpdatesyyKF', symObjAddr: 0x13C, symBinAddr: 0x360B8, symSize: 0x164 }
  - { offset: 0xE315E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21broadcastNotification12notificationy10Foundation0E0V_tF', symObjAddr: 0x2A0, symBinAddr: 0x3621C, symSize: 0x11C }
  - { offset: 0xE31D6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC21broadcastNotification12notificationy10Foundation0E0V_tFTo', symObjAddr: 0x3BC, symBinAddr: 0x36338, symSize: 0x8C }
  - { offset: 0xE31EF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverC20stopReceivingUpdatesyyF', symObjAddr: 0x448, symBinAddr: 0x363C4, symSize: 0x18C }
  - { offset: 0xE3283, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20ReachabilityObserverCfD', symObjAddr: 0x5D4, symBinAddr: 0x36550, symSize: 0x34 }
  - { offset: 0xE3432, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwxx', symObjAddr: 0xA4, symBinAddr: 0x365A4, symSize: 0x38 }
  - { offset: 0xE3446, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwcp', symObjAddr: 0x150, symBinAddr: 0x365DC, symSize: 0x98 }
  - { offset: 0xE345A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwca', symObjAddr: 0x1E8, symBinAddr: 0x36674, symSize: 0xB4 }
  - { offset: 0xE346E, size: 0x8, addend: 0x0, symName: ___swift_memcpy65_8, symObjAddr: 0x29C, symBinAddr: 0x36728, symSize: 0x24 }
  - { offset: 0xE3482, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwta', symObjAddr: 0x2C0, symBinAddr: 0x3674C, symSize: 0x64 }
  - { offset: 0xE3496, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwet', symObjAddr: 0x324, symBinAddr: 0x367B0, symSize: 0x48 }
  - { offset: 0xE34AA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwst', symObjAddr: 0x36C, symBinAddr: 0x367F8, symSize: 0x58 }
  - { offset: 0xE34BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwug', symObjAddr: 0x3C4, symBinAddr: 0x36850, symSize: 0x8 }
  - { offset: 0xE34D2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOwui', symObjAddr: 0x3D0, symBinAddr: 0x36858, symSize: 0x8 }
  - { offset: 0xE34E6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOMa', symObjAddr: 0x3D8, symBinAddr: 0x36860, symSize: 0x10 }
  - { offset: 0xE356C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOb', symObjAddr: 0x554, symBinAddr: 0x369DC, symSize: 0x28 }
  - { offset: 0xE3584, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeOWOb', symObjAddr: 0x554, symBinAddr: 0x369DC, symSize: 0x28 }
  - { offset: 0xE35BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay7URLTypeO3URLSSyF', symObjAddr: 0x3E8, symBinAddr: 0x36870, symSize: 0x16C }
  - { offset: 0xE388B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSON_WZ', symObjAddr: 0x0, symBinAddr: 0x36A04, symSize: 0x1F8 }
  - { offset: 0xE38AF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSONSDys11AnyHashableVypGvpZ', symObjAddr: 0x2C48, symBinAddr: 0xBA200, symSize: 0x0 }
  - { offset: 0xE390A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay9ConstantsV28PAYMENT_CANCELLED_ERROR_JSON_WZ', symObjAddr: 0x0, symBinAddr: 0x36A04, symSize: 0x1F8 }
  - { offset: 0xE3C56, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelC8delegate24analyticsUtilityInstance11checkoutURL13otpelfEnabled7options9publicKey17isDataDelegateSet13timeStampOpen0stU6Millis26arrExternalPaymentEntitiesAcA0aB10VCDelegate_pSg_AA13AnalyticsUtilCSSSbSDys11AnyHashableVypGSSSbS2iSayAA06PluginyQ0_pGSgtcfc', symObjAddr: 0x0, symBinAddr: 0x36BFC, symSize: 0xE4 }
  - { offset: 0xE3CEF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelCMa', symObjAddr: 0x138, symBinAddr: 0x36D34, symSize: 0x20 }
  - { offset: 0xE3D8A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelC8delegate24analyticsUtilityInstance11checkoutURL13otpelfEnabled7options9publicKey17isDataDelegateSet13timeStampOpen0stU6Millis26arrExternalPaymentEntitiesAcA0aB10VCDelegate_pSg_AA13AnalyticsUtilCSSSbSDys11AnyHashableVypGSSSbS2iSayAA06PluginyQ0_pGSgtcfc', symObjAddr: 0x0, symBinAddr: 0x36BFC, symSize: 0xE4 }
  - { offset: 0xE3E73, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13CheckoutModelCfD', symObjAddr: 0xE4, symBinAddr: 0x36CE0, symSize: 0x54 }
  - { offset: 0xE4222, size: 0x8, addend: 0x0, symName: '_$ss30_dictionaryDownCastConditionalySDyq0_q1_GSgSDyxq_GSHRzSHR0_r2_lFs11AnyHashableV_ypSSypTg5', symObjAddr: 0xF30, symBinAddr: 0x37C84, symSize: 0x458 }
  - { offset: 0xE447D, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE6starts4withSbqd___tSTRd__AAQyd__ABRSlFSS_SSTg5', symObjAddr: 0x3738, symBinAddr: 0x3A48C, symSize: 0x164 }
  - { offset: 0xE45F1, size: 0x8, addend: 0x0, symName: '_$sSo18NSLayoutConstraintC4item9attribute9relatedBy6toItemAD10multiplier8constantAByp_So0A9AttributeVSo0A8RelationVypSgAJ12CoreGraphics7CGFloatVAPtcfCTO', symObjAddr: 0x0, symBinAddr: 0x36D54, symSize: 0x164 }
  - { offset: 0xE4D7D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfETo', symObjAddr: 0x2738, symBinAddr: 0x3948C, symSize: 0xB0 }
  - { offset: 0xE4DAD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCMa', symObjAddr: 0x2B20, symBinAddr: 0x39874, symSize: 0x20 }
  - { offset: 0xE4DE2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC16sendCredResponse33_813001DEEE20F6D789A6CA33DA760B43LL4withySi_tF', symObjAddr: 0x2B40, symBinAddr: 0x39894, symSize: 0x3CC }
  - { offset: 0xE5080, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15publishCredData33_813001DEEE20F6D789A6CA33DA760B43LLyySo14NSNotificationCF', symObjAddr: 0x2F0C, symBinAddr: 0x39C60, symSize: 0x288 }
  - { offset: 0xE5199, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15publishCredData33_813001DEEE20F6D789A6CA33DA760B43LLyySo14NSNotificationCFTo', symObjAddr: 0x3194, symBinAddr: 0x39EE8, symSize: 0x50 }
  - { offset: 0xE51B5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSF', symObjAddr: 0x31E4, symBinAddr: 0x39F38, symSize: 0x554 }
  - { offset: 0xE550C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_Tf2in_n', symObjAddr: 0x389C, symBinAddr: 0x3A5F0, symSize: 0xE8 }
  - { offset: 0xE5558, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU_', symObjAddr: 0x3984, symBinAddr: 0x3A6D8, symSize: 0x24C }
  - { offset: 0xE56B0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU0_', symObjAddr: 0x3BD0, symBinAddr: 0x3A924, symSize: 0x310 }
  - { offset: 0xE5911, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17gestureRecognizer_33shouldRecognizeSimultaneouslyWithSbSo09UIGestureE0C_AGtFTo', symObjAddr: 0x3EE0, symBinAddr: 0x3AC34, symSize: 0x18 }
  - { offset: 0xE5931, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17gestureRecognizer_33shouldRecognizeSimultaneouslyWithSbSo09UIGestureE0C_AGtFTo', symObjAddr: 0x3EE0, symBinAddr: 0x3AC34, symSize: 0x18 }
  - { offset: 0xE595D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_Tf2in_nTA', symObjAddr: 0x3FDC, symBinAddr: 0x3AC78, symSize: 0xC }
  - { offset: 0xE5971, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3FE8, symBinAddr: 0x3AC84, symSize: 0x10 }
  - { offset: 0xE5985, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3FF8, symBinAddr: 0x3AC94, symSize: 0x8 }
  - { offset: 0xE5999, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU_TA', symObjAddr: 0x4168, symBinAddr: 0x3ACC0, symSize: 0x8 }
  - { offset: 0xE59AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC20pollStatusForPaymentyySSFyyScMYccfU_ySDys11AnyHashableVypGcfU0_TA', symObjAddr: 0x4170, symBinAddr: 0x3ACC8, symSize: 0x8 }
  - { offset: 0xE5A7F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x5638, symBinAddr: 0x3C098, symSize: 0x8 }
  - { offset: 0xE5A93, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_TA', symObjAddr: 0x5644, symBinAddr: 0x3C0A4, symSize: 0xC }
  - { offset: 0xE5AA7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_yyScMYccfU_TA', symObjAddr: 0x5688, symBinAddr: 0x3C0E8, symSize: 0xC }
  - { offset: 0xE5ABB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFySo13UIAlertActionCcfU_TA', symObjAddr: 0x5790, symBinAddr: 0x3C0F4, symSize: 0x30 }
  - { offset: 0xE5B07, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17activityIndicatorAA014CustomActivityE0CSgvgTo', symObjAddr: 0x164, symBinAddr: 0x36EB8, symSize: 0x20 }
  - { offset: 0xE5B67, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17activityIndicatorAA014CustomActivityE0CSgvsTo', symObjAddr: 0x184, symBinAddr: 0x36ED8, symSize: 0x14 }
  - { offset: 0xE5C62, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadViewyyF', symObjAddr: 0x198, symBinAddr: 0x36EEC, symSize: 0x168 }
  - { offset: 0xE5D4B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC8loadViewyyFTo', symObjAddr: 0x300, symBinAddr: 0x37054, symSize: 0x28 }
  - { offset: 0xE5D8B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC11viewDidLoadyyF', symObjAddr: 0x328, symBinAddr: 0x3707C, symSize: 0x14C }
  - { offset: 0xE5E08, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC11viewDidLoadyyFTo', symObjAddr: 0x474, symBinAddr: 0x371C8, symSize: 0x28 }
  - { offset: 0xE5E30, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15dismissKeyboardyyFTo', symObjAddr: 0x49C, symBinAddr: 0x371F0, symSize: 0x6C }
  - { offset: 0xE5ED7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC14viewWillAppearyySbFTo', symObjAddr: 0x508, symBinAddr: 0x3725C, symSize: 0x94 }
  - { offset: 0xE5F8F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13viewDidAppearyySbF', symObjAddr: 0x59C, symBinAddr: 0x372F0, symSize: 0x2B0 }
  - { offset: 0xE6102, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13viewDidAppearyySbFTo', symObjAddr: 0x84C, symBinAddr: 0x375A0, symSize: 0x30 }
  - { offset: 0xE612A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17viewWillDisappearyySbF', symObjAddr: 0x87C, symBinAddr: 0x375D0, symSize: 0xE0 }
  - { offset: 0xE617D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17viewWillDisappearyySbFTo', symObjAddr: 0x95C, symBinAddr: 0x376B0, symSize: 0x30 }
  - { offset: 0xE6198, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFTo', symObjAddr: 0x98C, symBinAddr: 0x376E0, symSize: 0x4C }
  - { offset: 0xE61F2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyF', symObjAddr: 0x9D8, symBinAddr: 0x3772C, symSize: 0x4D8 }
  - { offset: 0xE6462, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0xEB0, symBinAddr: 0x37C04, symSize: 0x80 }
  - { offset: 0xE64AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_', symObjAddr: 0x1388, symBinAddr: 0x380DC, symSize: 0x1F4 }
  - { offset: 0xE6512, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC21performInitialization33_813001DEEE20F6D789A6CA33DA760B43LLyyFyycfU0_yyScMYccfU_', symObjAddr: 0x157C, symBinAddr: 0x382D0, symSize: 0x7C }
  - { offset: 0xE6655, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC29payWithExternalPaymentManager7options9publicKey03arrfG8EntitiesySDys11AnyHashableVypG_SSSayAA06PluginG8Delegate_pGtF', symObjAddr: 0x15F8, symBinAddr: 0x3834C, symSize: 0x118 }
  - { offset: 0xE67D7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13cancelPayment33_813001DEEE20F6D789A6CA33DA760B43LLyyF', symObjAddr: 0x1710, symBinAddr: 0x38464, symSize: 0x458 }
  - { offset: 0xE6A1C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19addWebViewAsSubview33_813001DEEE20F6D789A6CA33DA760B43LL03webF0ySo05WKWebF0C_tF', symObjAddr: 0x1B68, symBinAddr: 0x388BC, symSize: 0x698 }
  - { offset: 0xE6CC0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC24sendUserAgentToAnalytics33_813001DEEE20F6D789A6CA33DA760B43LL15webViewResponse5erroryypSg_s5Error_pSgtF', symObjAddr: 0x2200, symBinAddr: 0x38F54, symSize: 0x36C }
  - { offset: 0xE6EC0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC12closeWebViewyyF', symObjAddr: 0x256C, symBinAddr: 0x392C0, symSize: 0x134 }
  - { offset: 0xE6F31, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfD', symObjAddr: 0x26A0, symBinAddr: 0x393F4, symSize: 0x40 }
  - { offset: 0xE6F63, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCCfDTo', symObjAddr: 0x26E0, symBinAddr: 0x39434, symSize: 0x58 }
  - { offset: 0xE6F97, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x27E8, symBinAddr: 0x3953C, symSize: 0x178 }
  - { offset: 0xE6FD8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x2960, symBinAddr: 0x396B4, symSize: 0x60 }
  - { offset: 0xE7054, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x29C0, symBinAddr: 0x39714, symSize: 0x138 }
  - { offset: 0xE7087, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2AF8, symBinAddr: 0x3984C, symSize: 0x28 }
  - { offset: 0xE7144, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC15getMobileNumber33_813001DEEE20F6D789A6CA33DA760B43LL11fromPayloadSSSgSDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x41C0, symBinAddr: 0x3ACD0, symSize: 0x1D8 }
  - { offset: 0xE71D7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC10getOrderId33_813001DEEE20F6D789A6CA33DA760B43LL11fromPayloadSSSgSDys11AnyHashableVypG_tFTf4nd_n', symObjAddr: 0x4398, symBinAddr: 0x3AEA8, symSize: 0x118 }
  - { offset: 0xE7328, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC23proceedToInitialization_7webviewyAA0aB5ModelC_AA0aB7WebViewCtFTf4nnd_n', symObjAddr: 0x44B0, symBinAddr: 0x3AFC0, symSize: 0xE6C }
  - { offset: 0xE7B6C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC18handleButtonCancelyySo05UIBarE4ItemCFTf4dn_n', symObjAddr: 0x531C, symBinAddr: 0x3BE2C, symSize: 0x248 }
  - { offset: 0xE7DBD, size: 0x8, addend: 0x0, symName: '_$sSo8UIDeviceC8RazorpayE9modelNameSSvgTf4d_n', symObjAddr: 0x0, symBinAddr: 0x3C14C, symSize: 0x276C }
  - { offset: 0xE7E63, size: 0x8, addend: 0x0, symName: '_$sSo8UIDeviceC8RazorpayE9modelNameSSvgTf4d_n', symObjAddr: 0x0, symBinAddr: 0x3C14C, symSize: 0x276C }
  - { offset: 0xE88F2, size: 0x8, addend: 0x0, symName: '_$sSSSg5label_yp5valuetWOh', symObjAddr: 0x27F8, symBinAddr: 0x3E8C8, symSize: 0x40 }
  - { offset: 0xE8B7A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay6LoggerCMa', symObjAddr: 0x10, symBinAddr: 0x3E908, symSize: 0x20 }
  - { offset: 0xE8D28, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC16internalInstance33_E1A96831138ED3F3E0A51B461F187E33LLACvpZ', symObjAddr: 0x16D0, symBinAddr: 0xA4518, symSize: 0x0 }
  - { offset: 0xE8DA3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC26startNSUncaughtExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFySo11NSExceptionCcfU_To', symObjAddr: 0x830, symBinAddr: 0x3F158, symSize: 0x28 }
  - { offset: 0xE8E6B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC16internalInstance33_E1A96831138ED3F3E0A51B461F187E33LL_WZ', symObjAddr: 0x0, symBinAddr: 0x3E928, symSize: 0x48 }
  - { offset: 0xE9037, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterCMa', symObjAddr: 0xDBC, symBinAddr: 0x3F6E4, symSize: 0x20 }
  - { offset: 0xE904B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_TA', symObjAddr: 0xE00, symBinAddr: 0x3F728, symSize: 0x8 }
  - { offset: 0xE905F, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xE08, symBinAddr: 0x3F730, symSize: 0x10 }
  - { offset: 0xE9073, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xE18, symBinAddr: 0x3F740, symSize: 0x8 }
  - { offset: 0xE90F0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_yyScMYcXEfU_TA', symObjAddr: 0x1658, symBinAddr: 0x3FE30, symSize: 0xC }
  - { offset: 0xE9104, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x1674, symBinAddr: 0x3FE4C, symSize: 0x8 }
  - { offset: 0xE931C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_', symObjAddr: 0x48, symBinAddr: 0x3E970, symSize: 0x1F4 }
  - { offset: 0xE9435, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_yyScMYcXEfU_', symObjAddr: 0x23C, symBinAddr: 0x3EB64, symSize: 0x1F8 }
  - { offset: 0xE9648, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC26startNSUncaughtExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFySo11NSExceptionCcfU_', symObjAddr: 0x434, symBinAddr: 0x3ED5C, symSize: 0x3FC }
  - { offset: 0xE9974, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyF', symObjAddr: 0x858, symBinAddr: 0x3F180, symSize: 0x254 }
  - { offset: 0xE99EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_', symObjAddr: 0xAAC, symBinAddr: 0x3F3D4, symSize: 0x1D0 }
  - { offset: 0xE9A8E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU_To', symObjAddr: 0xC7C, symBinAddr: 0x3F5A4, symSize: 0x28 }
  - { offset: 0xE9AAA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU_To', symObjAddr: 0xC7C, symBinAddr: 0x3F5A4, symSize: 0x28 }
  - { offset: 0xE9AF4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU0_To', symObjAddr: 0xCA4, symBinAddr: 0x3F5CC, symSize: 0x24 }
  - { offset: 0xE9B10, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU0_To', symObjAddr: 0xCA4, symBinAddr: 0x3F5CC, symSize: 0x24 }
  - { offset: 0xE9B5A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU1_To', symObjAddr: 0xCC8, symBinAddr: 0x3F5F0, symSize: 0x24 }
  - { offset: 0xE9B76, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU1_To', symObjAddr: 0xCC8, symBinAddr: 0x3F5F0, symSize: 0x24 }
  - { offset: 0xE9BC0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU2_To', symObjAddr: 0xCEC, symBinAddr: 0x3F614, symSize: 0x28 }
  - { offset: 0xE9BDC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU2_To', symObjAddr: 0xCEC, symBinAddr: 0x3F614, symSize: 0x28 }
  - { offset: 0xE9C26, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU3_To', symObjAddr: 0xD14, symBinAddr: 0x3F63C, symSize: 0x24 }
  - { offset: 0xE9C42, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU3_To', symObjAddr: 0xD14, symBinAddr: 0x3F63C, symSize: 0x24 }
  - { offset: 0xE9C8C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU4_To', symObjAddr: 0xD38, symBinAddr: 0x3F660, symSize: 0x24 }
  - { offset: 0xE9CA8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU4_To', symObjAddr: 0xD38, symBinAddr: 0x3F660, symSize: 0x24 }
  - { offset: 0xE9CF2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU5_To', symObjAddr: 0xD5C, symBinAddr: 0x3F684, symSize: 0x28 }
  - { offset: 0xE9D0E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU5_To', symObjAddr: 0xD5C, symBinAddr: 0x3F684, symSize: 0x28 }
  - { offset: 0xE9D58, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU6_To', symObjAddr: 0xD84, symBinAddr: 0x3F6AC, symSize: 0x28 }
  - { offset: 0xE9D74, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC27startSwiftUnhandExcHandling33_E1A96831138ED3F3E0A51B461F187E33LLyyFyyYbcfU_ys5Int32VcfU6_To', symObjAddr: 0xD84, symBinAddr: 0x3F6AC, symSize: 0x28 }
  - { offset: 0xE9DC6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterCfD', symObjAddr: 0xDAC, symBinAddr: 0x3F6D4, symSize: 0x10 }
  - { offset: 0xE9DEB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC012reportStoredB4Dump33_E1A96831138ED3F3E0A51B461F187E33LLyyFTf4d_n', symObjAddr: 0xF38, symBinAddr: 0x3F748, symSize: 0x200 }
  - { offset: 0xE9E45, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC5startyyFZTf4d_n', symObjAddr: 0x1138, symBinAddr: 0x3F948, symSize: 0x104 }
  - { offset: 0xE9EE3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13CrashReporterC29handleSwiftUnhandledException33_E1A96831138ED3F3E0A51B461F187E33LL16havingSignalCode03andR4Nameys5Int32V_SStFZTf4nnd_n', symObjAddr: 0x123C, symBinAddr: 0x3FA4C, symSize: 0x3C0 }
  - { offset: 0xEA4C8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC0B7DefaultSDySSypGvpZ', symObjAddr: 0x11C58, symBinAddr: 0xBA208, symSize: 0x0 }
  - { offset: 0xEA66F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC0B7Default_WZ', symObjAddr: 0x384, symBinAddr: 0x401E8, symSize: 0x1614 }
  - { offset: 0xEBF56, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigCMa', symObjAddr: 0x2D54, symBinAddr: 0x42BB8, symSize: 0x20 }
  - { offset: 0xEBF8E, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x2DC8, symBinAddr: 0x42BEC, symSize: 0xE4 }
  - { offset: 0xEC019, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSs_SiTg5', symObjAddr: 0x2EAC, symBinAddr: 0x42CD0, symSize: 0xF0 }
  - { offset: 0xEC092, size: 0x8, addend: 0x0, symName: '_$sSS8UTF8ViewV32withContiguousStorageIfAvailableyxSgxSRys5UInt8VGKXEKlFSiSg_Tg5', symObjAddr: 0x3218, symBinAddr: 0x4303C, symSize: 0xD4 }
  - { offset: 0xEC0E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_AM4DataVSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x3320, symBinAddr: 0x43144, symSize: 0x34 }
  - { offset: 0xEC0F9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_TA', symObjAddr: 0x3378, symBinAddr: 0x4319C, symSize: 0x8 }
  - { offset: 0xEC10D, size: 0x8, addend: 0x0, symName: '_$sS2sSTsWl', symObjAddr: 0x3474, symBinAddr: 0x431A4, symSize: 0x44 }
  - { offset: 0xEC121, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOwet', symObjAddr: 0x34C8, symBinAddr: 0x431E8, symSize: 0x90 }
  - { offset: 0xEC135, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOwst', symObjAddr: 0x3558, symBinAddr: 0x43278, symSize: 0xBC }
  - { offset: 0xEC149, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOMa', symObjAddr: 0x3628, symBinAddr: 0x43334, symSize: 0x10 }
  - { offset: 0xEC15D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASQWb', symObjAddr: 0x3638, symBinAddr: 0x43344, symSize: 0x4 }
  - { offset: 0xEC171, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOACSQAAWl', symObjAddr: 0x363C, symBinAddr: 0x43348, symSize: 0x44 }
  - { offset: 0xEC1D6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x1AC, symBinAddr: 0x40010, symSize: 0x88 }
  - { offset: 0xEC291, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH9hashValueSivgTW', symObjAddr: 0x234, symBinAddr: 0x40098, symSize: 0x60 }
  - { offset: 0xEC30F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x294, symBinAddr: 0x400F8, symSize: 0x40 }
  - { offset: 0xEC361, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x2D4, symBinAddr: 0x40138, symSize: 0x5C }
  - { offset: 0xEC530, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsEyxSgSScfCSi_Tgm5', symObjAddr: 0x29F0, symBinAddr: 0x42854, symSize: 0x24C }
  - { offset: 0xEC726, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x3FE64, symSize: 0x1AC }
  - { offset: 0xEC769, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x330, symBinAddr: 0x40194, symSize: 0x2C }
  - { offset: 0xEC792, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x35C, symBinAddr: 0x401C0, symSize: 0x28 }
  - { offset: 0xEC82E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctF', symObjAddr: 0x1998, symBinAddr: 0x417FC, symSize: 0x284 }
  - { offset: 0xEC91F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_AM4DataVSo13NSURLResponseCSgtcfU_', symObjAddr: 0x1C1C, symBinAddr: 0x41A80, symSize: 0xDD4 }
  - { offset: 0xECED2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigC5fetch12prodEndPoint0F3Key17isFallbackEnabled9andSendToySS_SSSbyAA0cD13FetchResponseO_SDySSypGSgtctFy10Foundation10URLRequestV_So7NSErrorCSgSStcfU0_', symObjAddr: 0x2C3C, symBinAddr: 0x42AA0, symSize: 0xDC }
  - { offset: 0xED091, size: 0x8, addend: 0x0, symName: '_$s8Razorpay16BaseRemoteConfigCfD', symObjAddr: 0x2D18, symBinAddr: 0x42B7C, symSize: 0x3C }
  - { offset: 0xED0D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay23BaseRemoteConfigOptionsO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x3680, symBinAddr: 0x4338C, symSize: 0x5C }
  - { offset: 0xED284, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tF', symObjAddr: 0x0, symBinAddr: 0x433E8, symSize: 0x37C }
  - { offset: 0xED2D4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tF', symObjAddr: 0x0, symBinAddr: 0x433E8, symSize: 0x37C }
  - { offset: 0xED36A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tFyyScMYccfU_Tf2ni_n', symObjAddr: 0x60C, symBinAddr: 0x439F4, symSize: 0x114 }
  - { offset: 0xED498, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC17paymentSuccessful7orderID16dictVerificationySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x37C, symBinAddr: 0x43764, symSize: 0x290 }
  - { offset: 0xED67B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGSgtFTf4dnnn_n', symObjAddr: 0x720, symBinAddr: 0x43B08, symSize: 0x360 }
  - { offset: 0xED8C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC19makeExternalSDKCall8withDictySDys11AnyHashableVypGSg_tFyyScMYccfU_Tf2ni_nTA', symObjAddr: 0xBD8, symBinAddr: 0x43E94, symSize: 0xC }
  - { offset: 0xED8D6, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xBE4, symBinAddr: 0x43EA0, symSize: 0x10 }
  - { offset: 0xED8EA, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xBF4, symBinAddr: 0x43EB0, symSize: 0x8 }
  - { offset: 0xEDC17, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDict_WZ', symObjAddr: 0x0, symBinAddr: 0x43EB8, symSize: 0x128 }
  - { offset: 0xEDC3B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDictSDySSypGvpZ', symObjAddr: 0x2F58, symBinAddr: 0xBA210, symSize: 0x0 }
  - { offset: 0xEDCB7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsC22InitalOtelfOptionsDict_WZ', symObjAddr: 0x0, symBinAddr: 0x43EB8, symSize: 0x128 }
  - { offset: 0xEDDF7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15OtpelfConstantsCMa', symObjAddr: 0x138, symBinAddr: 0x43FE0, symSize: 0x20 }
  - { offset: 0xEE044, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeWrapperCMa', symObjAddr: 0x18, symBinAddr: 0x44000, symSize: 0x20 }
  - { offset: 0xEE1D0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x44020, symSize: 0xDC }
  - { offset: 0xEE1F4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigC14sharedInstanceACvpZ', symObjAddr: 0xAA50, symBinAddr: 0xBA218, symSize: 0x0 }
  - { offset: 0xEE385, size: 0x8, addend: 0x0, symName: '_$sSD8RazorpayE5merge5otherySDyxq_G_tFSS_ypTg5', symObjAddr: 0xDC, symBinAddr: 0x440FC, symSize: 0x3E8 }
  - { offset: 0xEE599, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigC14sharedInstance_WZ', symObjAddr: 0x69C, symBinAddr: 0x446BC, symSize: 0x44 }
  - { offset: 0xEE5DF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCfE', symObjAddr: 0x6E0, symBinAddr: 0x44700, symSize: 0x1C }
  - { offset: 0xEE645, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCMa', symObjAddr: 0x748, symBinAddr: 0x44768, symSize: 0x20 }
  - { offset: 0xEE7A9, size: 0x8, addend: 0x0, symName: '_$sSS3key_yp5valuetSgWOb', symObjAddr: 0x1A94, symBinAddr: 0x45A38, symSize: 0x48 }
  - { offset: 0xEE7BD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOwet', symObjAddr: 0x1B44, symBinAddr: 0x45A80, symSize: 0x90 }
  - { offset: 0xEE7D1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOwst', symObjAddr: 0x1BD4, symBinAddr: 0x45B10, symSize: 0xBC }
  - { offset: 0xEE7E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOMa', symObjAddr: 0x1CA4, symBinAddr: 0x45BCC, symSize: 0x10 }
  - { offset: 0xEE7F9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASQWb', symObjAddr: 0x1CB4, symBinAddr: 0x45BDC, symSize: 0x4 }
  - { offset: 0xEE80D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOACSQAAWl', symObjAddr: 0x1CB8, symBinAddr: 0x45BE0, symSize: 0x44 }
  - { offset: 0xEE8A3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x4C4, symBinAddr: 0x444E4, symSize: 0x88 }
  - { offset: 0xEE95E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH9hashValueSivgTW', symObjAddr: 0x54C, symBinAddr: 0x4456C, symSize: 0x60 }
  - { offset: 0xEE9DC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x5AC, symBinAddr: 0x445CC, symSize: 0x40 }
  - { offset: 0xEEA2E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x5EC, symBinAddr: 0x4460C, symSize: 0x5C }
  - { offset: 0xEEBC2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueSSvg', symObjAddr: 0x0, symBinAddr: 0x44020, symSize: 0xDC }
  - { offset: 0xEEC18, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x648, symBinAddr: 0x44668, symSize: 0x2C }
  - { offset: 0xEEC41, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x674, symBinAddr: 0x44694, symSize: 0x28 }
  - { offset: 0xEECAC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCfD', symObjAddr: 0x6FC, symBinAddr: 0x4471C, symSize: 0x4C }
  - { offset: 0xEED51, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12RemoteConfigCACyc33_DF8BC766DBD29A885663B5F7C39C22B4LlfcTf4g_n', symObjAddr: 0x768, symBinAddr: 0x44788, symSize: 0x12B0 }
  - { offset: 0xEFB8B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19RemoteConfigOptionsO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x1CFC, symBinAddr: 0x45C24, symSize: 0x5C }
  - { offset: 0xEFD6A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11merchantKey33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLLSSvpZ', symObjAddr: 0x3160, symBinAddr: 0xA4930, symSize: 0x0 }
  - { offset: 0xEFD84, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC14sharedInstance33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLLACvpZ', symObjAddr: 0x3170, symBinAddr: 0xA4940, symSize: 0x0 }
  - { offset: 0xF023F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC14sharedInstance33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLL_WZ', symObjAddr: 0x200, symBinAddr: 0x45E80, symSize: 0x2C }
  - { offset: 0xF055B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCfETo', symObjAddr: 0x1330, symBinAddr: 0x46F78, symSize: 0x48 }
  - { offset: 0xF078F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCMa', symObjAddr: 0x2CF0, symBinAddr: 0x48938, symSize: 0x20 }
  - { offset: 0xF07A3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFyyScMYccfU_TA', symObjAddr: 0x2D84, symBinAddr: 0x4898C, symSize: 0x10 }
  - { offset: 0xF07B7, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2D94, symBinAddr: 0x4899C, symSize: 0x10 }
  - { offset: 0xF07CB, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2DA4, symBinAddr: 0x489AC, symSize: 0x8 }
  - { offset: 0xF07DF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay17SDKChecksProtocol_pSgWOd', symObjAddr: 0x2F00, symBinAddr: 0x489B4, symSize: 0x48 }
  - { offset: 0xF07F3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwCP', symObjAddr: 0x2F68, symBinAddr: 0x489FC, symSize: 0x2C }
  - { offset: 0xF0807, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwxx', symObjAddr: 0x2F94, symBinAddr: 0x48A28, symSize: 0x8 }
  - { offset: 0xF081B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwca', symObjAddr: 0x2F9C, symBinAddr: 0x48A30, symSize: 0x40 }
  - { offset: 0xF082F, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x2FDC, symBinAddr: 0x48A70, symSize: 0xC }
  - { offset: 0xF0843, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwta', symObjAddr: 0x2FE8, symBinAddr: 0x48A7C, symSize: 0x30 }
  - { offset: 0xF0857, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwet', symObjAddr: 0x3018, symBinAddr: 0x48AAC, symSize: 0x48 }
  - { offset: 0xF086B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVwst', symObjAddr: 0x3060, symBinAddr: 0x48AF4, symSize: 0x3C }
  - { offset: 0xF087F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVMa', symObjAddr: 0x309C, symBinAddr: 0x48B30, symSize: 0x10 }
  - { offset: 0xF0893, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFyyScMYccfU_TA', symObjAddr: 0x30D8, symBinAddr: 0x48B6C, symSize: 0x10 }
  - { offset: 0xF08A7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFyyScMYccfU_yycfU_TA', symObjAddr: 0x30F8, symBinAddr: 0x48B8C, symSize: 0x8 }
  - { offset: 0xF0A78, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutCACyc33_A7D97CF65CCE52FB62DCCEF29A0BBC3CLlfcTo', symObjAddr: 0x0, symBinAddr: 0x45C80, symSize: 0x74 }
  - { offset: 0xF0AC1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvgTo', symObjAddr: 0x74, symBinAddr: 0x45CF4, symSize: 0x48 }
  - { offset: 0xF0AF6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvg', symObjAddr: 0xBC, symBinAddr: 0x45D3C, symSize: 0x48 }
  - { offset: 0xF0B40, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvsTo', symObjAddr: 0x104, symBinAddr: 0x45D84, symSize: 0x64 }
  - { offset: 0xF0B7D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvs', symObjAddr: 0x168, symBinAddr: 0x45DE8, symSize: 0x50 }
  - { offset: 0xF0BA6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvM', symObjAddr: 0x1B8, symBinAddr: 0x45E38, symSize: 0x44 }
  - { offset: 0xF0BCB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvM.resume.0', symObjAddr: 0x1FC, symBinAddr: 0x45E7C, symSize: 0x4 }
  - { offset: 0xF0CB2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_11andDelegate17forViewControllerySS_AA0A25PaymentCompletionProtocol_pSo06UIViewJ0CtFZTo', symObjAddr: 0x230, symBinAddr: 0x45EAC, symSize: 0x4 }
  - { offset: 0xF0CC6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_11andDelegateACSS_AA0A8Protocol_ptFZ', symObjAddr: 0x234, symBinAddr: 0x45EB0, symSize: 0x28 }
  - { offset: 0xF0CDA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZ', symObjAddr: 0x270, symBinAddr: 0x45EEC, symSize: 0x18 }
  - { offset: 0xF0CF5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTo', symObjAddr: 0x288, symBinAddr: 0x45F04, symSize: 0x9C }
  - { offset: 0xF0D1F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4DataACSS_AA0a25PaymentCompletionProtocoldH0_ptFZ', symObjAddr: 0x324, symBinAddr: 0x45FA0, symSize: 0x28 }
  - { offset: 0xF0D33, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZ', symObjAddr: 0x3E4, symBinAddr: 0x46060, symSize: 0x18 }
  - { offset: 0xF0D4E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTo', symObjAddr: 0x3FC, symBinAddr: 0x46078, symSize: 0x84 }
  - { offset: 0xF0D88, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10publishUri4withySS_tFZ', symObjAddr: 0x480, symBinAddr: 0x460FC, symSize: 0x60 }
  - { offset: 0xF0DF9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10publishUri4withySS_tFZTo', symObjAddr: 0x4E0, symBinAddr: 0x4615C, symSize: 0x70 }
  - { offset: 0xF0E79, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pF', symObjAddr: 0x550, symBinAddr: 0x461CC, symSize: 0x48 }
  - { offset: 0xF0EFB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pFTo', symObjAddr: 0x598, symBinAddr: 0x46214, symSize: 0x48 }
  - { offset: 0xF0F13, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pFTo', symObjAddr: 0x598, symBinAddr: 0x46214, symSize: 0x48 }
  - { offset: 0xF0FC0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayControllerySDys11AnyHashableVypG_So06UIViewE0CtF', symObjAddr: 0x5E0, symBinAddr: 0x4625C, symSize: 0x7C }
  - { offset: 0xF1063, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayControllerySDys11AnyHashableVypG_So06UIViewE0CtFTo', symObjAddr: 0x65C, symBinAddr: 0x462D8, symSize: 0xE8 }
  - { offset: 0xF1164, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4openyySDys11AnyHashableVypGF', symObjAddr: 0x744, symBinAddr: 0x463C0, symSize: 0x188 }
  - { offset: 0xF128D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4openyySDys11AnyHashableVypGFTo', symObjAddr: 0x8CC, symBinAddr: 0x46548, symSize: 0x70 }
  - { offset: 0xF12A1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayController26arrExternalPaymentEntitiesySDys11AnyHashableVypG_So06UIViewE0CSayAA06PluginH8Delegate_pGtF', symObjAddr: 0x93C, symBinAddr: 0x465B8, symSize: 0x19C }
  - { offset: 0xF13C7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_17displayController26arrExternalPaymentEntitiesySDys11AnyHashableVypG_So06UIViewE0CSayAA06PluginH8Delegate_pGtFTo', symObjAddr: 0xAD8, symBinAddr: 0x46754, symSize: 0xC0 }
  - { offset: 0xF13DB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZ', symObjAddr: 0xB98, symBinAddr: 0x46814, symSize: 0x4 }
  - { offset: 0xF1437, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZTo', symObjAddr: 0xB9C, symBinAddr: 0x46818, symSize: 0x2C }
  - { offset: 0xF1461, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_26arrExternalPaymentEntitiesySDys11AnyHashableVypG_SayAA06PluginF8Delegate_pGtF', symObjAddr: 0xBC8, symBinAddr: 0x46844, symSize: 0x8C }
  - { offset: 0xF1531, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC4open_26arrExternalPaymentEntitiesySDys11AnyHashableVypG_SayAA06PluginF8Delegate_pGtFTo', symObjAddr: 0xC54, symBinAddr: 0x468D0, symSize: 0x104 }
  - { offset: 0xF15DA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptF', symObjAddr: 0xD58, symBinAddr: 0x469D4, symSize: 0x4 }
  - { offset: 0xF15EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFyyScMYccfU_', symObjAddr: 0xD5C, symBinAddr: 0x469D8, symSize: 0x378 }
  - { offset: 0xF17EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFTo', symObjAddr: 0x10D4, symBinAddr: 0x46D50, symSize: 0x9C }
  - { offset: 0xF1818, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC5closeyyF', symObjAddr: 0x1170, symBinAddr: 0x46DEC, symSize: 0x4C }
  - { offset: 0xF1860, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC5closeyyFTo', symObjAddr: 0x11BC, symBinAddr: 0x46E38, symSize: 0x68 }
  - { offset: 0xF18B6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC13clearUserDatayyF', symObjAddr: 0x1224, symBinAddr: 0x46EA0, symSize: 0x60 }
  - { offset: 0xF1936, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC13clearUserDatayyFTo', symObjAddr: 0x1284, symBinAddr: 0x46F00, symSize: 0x78 }
  - { offset: 0xF1A06, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtF', symObjAddr: 0x1378, symBinAddr: 0x46FC0, symSize: 0x284 }
  - { offset: 0xF1A74, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFyyScMYccfU_', symObjAddr: 0x15FC, symBinAddr: 0x47244, symSize: 0x2CC }
  - { offset: 0xF1BA2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerV17validationResults14sdkCheckPoints17continueToPaymentySayAA0hI0VG_SbtFyyScMYccfU_yycfU_', symObjAddr: 0x18C8, symBinAddr: 0x47510, symSize: 0xB0 }
  - { offset: 0xF1CBC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay28OpinionatedChecksInitializerVAA17SDKChecksProtocolA2aDP17validationResults14sdkCheckPoints17continueToPaymentySayAA0jK0VG_SbtFTW', symObjAddr: 0x1978, symBinAddr: 0x475C0, symSize: 0x8 }
  - { offset: 0xF1D1C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC13sdkValidation17continueToPaymentAcA17SDKChecksProtocol_p_SbSgtcfcTf4enn_nAA08InternalA0C_Tg5', symObjAddr: 0x1980, symBinAddr: 0x475C8, symSize: 0x5B0 }
  - { offset: 0xF211A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24OpinionatedChecksHandlerC13sdkValidation17continueToPaymentAcA17SDKChecksProtocol_p_SbSgtcfcTf4enn_nAA0bC11InitializerV_Tg5', symObjAddr: 0x1F30, symBinAddr: 0x47B78, symSize: 0x5A4 }
  - { offset: 0xF2531, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data0F16HostedOptiConfigACSS_AA0a25PaymentCompletionProtocoldH0_pSDyS2SGtFZTf4nnnd_g', symObjAddr: 0x2554, symBinAddr: 0x4819C, symSize: 0xDC }
  - { offset: 0xF259F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZTf4nnnd_g', symObjAddr: 0x2704, symBinAddr: 0x4834C, symSize: 0x1A0 }
  - { offset: 0xF261B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZTf4nd_n', symObjAddr: 0x28A4, symBinAddr: 0x484EC, symSize: 0x24C }
  - { offset: 0xF266A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptFTf4nnnd_n', symObjAddr: 0x2AF0, symBinAddr: 0x48738, symSize: 0x200 }
  - { offset: 0xF286B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZySPyytGXEfU_', symObjAddr: 0x0, symBinAddr: 0x48BA8, symSize: 0xEC }
  - { offset: 0xF2C2E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperCMa', symObjAddr: 0x1FC, symBinAddr: 0x48D94, symSize: 0x20 }
  - { offset: 0xF2CD0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_TA', symObjAddr: 0xB18, symBinAddr: 0x496B0, symSize: 0x78 }
  - { offset: 0xF2D21, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_TA', symObjAddr: 0xBD4, symBinAddr: 0x49728, symSize: 0x80 }
  - { offset: 0xF2D6A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_TA', symObjAddr: 0xC54, symBinAddr: 0x497A8, symSize: 0x7C }
  - { offset: 0xF2DBB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_ApRXEfU_TA', symObjAddr: 0xCD0, symBinAddr: 0x49824, symSize: 0x3C }
  - { offset: 0xF2DCF, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV8IteratorVAEStAAWl', symObjAddr: 0xD50, symBinAddr: 0x49860, symSize: 0x48 }
  - { offset: 0xF2DE3, size: 0x8, addend: 0x0, symName: '_$ss5UInt8VABSzsWl', symObjAddr: 0xD98, symBinAddr: 0x498A8, symSize: 0x44 }
  - { offset: 0xF2DF7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZySPyytGXEfU_TA', symObjAddr: 0xE20, symBinAddr: 0x498EC, symSize: 0x1C }
  - { offset: 0xF2E0B, size: 0x8, addend: 0x0, symName: '_$sSays5UInt8VGSayxGSTsWl', symObjAddr: 0xE7C, symBinAddr: 0x49908, symSize: 0x4C }
  - { offset: 0xF2E3B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZySPyytGXEfU_', symObjAddr: 0x0, symBinAddr: 0x48BA8, symSize: 0xEC }
  - { offset: 0xF2F8A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZs5Int32VSpyytGXEfU_APSPyytGXEfU_ApRXEfU_ApRXEfU_', symObjAddr: 0xFC, symBinAddr: 0x48CA4, symSize: 0xE0 }
  - { offset: 0xF309A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC13getSha256Hash5input10Foundation4DataVSS_tFZTf4nd_n', symObjAddr: 0x21C, symBinAddr: 0x48DB4, symSize: 0x188 }
  - { offset: 0xF3189, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC22convertDataToHexString4dataSS10Foundation0E0V_tFZTf4nd_n', symObjAddr: 0x3A4, symBinAddr: 0x48F3C, symSize: 0x240 }
  - { offset: 0xF3228, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC15validateKeySize33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL3keySb10Foundation4DataV_tFZTf4nd_n', symObjAddr: 0x5E4, symBinAddr: 0x4917C, symSize: 0x150 }
  - { offset: 0xF329B, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV5countACSi_tcfCTf4nd_n', symObjAddr: 0x744, symBinAddr: 0x492DC, symSize: 0x9C }
  - { offset: 0xF32E5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12CryptoHelperC20performaAesOperation33_C99FBFCF6D1A0CD6FCF3723A5B9E9751LL5input3key2iv9isCbcMode9operation10Foundation4DataVSgAM_A2MSbSitFZTf4nnnnnd_n', symObjAddr: 0x7E0, symBinAddr: 0x49378, symSize: 0x308 }
  - { offset: 0xF384D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageV05buildbcdE0012fromWKScriptE0ACSgSo0hE0C_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x49954, symSize: 0x294 }
  - { offset: 0xF3927, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwxx', symObjAddr: 0x380, symBinAddr: 0x49BE8, symSize: 0x30 }
  - { offset: 0xF393B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwcp', symObjAddr: 0x3B0, symBinAddr: 0x49C18, symSize: 0x54 }
  - { offset: 0xF394F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwca', symObjAddr: 0x404, symBinAddr: 0x49C6C, symSize: 0x84 }
  - { offset: 0xF3963, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x488, symBinAddr: 0x49CF0, symSize: 0x14 }
  - { offset: 0xF3977, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwta', symObjAddr: 0x49C, symBinAddr: 0x49D04, symSize: 0x54 }
  - { offset: 0xF398B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwet', symObjAddr: 0x4F0, symBinAddr: 0x49D58, symSize: 0x5C }
  - { offset: 0xF399F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVwst', symObjAddr: 0x54C, symBinAddr: 0x49DB4, symSize: 0x5C }
  - { offset: 0xF39B3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageVMa', symObjAddr: 0x5A8, symBinAddr: 0x49E10, symSize: 0x10 }
  - { offset: 0xF3A39, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20WebViewBridgeMessageV05buildbcdE0012fromWKScriptE0ACSgSo0hE0C_tFZTf4nd_n', symObjAddr: 0x0, symBinAddr: 0x49954, symSize: 0x294 }
  - { offset: 0xF3D72, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xA8C, symBinAddr: 0x4A870, symSize: 0x10 }
  - { offset: 0xF3D86, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xA9C, symBinAddr: 0x4A880, symSize: 0x8 }
  - { offset: 0xF3D9A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_TA', symObjAddr: 0xAA4, symBinAddr: 0x4A888, symSize: 0xC }
  - { offset: 0xF3DAE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_yycfU_TA', symObjAddr: 0xAD4, symBinAddr: 0x4A8B8, symSize: 0x24 }
  - { offset: 0xF3DE0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_TA', symObjAddr: 0xB24, symBinAddr: 0x4A908, symSize: 0xC }
  - { offset: 0xF3DF4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_ySbcfU0_TA', symObjAddr: 0xB94, symBinAddr: 0x4A978, symSize: 0x40 }
  - { offset: 0xF3EFD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_', symObjAddr: 0x0, symBinAddr: 0x49E20, symSize: 0x188 }
  - { offset: 0xF3F4A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZySbcfU0_ySbcfU0_', symObjAddr: 0x188, symBinAddr: 0x49FA8, symSize: 0x188 }
  - { offset: 0xF403B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay5ToastO4show8withText11forDuration8onWindowySS_ACSo8UIWindowCSgtFZTf4nnnd_n', symObjAddr: 0x34C, symBinAddr: 0x4A130, symSize: 0x6F8 }
  - { offset: 0xF473B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsCMa', symObjAddr: 0x668, symBinAddr: 0x4B044, symSize: 0x20 }
  - { offset: 0xF51E7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath03althI0011withDefaultF00k10LiveReloadF008fallBacklC0xSgAA0bC7OptionsO_ALSgAJS2btlFZSS_Tgm5', symObjAddr: 0x0, symBinAddr: 0x4A9EC, symSize: 0x360 }
  - { offset: 0xF53CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson12ofDictionarySDySSypGSDys11AnyHashableVypG_tFZ', symObjAddr: 0x360, symBinAddr: 0x4AD4C, symSize: 0x4 }
  - { offset: 0xF53E0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34appropriatePaymentEntityDictionary011arrExternalE8EntitiesSDys11AnyHashableVypGSayAA06PluginE8Delegate_pGSg_tFZyAaI_pXEfU_', symObjAddr: 0x364, symBinAddr: 0x4AD50, symSize: 0x2F4 }
  - { offset: 0xF5544, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson7ofArraySayypGSayypSgG_tFZTf4nd_n', symObjAddr: 0x688, symBinAddr: 0x4B064, symSize: 0x584 }
  - { offset: 0xF5984, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC32validateAndEditPropertiesForJson12ofDictionarySDySSypGSDys11AnyHashableVypG_tFZTf4nd_n', symObjAddr: 0xC0C, symBinAddr: 0x4B5E8, symSize: 0x8C0 }
  - { offset: 0xF5D30, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC10filterInfoySDys11AnyHashableVypGAGFZTf4nd_n', symObjAddr: 0x14CC, symBinAddr: 0x4BEA8, symSize: 0x74C }
  - { offset: 0xF6236, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC22isCurrentVersionLatest07currentF0SbSS_tKFZTf4nd_n', symObjAddr: 0x1D34, symBinAddr: 0x4C5F4, symSize: 0x6A4 }
  - { offset: 0xF67EB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34checkAndShowUpdateSDKToastForDebug8onWindow15withMerchantKeyySo8UIWindowCSg_SStFZTf4nnd_n', symObjAddr: 0x23D8, symBinAddr: 0x4CC98, symSize: 0x424 }
  - { offset: 0xF6A2D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC18isMessageAuthentic7messageSbSo08WKScriptE0C_tFZTf4nd_n', symObjAddr: 0x27FC, symBinAddr: 0x4D0BC, symSize: 0x46C }
  - { offset: 0xF6B0D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC21convertColorToUIColor9hexStringSo0G0CSgSS_tFZTf4nd_n', symObjAddr: 0x2C68, symBinAddr: 0x4D528, symSize: 0x2D4 }
  - { offset: 0xF6CAF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A13UtilFunctionsC34appropriatePaymentEntityDictionary011arrExternalE8EntitiesSDys11AnyHashableVypGSayAA06PluginE8Delegate_pGSg_tFZTf4nd_n', symObjAddr: 0x2F3C, symBinAddr: 0x4D7FC, symSize: 0x270 }
  - { offset: 0xF70A9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay24SwiftCompatibilityHelperCMa', symObjAddr: 0x10, symBinAddr: 0x4DA6C, symSize: 0x20 }
  - { offset: 0xF7249, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOwet', symObjAddr: 0x10, symBinAddr: 0x4DA8C, symSize: 0x90 }
  - { offset: 0xF725D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOwst', symObjAddr: 0xA0, symBinAddr: 0x4DB1C, symSize: 0xBC }
  - { offset: 0xF7271, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOMa', symObjAddr: 0x160, symBinAddr: 0x4DBD8, symSize: 0x10 }
  - { offset: 0xF7285, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOwst', symObjAddr: 0x200, symBinAddr: 0x4DBE8, symSize: 0xBC }
  - { offset: 0xF7299, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOMa', symObjAddr: 0x2D0, symBinAddr: 0x4DCA4, symSize: 0x10 }
  - { offset: 0xF73AE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASQWb', symObjAddr: 0x112C, symBinAddr: 0x4EB00, symSize: 0x4 }
  - { offset: 0xF73C2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOACSQAAWl', symObjAddr: 0x1130, symBinAddr: 0x4EB04, symSize: 0x44 }
  - { offset: 0xF73EC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASQWb', symObjAddr: 0x13A4, symBinAddr: 0x4ED20, symSize: 0x4 }
  - { offset: 0xF7400, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOACSQAAWl', symObjAddr: 0x13A8, symBinAddr: 0x4ED24, symSize: 0x44 }
  - { offset: 0xF7422, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xEEC, symBinAddr: 0x4E8C0, symSize: 0xC }
  - { offset: 0xF7489, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH9hashValueSivgTW', symObjAddr: 0xEF8, symBinAddr: 0x4E8CC, symSize: 0x9C }
  - { offset: 0xF753D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xF94, symBinAddr: 0x4E968, symSize: 0x78 }
  - { offset: 0xF75AD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x100C, symBinAddr: 0x4E9E0, symSize: 0x98 }
  - { offset: 0xF7663, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x11CC, symBinAddr: 0x4EB48, symSize: 0x88 }
  - { offset: 0xF771E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH9hashValueSivgTW', symObjAddr: 0x1254, symBinAddr: 0x4EBD0, symSize: 0x60 }
  - { offset: 0xF779C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x12B4, symBinAddr: 0x4EC30, symSize: 0x40 }
  - { offset: 0xF77EE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x12F4, symBinAddr: 0x4EC70, symSize: 0x5C }
  - { offset: 0xF7860, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventO8rawValueSSvg', symObjAddr: 0x2E0, symBinAddr: 0x4DCB4, symSize: 0xC0C }
  - { offset: 0xF78BA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x10A4, symBinAddr: 0x4EA78, symSize: 0x2C }
  - { offset: 0xF78E3, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x10D0, symBinAddr: 0x4EAA4, symSize: 0x5C }
  - { offset: 0xF7931, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x1350, symBinAddr: 0x4ECCC, symSize: 0x2C }
  - { offset: 0xF795A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x137C, symBinAddr: 0x4ECF8, symSize: 0x28 }
  - { offset: 0xF7974, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10ErrorLevelO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x13EC, symBinAddr: 0x4ED68, symSize: 0x5C }
  - { offset: 0xF79AB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay14AnalyticsEventO8rawValueACSgSS_tcfCTf4nd_n', symObjAddr: 0x1448, symBinAddr: 0x4EDC4, symSize: 0x458 }
  - { offset: 0xF7BB7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12OtpelfBridgeCMa', symObjAddr: 0x10, symBinAddr: 0x4F224, symSize: 0x20 }
  - { offset: 0xF7BCB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeCMa', symObjAddr: 0x30, symBinAddr: 0x4F244, symSize: 0x20 }
  - { offset: 0xF81B6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC14setValueForKey33_8B3A70AEAD4AD830A8BC74BF45B8FC5FLL10fromParams9classNameySayypG_SStFTf4nnd_n', symObjAddr: 0x50, symBinAddr: 0x4F264, symSize: 0x700 }
  - { offset: 0xF8570, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC8getValue33_8B3A70AEAD4AD830A8BC74BF45B8FC5FLL10fromParams9className10andWebView0S10CallBackIdySayypG_SSSo05WKWebU0CSSSgtFTf4nnnnd_n', symObjAddr: 0x750, symBinAddr: 0x4F964, symSize: 0x918 }
  - { offset: 0xF8BF1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13StorageBridgeC15executeFunction_10withParams16havingCallBackId8sentFromySS_SayypGSgSSSgSo9WKWebViewCtFTf4nnnnd_n', symObjAddr: 0x1068, symBinAddr: 0x5027C, symSize: 0x5EC }
  - { offset: 0xF8EA6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay12OtpelfBridgeC15executeFunction_10withParams16havingCallBackId8sentFromySS_SayypGSgSSSgSo9WKWebViewCtFTf4nnddd_n', symObjAddr: 0x1654, symBinAddr: 0x50868, symSize: 0xF54 }
  - { offset: 0xF9ADC, size: 0x8, addend: 0x0, symName: '_$sypSgs5Error_pSgIeghng_yXlSgSo7NSErrorCSgIeyBhyy_TR', symObjAddr: 0x0, symBinAddr: 0x517C0, symSize: 0x9C }
  - { offset: 0xF9B03, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_7didFail9withErrorySo05WKWebE0C_So12WKNavigationCSgs0I0_ptFTo', symObjAddr: 0xA0, symBinAddr: 0x51860, symSize: 0x88 }
  - { offset: 0xF9B44, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_28didFailProvisionalNavigation9withErrorySo05WKWebE0C_So12WKNavigationCSgs0K0_ptFTo', symObjAddr: 0x128, symBinAddr: 0x518E8, symSize: 0x8C }
  - { offset: 0xF9B81, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFyypSg_s5Error_pSgtYbScMYccfU_', symObjAddr: 0x1B4, symBinAddr: 0x51974, symSize: 0x290 }
  - { offset: 0xF9C49, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTo', symObjAddr: 0x444, symBinAddr: 0x51C04, symSize: 0x68 }
  - { offset: 0xF9C8A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_15decidePolicyFor15decisionHandlerySo05WKWebE0C_So18WKNavigationActionCySo0lmG0VctFTo', symObjAddr: 0x4AC, symBinAddr: 0x51C6C, symSize: 0x94 }
  - { offset: 0xF9CBC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo11UITextFieldCcfU_', symObjAddr: 0x694, symBinAddr: 0x51E54, symSize: 0x60 }
  - { offset: 0xF9D86, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU0_', symObjAddr: 0x744, symBinAddr: 0x51F00, symSize: 0x18C }
  - { offset: 0xF9F1F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFTo', symObjAddr: 0x8D0, symBinAddr: 0x5208C, symSize: 0x110 }
  - { offset: 0xF9F51, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgIeyBy_SSSgIegg_TR', symObjAddr: 0x9E0, symBinAddr: 0x5219C, symSize: 0x44 }
  - { offset: 0xF9F69, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgIeyBy_SSSgIegg_TRTA', symObjAddr: 0xA48, symBinAddr: 0x52204, symSize: 0x8 }
  - { offset: 0xFA106, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_7didFail9withErrorySo05WKWebE0C_So12WKNavigationCSgs0I0_ptFTf4ddnn_n', symObjAddr: 0xA50, symBinAddr: 0x5220C, symSize: 0x1B0C }
  - { offset: 0xFA964, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_28didFailProvisionalNavigation9withErrorySo05WKWebE0C_So12WKNavigationCSgs0K0_ptFTf4ndnn_n', symObjAddr: 0x255C, symBinAddr: 0x53D18, symSize: 0x55C }
  - { offset: 0xFAC4C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x2AB8, symBinAddr: 0x54274, symSize: 0xA5C }
  - { offset: 0xFB04A, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_15decidePolicyFor15decisionHandlerySo05WKWebE0C_So18WKNavigationActionCySo0lmG0VctF06$sSo24lmG16VIeyBy_ABIegy_TRALIeyBy_Tf1nncn_nTf4dnng_n', symObjAddr: 0x3514, symBinAddr: 0x54CD0, symSize: 0x574 }
  - { offset: 0xFB2AC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_34runJavaScriptAlertPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCyyctFTf4dndnn_n', symObjAddr: 0x3A88, symBinAddr: 0x55244, symSize: 0x1A8 }
  - { offset: 0xFB32C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_36runJavaScriptConfirmPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCySbctFTf4dndnn_n', symObjAddr: 0x3C30, symBinAddr: 0x553EC, symSize: 0x284 }
  - { offset: 0xFB3B9, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFTf4dnndnn_n', symObjAddr: 0x3EB4, symBinAddr: 0x55670, symSize: 0x324 }
  - { offset: 0xFB456, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo11UITextFieldCcfU_TA', symObjAddr: 0x41FC, symBinAddr: 0x559B8, symSize: 0x8 }
  - { offset: 0xFB46A, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x4204, symBinAddr: 0x559C0, symSize: 0x10 }
  - { offset: 0xFB47E, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4214, symBinAddr: 0x559D0, symSize: 0x8 }
  - { offset: 0xFB492, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU0_TA', symObjAddr: 0x4250, symBinAddr: 0x55A0C, symSize: 0x10 }
  - { offset: 0xFB4CC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_37runJavaScriptTextInputPanelWithPrompt07defaultI016initiatedByFrame17completionHandlerySo05WKWebE0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU1_TA', symObjAddr: 0x4284, symBinAddr: 0x55A40, symSize: 0x38 }
  - { offset: 0xFB500, size: 0x8, addend: 0x0, symName: '_$sSo11UITextFieldCMa', symObjAddr: 0x42BC, symBinAddr: 0x55A78, symSize: 0x3C }
  - { offset: 0xFB51F, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x42F8, symBinAddr: 0x55AB4, symSize: 0x14 }
  - { offset: 0xFB553, size: 0x8, addend: 0x0, symName: '_$sIeyB_Ieg_TRTA', symObjAddr: 0x435C, symBinAddr: 0x55B18, symSize: 0xC }
  - { offset: 0xFB5A2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_34runJavaScriptAlertPanelWithMessage16initiatedByFrame17completionHandlerySo05WKWebE0C_SSSo11WKFrameInfoCyyctFySo13UIAlertActionCcfU_TA', symObjAddr: 0x4368, symBinAddr: 0x55B24, symSize: 0x30 }
  - { offset: 0xFB5D6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A10CheckoutVCC7webView_9didFinishySo05WKWebE0C_So12WKNavigationCSgtFyypSg_s5Error_pSgtYbScMYccfU_TA', symObjAddr: 0x4488, symBinAddr: 0x55B78, symSize: 0x8 }
  - { offset: 0xFBBEC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC14sharedInstanceACvpZ', symObjAddr: 0x2E828, symBinAddr: 0xBA220, symSize: 0x0 }
  - { offset: 0xFBFE7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC14sharedInstance_WZ', symObjAddr: 0x2368, symBinAddr: 0x57F2C, symSize: 0x48 }
  - { offset: 0xFC0BD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataCMa', symObjAddr: 0x3B44, symBinAddr: 0x59708, symSize: 0x20 }
  - { offset: 0xFC0D1, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x3BA4, symBinAddr: 0x59728, symSize: 0x50 }
  - { offset: 0xFC111, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x3C40, symBinAddr: 0x59778, symSize: 0x144 }
  - { offset: 0xFC17F, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x3D84, symBinAddr: 0x598BC, symSize: 0xEC }
  - { offset: 0xFC1A4, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgmq5', symObjAddr: 0x3E70, symBinAddr: 0x599A8, symSize: 0x64 }
  - { offset: 0xFC1DA, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x3ED4, symBinAddr: 0x59A0C, symSize: 0x210 }
  - { offset: 0xFC213, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x40E4, symBinAddr: 0x59C1C, symSize: 0x78 }
  - { offset: 0xFC22B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x4188, symBinAddr: 0x59CC0, symSize: 0xC }
  - { offset: 0xFC23F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_TA', symObjAddr: 0x4204, symBinAddr: 0x59D00, symSize: 0x10 }
  - { offset: 0xFC253, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x4214, symBinAddr: 0x59D10, symSize: 0x10 }
  - { offset: 0xFC267, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4224, symBinAddr: 0x59D20, symSize: 0x8 }
  - { offset: 0xFC325, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_yyScMYcXEfU_TA', symObjAddr: 0x4D7C, symBinAddr: 0x5A7A0, symSize: 0xC }
  - { offset: 0xFC339, size: 0x8, addend: 0x0, symName: '_$sS2sSysWl', symObjAddr: 0x4DE4, symBinAddr: 0x5A7BC, symSize: 0x44 }
  - { offset: 0xFC34D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyFy10Foundation10URLRequestV_AF0C0VSo13NSURLResponseCSgtcfU_TA', symObjAddr: 0x4F90, symBinAddr: 0x5A850, symSize: 0x8 }
  - { offset: 0xFC55A, size: 0x8, addend: 0x0, symName: '_$sSlsSQ7ElementRpzrlE10firstIndex2of0C0QzSgAB_tFSS_Tg5', symObjAddr: 0x227C, symBinAddr: 0x57E40, symSize: 0xEC }
  - { offset: 0xFC6F7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSS_Tg5', symObjAddr: 0x0, symBinAddr: 0x55BC4, symSize: 0x838 }
  - { offset: 0xFCADF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSb_Tg5', symObjAddr: 0x838, symBinAddr: 0x563FC, symSize: 0x820 }
  - { offset: 0xFCEC7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue33_8AAB6EF7C39E0A44F2F0F5CD6597C5E7LL8fromDict10forKeyPath03altuV0015fallBackDefaultC0xSgSDySSypG_S2SSgSbtlFSi_Tg5', symObjAddr: 0x1058, symBinAddr: 0x56C1C, symSize: 0x838 }
  - { offset: 0xFD364, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSS_Tgm5', symObjAddr: 0x1890, symBinAddr: 0x57454, symSize: 0x360 }
  - { offset: 0xFD58D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSb_Tgm5', symObjAddr: 0x1BF0, symBinAddr: 0x577B4, symSize: 0x344 }
  - { offset: 0xFD7B8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18RemoteConfigHelperC8getValue10forKeyPath011withDefaultF003altF00j10LiveReloadF008fallBackkC0xSgAA04BasebC7OptionsO_AjLSgS2btlFZSi_Tgm5', symObjAddr: 0x1F34, symBinAddr: 0x57AF8, symSize: 0x348 }
  - { offset: 0xFD9F2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC5startyyF', symObjAddr: 0x23B0, symBinAddr: 0x57F74, symSize: 0x180 }
  - { offset: 0xFDAB8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC5startyyF6$deferL_yyF', symObjAddr: 0x2530, symBinAddr: 0x580F4, symSize: 0x128 }
  - { offset: 0xFDC0E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyF', symObjAddr: 0x2658, symBinAddr: 0x5821C, symSize: 0x414 }
  - { offset: 0xFDE38, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC15checkForUpdates33_C969CD0145D9EE45921CEF1FF36E8712LLyyFy10Foundation10URLRequestV_AF0C0VSo13NSURLResponseCSgtcfU_', symObjAddr: 0x2A6C, symBinAddr: 0x58630, symSize: 0x500 }
  - { offset: 0xFDF76, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tF', symObjAddr: 0x2F90, symBinAddr: 0x58B54, symSize: 0x3FC }
  - { offset: 0xFE199, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_', symObjAddr: 0x338C, symBinAddr: 0x58F50, symSize: 0x334 }
  - { offset: 0xFE260, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_', symObjAddr: 0x36C0, symBinAddr: 0x59284, symSize: 0x258 }
  - { offset: 0xFE31B, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC06updateB033_C969CD0145D9EE45921CEF1FF36E8712LL07withNewB4HashySS_tFy10Foundation10URLRequestV_AG0C0VSo13NSURLResponseCSgtcfU_yyYbcfU_yyScMYcXEfU_', symObjAddr: 0x3918, symBinAddr: 0x594DC, symSize: 0x114 }
  - { offset: 0xFE3F7, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataCfD', symObjAddr: 0x3B20, symBinAddr: 0x596E4, symSize: 0x24 }
  - { offset: 0xFE44D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC014fetchEncryptedB7AndHash33_C969CD0145D9EE45921CEF1FF36E8712LLSS_SStyFTf4d_n', symObjAddr: 0x4304, symBinAddr: 0x59D28, symSize: 0x2C0 }
  - { offset: 0xFE560, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC07decryptB6String8inBase64S2S_tFZTf4nd_n', symObjAddr: 0x45C4, symBinAddr: 0x59FE8, symSize: 0x690 }
  - { offset: 0xFE7BF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay10OtpelfDataC03getB2JsSSyFTf4n_g', symObjAddr: 0x4C54, symBinAddr: 0x5A678, symSize: 0xFC }
  - { offset: 0xFECE0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCMa', symObjAddr: 0x540, symBinAddr: 0x5ADA0, symSize: 0x20 }
  - { offset: 0xFED0E, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo19WKWebsiteDataRecordC_Tgm5', symObjAddr: 0x600, symBinAddr: 0x5AE60, symSize: 0x64 }
  - { offset: 0xFED3B, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlF8Razorpay21PluginPaymentDelegate_p_Tgm5', symObjAddr: 0x664, symBinAddr: 0x5AEC4, symSize: 0xC }
  - { offset: 0xFED53, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo18NSLayoutConstraintC_Tgm5', symObjAddr: 0x670, symBinAddr: 0x5AED0, symSize: 0x64 }
  - { offset: 0xFED8B, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0x904, symBinAddr: 0x5B164, symSize: 0x1DC }
  - { offset: 0xFEDEC, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFypSg_Tg5', symObjAddr: 0xAF4, symBinAddr: 0x5B354, symSize: 0x1C }
  - { offset: 0xFEE04, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0xB10, symBinAddr: 0x5B370, symSize: 0x1C }
  - { offset: 0xFEE74, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFypSg_Tg5', symObjAddr: 0xB2C, symBinAddr: 0x5B38C, symSize: 0x120 }
  - { offset: 0xFEFB8, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF8Razorpay21PluginPaymentDelegate_p_Tg5', symObjAddr: 0xC4C, symBinAddr: 0x5B4AC, symSize: 0x128 }
  - { offset: 0xFF1FE, size: 0x8, addend: 0x0, symName: '_$ss14_ArrayProtocolPsE6filterySay7ElementQzGSbAEKXEKFSay8Razorpay21PluginPaymentDelegate_pG_Tg5013$s8Razorpay20fg30ManagerC07processC0yyFSbAA0bC8H7_pXEfU_AG0fG5ModelCTf1cn_nTf4gg_n', symObjAddr: 0xD74, symBinAddr: 0x5B5D4, symSize: 0x1A4 }
  - { offset: 0xFF3FC, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC17analyticsInstanceAA13AnalyticsUtilCSgvg', symObjAddr: 0x0, symBinAddr: 0x5A860, symSize: 0x100 }
  - { offset: 0xFF4A8, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC07processC0yyF', symObjAddr: 0x100, symBinAddr: 0x5A960, symSize: 0x2A0 }
  - { offset: 0xFF7B5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC10trackEvent5event14withPropertiesySS_SDys11AnyHashableVypGSgtF', symObjAddr: 0x3A0, symBinAddr: 0x5AC00, symSize: 0x14C }
  - { offset: 0xFF8BB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCfD', symObjAddr: 0x4EC, symBinAddr: 0x5AD4C, symSize: 0x54 }
  - { offset: 0xFF925, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP17paymentSuccessful7orderID16dictVerificationySS_SDys11AnyHashableVypGSgtFTW', symObjAddr: 0x560, symBinAddr: 0x5ADC0, symSize: 0x8C }
  - { offset: 0xFF980, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGtFTW', symObjAddr: 0x5EC, symBinAddr: 0x5AE4C, symSize: 0x10 }
  - { offset: 0xFF9AA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerCAA0bC18CompletionDelegateA2aDP10trackEvent5event14withPropertiesySS_SDys11AnyHashableVypGSgtFTW', symObjAddr: 0x5FC, symBinAddr: 0x5AE5C, symSize: 0x4 }
  - { offset: 0xFF9CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay20PluginPaymentManagerC13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGtFTf4dnnn_n', symObjAddr: 0xF18, symBinAddr: 0x5B778, symSize: 0x8C }
  - { offset: 0xFFC1C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC5track_16havingProperties6sentByySS_SDySSypGSgAA16RzpWebViewBridgeOtF', symObjAddr: 0x0, symBinAddr: 0x5B804, symSize: 0x294 }
  - { offset: 0xFFD19, size: 0x8, addend: 0x0, symName: '_$s8Razorpay13AnalyticsUtilC5track_16havingProperties6sentByySS_SDySSypGSgAA16RzpWebViewBridgeOtF', symObjAddr: 0x0, symBinAddr: 0x5B804, symSize: 0x294 }
  - { offset: 0x1002BE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCfETo', symObjAddr: 0x96C, symBinAddr: 0x5C404, symSize: 0x80 }
  - { offset: 0x1002ED, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCMa', symObjAddr: 0x9EC, symBinAddr: 0x5C484, symSize: 0x20 }
  - { offset: 0x100310, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC17gestureRecognizer_13shouldReceiveSbSo09UIGestureF0C_So7UITouchCtFTo', symObjAddr: 0xA0C, symBinAddr: 0x5C4A4, symSize: 0x74 }
  - { offset: 0x10038E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_21numberOfRowsInSectionSiSo07UITableF0C_SitFTo', symObjAddr: 0xA80, symBinAddr: 0x5C518, symSize: 0x24 }
  - { offset: 0x1003AE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_21numberOfRowsInSectionSiSo07UITableF0C_SitFTo', symObjAddr: 0xA80, symBinAddr: 0x5C518, symSize: 0x24 }
  - { offset: 0x10044C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_12cellForRowAtSo07UITableF4CellCSo0kF0C_10Foundation9IndexPathVtF', symObjAddr: 0xAA4, symBinAddr: 0x5C53C, symSize: 0x570 }
  - { offset: 0x100724, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_12cellForRowAtSo07UITableF4CellCSo0kF0C_10Foundation9IndexPathVtFTo', symObjAddr: 0x1014, symBinAddr: 0x5CAAC, symSize: 0xB8 }
  - { offset: 0x10074F, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_14didSelectRowAtySo07UITableF0C_10Foundation9IndexPathVtFTo', symObjAddr: 0x10CC, symBinAddr: 0x5CB64, symSize: 0xAC }
  - { offset: 0x100781, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC17gestureRecognizer_13shouldReceiveSbSo09UIGestureF0C_So7UITouchCtFTf4dnn_n', symObjAddr: 0x1178, symBinAddr: 0x5CC10, symSize: 0xE4 }
  - { offset: 0x1007C0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableView_14didSelectRowAtySo07UITableF0C_10Foundation9IndexPathVtFTf4dnn_n', symObjAddr: 0x125C, symBinAddr: 0x5CCF4, symSize: 0x2A0 }
  - { offset: 0x100866, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaABSHSCWl', symObjAddr: 0x157C, symBinAddr: 0x5CF94, symSize: 0x48 }
  - { offset: 0x10087A, size: 0x8, addend: 0x0, symName: '_$sSo6UIViewCMa', symObjAddr: 0x15C4, symBinAddr: 0x5CFDC, symSize: 0x3C }
  - { offset: 0x10088E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypFyycfU_TA', symObjAddr: 0x1624, symBinAddr: 0x5D03C, symSize: 0x20 }
  - { offset: 0x1008A2, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1644, symBinAddr: 0x5D05C, symSize: 0x10 }
  - { offset: 0x1008B6, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1654, symBinAddr: 0x5D06C, symSize: 0x8 }
  - { offset: 0x1008CA, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC13dismissDailogyyFyycfU_TA', symObjAddr: 0x166C, symBinAddr: 0x5D074, symSize: 0x20 }
  - { offset: 0x1008DE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC011autoDismissC0yyFyycfU_TA', symObjAddr: 0x168C, symBinAddr: 0x5D094, symSize: 0x20 }
  - { offset: 0x100AD4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableViewSo07UITableF0CSgvgTo', symObjAddr: 0x0, symBinAddr: 0x5BA98, symSize: 0x20 }
  - { offset: 0x100B34, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC9tableViewSo07UITableF0CSgvsTo', symObjAddr: 0x20, symBinAddr: 0x5BAB8, symSize: 0x14 }
  - { offset: 0x100C84, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC11viewDidLoadyyF', symObjAddr: 0x34, symBinAddr: 0x5BACC, symSize: 0x274 }
  - { offset: 0x100E1E, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC11viewDidLoadyyFTo', symObjAddr: 0x2A8, symBinAddr: 0x5BD40, symSize: 0x28 }
  - { offset: 0x100E32, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC011autoDismissC0yyFTo', symObjAddr: 0x2F4, symBinAddr: 0x5BD8C, symSize: 0x28 }
  - { offset: 0x100E46, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC13dismissDailogyyFTo', symObjAddr: 0x418, symBinAddr: 0x5BEB0, symSize: 0x28 }
  - { offset: 0x100EA4, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypF', symObjAddr: 0x440, symBinAddr: 0x5BED8, symSize: 0x1CC }
  - { offset: 0x100F42, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC04hideC13ForeverTappedyyypFTo', symObjAddr: 0x668, symBinAddr: 0x5C100, symSize: 0x64 }
  - { offset: 0x100F56, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x6CC, symBinAddr: 0x5C164, symSize: 0x114 }
  - { offset: 0x100F97, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x7E0, symBinAddr: 0x5C278, symSize: 0x60 }
  - { offset: 0x100FAB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x840, symBinAddr: 0x5C2D8, symSize: 0xD4 }
  - { offset: 0x100FDE, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x914, symBinAddr: 0x5C3AC, symSize: 0x28 }
  - { offset: 0x100FF2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay18OpinionatedAlertVCCfD', symObjAddr: 0x93C, symBinAddr: 0x5C3D4, symSize: 0x30 }
  - { offset: 0x101249, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LL_WZ', symObjAddr: 0x0, symBinAddr: 0x5D0CC, symSize: 0x40 }
  - { offset: 0x10126D, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC05_baseC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA00, symBinAddr: 0xA50F0, symSize: 0x0 }
  - { offset: 0x101287, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC8_baseCdn33_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA10, symBinAddr: 0xA5100, symSize: 0x0 }
  - { offset: 0x1012A1, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC10_staticCdn33_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA20, symBinAddr: 0xA5110, symSize: 0x0 }
  - { offset: 0x1012BB, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC06_trackC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA30, symBinAddr: 0xA5120, symSize: 0x0 }
  - { offset: 0x1012D5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA40, symBinAddr: 0xA5130, symSize: 0x0 }
  - { offset: 0x1012EF, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC07_butlerdC033_8F9D27B74BAD54C523A79C11DE882365LLSSvpZ', symObjAddr: 0xA50, symBinAddr: 0xA5140, symSize: 0x0 }
  - { offset: 0x1012FD, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04_cdnC033_8F9D27B74BAD54C523A79C11DE882365LL_WZ', symObjAddr: 0x0, symBinAddr: 0x5D0CC, symSize: 0x40 }
  - { offset: 0x1013A2, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigCMa', symObjAddr: 0x50, symBinAddr: 0x5D10C, symSize: 0x20 }
  - { offset: 0x101480, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC10initialize6configySDyS2SG_tFZTf4nd_n', symObjAddr: 0x70, symBinAddr: 0x5D12C, symSize: 0x460 }
  - { offset: 0x1016F6, size: 0x8, addend: 0x0, symName: '_$s8Razorpay15GlobalUrlConfigC04baseC8CheckoutSSvgZTf4d_n', symObjAddr: 0x4D0, symBinAddr: 0x5D58C, symSize: 0x2B0 }
  - { offset: 0x101B8C, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilCMa', symObjAddr: 0x10, symBinAddr: 0x5D83C, symSize: 0x20 }
  - { offset: 0x101BD5, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilC14getStringValue6forKey011withDefaultG0S2S_SSSgtFZTf4nnd_n', symObjAddr: 0x30, symBinAddr: 0x5D85C, symSize: 0x164 }
  - { offset: 0x101C25, size: 0x8, addend: 0x0, symName: '_$s8Razorpay0A16UserDefaultsUtilC11resetValuesyyFZTf4d_n', symObjAddr: 0x25C, symBinAddr: 0x5D9C0, symSize: 0x154 }
  - { offset: 0x101DD0, size: 0x8, addend: 0x0, symName: '_$s8Razorpay19ResourceBundleClass06_E20D2F25E2878EA12AB6F7C669BA135C5LLCMa', symObjAddr: 0x10, symBinAddr: 0x5DB14, symSize: 0x20 }
  - { offset: 0x101F6D, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_magic, symObjAddr: 0x28, symBinAddr: 0x5DB34, symSize: 0x14 }
  - { offset: 0x101F7F, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_padding_bytes, symObjAddr: 0x44, symBinAddr: 0x5DB48, symSize: 0xC }
  - { offset: 0x101F91, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_version, symObjAddr: 0x50, symBinAddr: 0x5DB54, symSize: 0x10 }
  - { offset: 0x101FD4, size: 0x8, addend: 0x0, symName: _lprofProfileDumped, symObjAddr: 0x0, symBinAddr: 0x5DB64, symSize: 0xC }
  - { offset: 0x101FE2, size: 0x8, addend: 0x0, symName: _lprofProfileDumped, symObjAddr: 0x0, symBinAddr: 0x5DB64, symSize: 0xC }
  - { offset: 0x10201E, size: 0x8, addend: 0x0, symName: _getValueProfRecordHeaderSize, symObjAddr: 0x0, symBinAddr: 0x5DB70, symSize: 0xC }
  - { offset: 0x10202C, size: 0x8, addend: 0x0, symName: _getValueProfRecordHeaderSize, symObjAddr: 0x0, symBinAddr: 0x5DB70, symSize: 0xC }
  - { offset: 0x102045, size: 0x8, addend: 0x0, symName: _getValueProfRecordValueData, symObjAddr: 0x1C, symBinAddr: 0x5DB7C, symSize: 0x14 }
  - { offset: 0x10207C, size: 0x8, addend: 0x0, symName: _getValueProfRecordNext, symObjAddr: 0x1E8, symBinAddr: 0x5DB90, symSize: 0x1D8 }
  - { offset: 0x1020BA, size: 0x8, addend: 0x0, symName: _getFirstValueProfRecord, symObjAddr: 0x3C0, symBinAddr: 0x5DD68, symSize: 0x8 }
  - { offset: 0x1020E8, size: 0x8, addend: 0x0, symName: _lprofSetupValueProfiler, symObjAddr: 0x960, symBinAddr: 0x5DD70, symSize: 0x68 }
  - { offset: 0x102154, size: 0x8, addend: 0x0, symName: ___llvm_profile_instrument_target_value, symObjAddr: 0xC50, symBinAddr: 0x5DDD8, symSize: 0x284 }
  - { offset: 0x1021D0, size: 0x8, addend: 0x0, symName: _lprofGetVPDataReader, symObjAddr: 0xF1C, symBinAddr: 0x5E05C, symSize: 0xC }
  - { offset: 0x1021E2, size: 0x8, addend: 0x0, symName: _initializeValueProfRuntimeRecord, symObjAddr: 0xF28, symBinAddr: 0x5E068, symSize: 0x178 }
  - { offset: 0x1021FB, size: 0x8, addend: 0x0, symName: _getNumValueDataForSiteWrapper, symObjAddr: 0x10A0, symBinAddr: 0x5E1E0, symSize: 0x14 }
  - { offset: 0x102211, size: 0x8, addend: 0x0, symName: _getNumValueDataForSiteWrapper, symObjAddr: 0x10A0, symBinAddr: 0x5E1E0, symSize: 0x14 }
  - { offset: 0x102224, size: 0x8, addend: 0x0, symName: _getValueProfDataSizeWrapper, symObjAddr: 0x10B4, symBinAddr: 0x5E1F4, symSize: 0xA8 }
  - { offset: 0x1022A9, size: 0x8, addend: 0x0, symName: _getNumValueSitesRT, symObjAddr: 0x11A0, symBinAddr: 0x5E2E0, symSize: 0x10 }
  - { offset: 0x1022BB, size: 0x8, addend: 0x0, symName: _getNumValueDataRT, symObjAddr: 0x11B0, symBinAddr: 0x5E2F0, symSize: 0x1C4 }
  - { offset: 0x1022CD, size: 0x8, addend: 0x0, symName: _getNextNValueData, symObjAddr: 0x115C, symBinAddr: 0x5E29C, symSize: 0x44 }
  - { offset: 0x102309, size: 0x8, addend: 0x0, symName: ___llvm_profile_is_continuous_mode_enabled, symObjAddr: 0x0, symBinAddr: 0x5E4B4, symSize: 0x1C }
  - { offset: 0x102317, size: 0x8, addend: 0x0, symName: ___llvm_profile_is_continuous_mode_enabled, symObjAddr: 0x0, symBinAddr: 0x5E4B4, symSize: 0x1C }
  - { offset: 0x102329, size: 0x8, addend: 0x0, symName: ___llvm_profile_enable_continuous_mode, symObjAddr: 0x1C, symBinAddr: 0x5E4D0, symSize: 0x10 }
  - { offset: 0x10233B, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_page_size, symObjAddr: 0x2C, symBinAddr: 0x5E4E0, symSize: 0xC }
  - { offset: 0x10234D, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_size_for_buffer, symObjAddr: 0x38, symBinAddr: 0x5E4EC, symSize: 0x6C }
  - { offset: 0x1023F2, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_size_for_buffer_internal, symObjAddr: 0xA4, symBinAddr: 0x5E558, symSize: 0x138 }
  - { offset: 0x1025C7, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_data, symObjAddr: 0x1DC, symBinAddr: 0x5E690, symSize: 0x1C }
  - { offset: 0x1025D9, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_data_size, symObjAddr: 0x1F8, symBinAddr: 0x5E6AC, symSize: 0x24 }
  - { offset: 0x1025EF, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_data_size, symObjAddr: 0x1F8, symBinAddr: 0x5E6AC, symSize: 0x24 }
  - { offset: 0x102601, size: 0x8, addend: 0x0, symName: ___llvm_profile_counter_entry_size, symObjAddr: 0x21C, symBinAddr: 0x5E6D0, symSize: 0x20 }
  - { offset: 0x102622, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_counters, symObjAddr: 0x23C, symBinAddr: 0x5E6F0, symSize: 0x4C }
  - { offset: 0x10267B, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_counters_size, symObjAddr: 0x288, symBinAddr: 0x5E73C, symSize: 0x5C }
  - { offset: 0x10270D, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_padding_sizes_for_counters, symObjAddr: 0x2E4, symBinAddr: 0x5E798, symSize: 0xC8 }
  - { offset: 0x1027A6, size: 0x8, addend: 0x0, symName: _initBufferWriter, symObjAddr: 0x3AC, symBinAddr: 0x5E860, symSize: 0x10 }
  - { offset: 0x102806, size: 0x8, addend: 0x0, symName: _getCurFilenameLength, symObjAddr: 0x1C0, symBinAddr: 0x5E870, symSize: 0x314 }
  - { offset: 0x102851, size: 0x8, addend: 0x0, symName: _getCurFilename, symObjAddr: 0x4D4, symBinAddr: 0x5EB84, symSize: 0x33C }
  - { offset: 0x102955, size: 0x8, addend: 0x0, symName: _parseAndSetFilename, symObjAddr: 0x918, symBinAddr: 0x5EEC0, symSize: 0x568 }
  - { offset: 0x102BA9, size: 0x8, addend: 0x0, symName: _truncateCurrentFile, symObjAddr: 0x1CE8, symBinAddr: 0x5FD90, symSize: 0xCC }
  - { offset: 0x102C1E, size: 0x8, addend: 0x0, symName: _initializeProfileForContinuousMode, symObjAddr: 0x1DB4, symBinAddr: 0x5FE5C, symSize: 0x250 }
  - { offset: 0x102E37, size: 0x8, addend: 0x0, symName: ___llvm_profile_initialize, symObjAddr: 0xE80, symBinAddr: 0x5F428, symSize: 0xBC }
  - { offset: 0x102EF5, size: 0x8, addend: 0x0, symName: ___llvm_profile_write_file, symObjAddr: 0xFC0, symBinAddr: 0x5F4E4, symSize: 0x184 }
  - { offset: 0x102FC1, size: 0x8, addend: 0x0, symName: _writeFile, symObjAddr: 0x1144, symBinAddr: 0x5F668, symSize: 0x288 }
  - { offset: 0x103257, size: 0x8, addend: 0x0, symName: _createProfileDir, symObjAddr: 0x2004, symBinAddr: 0x600AC, symSize: 0x88 }
  - { offset: 0x10329C, size: 0x8, addend: 0x0, symName: _getProfileFileSizeForMerging, symObjAddr: 0x184C, symBinAddr: 0x5F8F4, symSize: 0x114 }
  - { offset: 0x10332D, size: 0x8, addend: 0x0, symName: _mmapProfileForMerging, symObjAddr: 0x1960, symBinAddr: 0x5FA08, symSize: 0xC0 }
  - { offset: 0x1033EC, size: 0x8, addend: 0x0, symName: _writeFileWithoutReturn, symObjAddr: 0x1660, symBinAddr: 0x5F8F0, symSize: 0x4 }
  - { offset: 0x103402, size: 0x8, addend: 0x0, symName: _writeFileWithoutReturn, symObjAddr: 0x1660, symBinAddr: 0x5F8F0, symSize: 0x4 }
  - { offset: 0x103414, size: 0x8, addend: 0x0, symName: _mmapForContinuousMode, symObjAddr: 0x1A20, symBinAddr: 0x5FAC8, symSize: 0x184 }
  - { offset: 0x10355C, size: 0x8, addend: 0x0, symName: _fileWriter, symObjAddr: 0x1BA4, symBinAddr: 0x5FC4C, symSize: 0x144 }
  - { offset: 0x1035B8, size: 0x8, addend: 0x0, symName: _exitSignalHandler, symObjAddr: 0x208C, symBinAddr: 0x60134, symSize: 0x10 }
  - { offset: 0x10360B, size: 0x8, addend: 0x0, symName: _lprofGetLoadModuleSignature, symObjAddr: 0x0, symBinAddr: 0x60144, symSize: 0xD4 }
  - { offset: 0x103619, size: 0x8, addend: 0x0, symName: _lprofGetLoadModuleSignature, symObjAddr: 0x0, symBinAddr: 0x60144, symSize: 0xD4 }
  - { offset: 0x10371A, size: 0x8, addend: 0x0, symName: ___llvm_profile_check_compatibility, symObjAddr: 0xD4, symBinAddr: 0x60218, symSize: 0x19C }
  - { offset: 0x1037DE, size: 0x8, addend: 0x0, symName: ___llvm_profile_merge_from_buffer, symObjAddr: 0x270, symBinAddr: 0x603B4, symSize: 0x1EC }
  - { offset: 0x103899, size: 0x8, addend: 0x0, symName: _lprofMergeValueProfData, symObjAddr: 0x0, symBinAddr: 0x605A0, symSize: 0xE4 }
  - { offset: 0x1038A7, size: 0x8, addend: 0x0, symName: _lprofMergeValueProfData, symObjAddr: 0x0, symBinAddr: 0x605A0, symSize: 0xE4 }
  - { offset: 0x10393F, size: 0x8, addend: 0x0, symName: _lprofBufferWriter, symObjAddr: 0x0, symBinAddr: 0x60684, symSize: 0x70 }
  - { offset: 0x10394D, size: 0x8, addend: 0x0, symName: _lprofBufferWriter, symObjAddr: 0x0, symBinAddr: 0x60684, symSize: 0x70 }
  - { offset: 0x103974, size: 0x8, addend: 0x0, symName: _lprofBufferIOWrite, symObjAddr: 0x108, symBinAddr: 0x606F4, symSize: 0x138 }
  - { offset: 0x1039D7, size: 0x8, addend: 0x0, symName: _lprofWriteData, symObjAddr: 0x304, symBinAddr: 0x6082C, symSize: 0x98 }
  - { offset: 0x103A7C, size: 0x8, addend: 0x0, symName: _lprofWriteDataImpl, symObjAddr: 0x39C, symBinAddr: 0x608C4, symSize: 0x2E0 }
  - { offset: 0x103BCF, size: 0x8, addend: 0x0, symName: _createHeader, symObjAddr: 0x67C, symBinAddr: 0x60BA4, symSize: 0x124 }
  - { offset: 0x103C79, size: 0x8, addend: 0x0, symName: _writeOneValueProfData, symObjAddr: 0xC5C, symBinAddr: 0x60CC8, symSize: 0x370 }
  - { offset: 0x103DC3, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_data, symObjAddr: 0x0, symBinAddr: 0x61038, symSize: 0xC }
  - { offset: 0x103DD1, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_data, symObjAddr: 0x0, symBinAddr: 0x61038, symSize: 0xC }
  - { offset: 0x103DE3, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_data, symObjAddr: 0xC, symBinAddr: 0x61044, symSize: 0xC }
  - { offset: 0x103DF5, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_names, symObjAddr: 0x18, symBinAddr: 0x61050, symSize: 0xC }
  - { offset: 0x103E07, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_names, symObjAddr: 0x24, symBinAddr: 0x6105C, symSize: 0xC }
  - { offset: 0x103E19, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_counters, symObjAddr: 0x30, symBinAddr: 0x61068, symSize: 0xC }
  - { offset: 0x103E2B, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_counters, symObjAddr: 0x3C, symBinAddr: 0x61074, symSize: 0xC }
  - { offset: 0x103E3D, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_vnodes, symObjAddr: 0x54, symBinAddr: 0x61080, symSize: 0xC }
  - { offset: 0x103E4F, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_vnodes, symObjAddr: 0x60, symBinAddr: 0x6108C, symSize: 0xC }
  - { offset: 0x103E8B, size: 0x8, addend: 0x0, symName: ___llvm_profile_recursive_mkdir, symObjAddr: 0x0, symBinAddr: 0x610B4, symSize: 0x5C }
  - { offset: 0x103EA0, size: 0x8, addend: 0x0, symName: ___llvm_profile_recursive_mkdir, symObjAddr: 0x0, symBinAddr: 0x610B4, symSize: 0x5C }
  - { offset: 0x103EDF, size: 0x8, addend: 0x0, symName: _lprofGetHostName, symObjAddr: 0x74, symBinAddr: 0x61110, symSize: 0x80 }
  - { offset: 0x103F29, size: 0x8, addend: 0x0, symName: _lprofLockFileHandle, symObjAddr: 0x214, symBinAddr: 0x61190, symSize: 0x94 }
  - { offset: 0x103FA7, size: 0x8, addend: 0x0, symName: _lprofUnlockFileHandle, symObjAddr: 0x2A8, symBinAddr: 0x61224, symSize: 0x94 }
  - { offset: 0x104015, size: 0x8, addend: 0x0, symName: _lprofOpenFileEx, symObjAddr: 0x33C, symBinAddr: 0x612B8, symSize: 0xD4 }
  - { offset: 0x1040B9, size: 0x8, addend: 0x0, symName: _lprofFindFirstDirSeparator, symObjAddr: 0x554, symBinAddr: 0x6138C, symSize: 0x8 }
  - { offset: 0x1040E2, size: 0x8, addend: 0x0, symName: _lprofInstallSignalHandler, symObjAddr: 0x564, symBinAddr: 0x61394, symSize: 0x58 }
...
