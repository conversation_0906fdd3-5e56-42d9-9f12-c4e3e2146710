{"ABIRoot": {"kind": "Root", "name": "Razorpay", "printedName": "Razorpay", "children": [{"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "PluginPaymentCompletionDelegate", "printedName": "PluginPaymentCompletionDelegate", "children": [{"kind": "Function", "name": "paymentSuccessful", "printedName": "paymentSuccessful(orderID:dictVerification:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "[Swift.AnyHashable : Any]?", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "s:8Razorpay31PluginPaymentCompletionDelegateP17paymentSuccessful7orderID16dictVerificationySS_SDys11AnyHashableVypGSgtF", "mangledName": "$s8Razorpay31PluginPaymentCompletionDelegateP17paymentSuccessful7orderID16dictVerificationySS_SDys11AnyHashableVypGSgtF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentCompletionDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentCompletionDelegate>", "protocolReq": true, "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "paymentFailed", "printedName": "paymentFailed(code:errorDescription:data:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Func", "usr": "s:8Razorpay31PluginPaymentCompletionDelegateP13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGtF", "mangledName": "$s8Razorpay31PluginPaymentCompletionDelegateP13paymentFailed4code16errorDescription4dataySi_SSSDys11AnyHashableVypGtF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentCompletionDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentCompletionDelegate>", "protocolReq": true, "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "trackEvent", "printedName": "trackEvent(event:withProperties:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "[Swift.AnyHashable : Any]?", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "s:8Razorpay31PluginPaymentCompletionDelegateP10trackEvent5event14withPropertiesySS_SDys11AnyHashableVypGSgtF", "mangledName": "$s8Razorpay31PluginPaymentCompletionDelegateP10trackEvent5event14withPropertiesySS_SDys11AnyHashableVypGSgtF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentCompletionDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentCompletionDelegate>", "protocolReq": true, "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "s:8Razorpay31PluginPaymentCompletionDelegateP", "mangledName": "$s8Razorpay31PluginPaymentCompletionDelegateP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "RawDocComment"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "PluginPaymentDelegate", "printedName": "PluginPaymentDelegate", "children": [{"kind": "Function", "name": "canProcessPayment", "printedName": "canProcessPayment(model:)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "PluginPaymentModel", "printedName": "Razorpay.PluginPaymentModel", "usr": "c:@M@Razorpay@objc(cs)PluginPaymentModel"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate(im)canProcessPaymentWithModel:", "mangledName": "$s8Razorpay21PluginPaymentDelegateP010canProcessC05modelSbAA0bC5ModelC_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentDelegate>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "identifier", "printedName": "identifier()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate(im)identifier", "mangledName": "$s8Razorpay21PluginPaymentDelegateP10identifierSSyF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentDelegate>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "pay", "printedName": "pay(model:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "PluginPaymentModel", "printedName": "Razorpay.PluginPaymentModel", "usr": "c:@M@Razorpay@objc(cs)PluginPaymentModel"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate(im)payWithModel:", "mangledName": "$s8Razorpay21PluginPaymentDelegateP3pay5modelyAA0bC5ModelC_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentDelegate>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getExternalPaymentData", "printedName": "getExternalPaymentData(mobileNumber:orderId:handler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}, {"kind": "TypeFunc", "name": "Function", "printedName": "() -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Void", "printedName": "()"}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate(im)getExternalPaymentDataWithMobileNumber:orderId:handler:", "mangledName": "$s8Razorpay21PluginPaymentDelegateP011getExternalC4Data12mobileNumber7orderId7handlerySS_SSSgyyctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentDelegate>", "protocolReq": true, "objc_name": "getExternalPaymentDataWithMobileNumber:orderId:handler:", "declAttributes": ["Optional", "ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "paymentData", "printedName": "paymentData()", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[[Swift.AnyHashable : Any]]", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "usr": "s:Sa"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate(im)paymentData", "mangledName": "$s8Razorpay21PluginPaymentDelegateP11paymentDataSaySDys11AnyHashableVypGGyF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentDelegate>", "protocolReq": true, "declAttributes": ["Optional", "ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "paymentTPVData", "printedName": "paymentTPVData()", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate(im)paymentTPVData", "mangledName": "$s8Razorpay21PluginPaymentDelegateP14paymentTPVDataSDys11AnyHashableVypGyF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.PluginPaymentDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.PluginPaymentDelegate>", "protocolReq": true, "declAttributes": ["Optional", "ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate", "mangledName": "$s8Razorpay21PluginPaymentDelegateP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC", "RawDocComment"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "ErrorDescribable", "printedName": "ErrorDescribable", "children": [{"kind": "Var", "name": "localizedDescription", "printedName": "localizedDescription", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:8Razorpay16ErrorDescribableP20localizedDescriptionSSvp", "mangledName": "$s8Razorpay16ErrorDescribableP20localizedDescriptionSSvp", "moduleName": "Razorpay", "protocolReq": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:8Razorpay16ErrorDescribableP20localizedDescriptionSSvg", "mangledName": "$s8Razorpay16ErrorDescribableP20localizedDescriptionSSvg", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.ErrorDescribable>", "sugared_genericSig": "<Self where Self : Ra<PERSON><PERSON>y.ErrorDescribable>", "protocolReq": true, "reqNewWitnessTableEntry": true, "accessorKind": "get"}]}], "declKind": "Protocol", "usr": "s:8Razorpay16ErrorDescribableP", "mangledName": "$s8Razorpay16ErrorDescribableP", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "UPITurboPlugin", "printedName": "UPITurboPlugin", "children": [{"kind": "Var", "name": "TPV", "printedName": "TPV", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboTPVPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVPlugin", "printedName": "any Razorpay.UPITurboTPVPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(py)TPV", "mangledName": "$s8Razorpay14UPITurboPluginP3TPVAA0B9TPVPlugin_pSgvp", "moduleName": "Razorpay", "protocolReq": true, "declAttributes": ["ObjC"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboTPVPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVPlugin", "printedName": "any Razorpay.UPITurboTPVPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)TPV", "mangledName": "$s8Razorpay14UPITurboPluginP3TPVAA0B9TPVPlugin_pSgvg", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "paymentPlugin", "printedName": "paymentPlugin", "children": [{"kind": "TypeNominal", "name": "PluginPaymentDelegate", "printedName": "any Razorpay.PluginPaymentDelegate", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate"}], "declKind": "Var", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(py)paymentPlugin", "mangledName": "$s8Razorpay14UPITurboPluginP07paymentC0AA0C15PaymentDelegate_pvp", "moduleName": "Razorpay", "protocolReq": true, "declAttributes": ["ObjC"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "PluginPaymentDelegate", "printedName": "any Razorpay.PluginPaymentDelegate", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate"}], "declKind": "Accessor", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)paymentPlugin", "mangledName": "$s8Razorpay14UPITurboPluginP07paymentC0AA0C15PaymentDelegate_pvg", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "deviceBindingDone", "printedName": "deviceBindingDone", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Var", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(py)deviceBindingDone", "mangledName": "$s8Razorpay14UPITurboPluginP17deviceBindingDoneSbvp", "moduleName": "Razorpay", "protocolReq": true, "declAttributes": ["ObjC"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Accessor", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)deviceBindingDone", "mangledName": "$s8Razorpay14UPITurboPluginP17deviceBindingDoneSbvg", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "accessorKind": "get"}]}, {"kind": "Function", "name": "linkNewAccount", "printedName": "linkNewAccount(mobileNumber:linkActionDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)linkNewAccountWithMobileNumber:linkActionDelegate:", "mangledName": "$s8Razorpay14UPITurboPluginP14linkNewAccount12mobileNumber0D14ActionDelegateySS_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "linkNewAccount", "printedName": "linkNewAccount(customerId:linkActionDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)linkNewAccountWithCustomerId:linkActionDelegate:", "mangledName": "$s8Razorpay14UPITurboPluginP14linkNewAccount10customerId0D14ActionDelegateySS_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "fetchAccountBalance", "printedName": "fetchAccountBalance(upiAccount:handler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)fetchAccountBalanceWithUpiAccount:handler:", "mangledName": "$s8Razorpay14UPITurboPluginP19fetchAccountBalance03upiE07handleryypSg_yAG_AGtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "resetUpiPin", "printedName": "resetUpiPin(upiAccount:card:handler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)resetUpiPinWithUpiAccount:card:handler:", "mangledName": "$s8Razorpay14UPITurboPluginP11resetUpiPin10upiAccount4card7handleryypSg_ypyAH_AHtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "delinkVpa", "printedName": "delinkVpa(upiAccount:handler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)delinkVpaWithUpiAccount:handler:", "mangledName": "$s8Razorpay14UPITurboPluginP9delinkVpa10upiAccount7handleryypSg_yAG_AGtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "delinkVpa", "printedName": "delinkVpa(linkedBankAccount:handler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)delinkVpaWithLinkedBankAccount:handler:", "mangledName": "$s8Razorpay14UPITurboPluginP9delinkVpa17linkedBankAccount7handleryypSg_yAG_AGtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "changeUpiPin", "printedName": "changeUpiPin(upiAccount:handler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin(im)changeUpiPinWithUpiAccount:handler:", "mangledName": "$s8Razorpay14UPITurboPluginP12changeUpiPin10upiAccount7handleryypSg_yAG_AGtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin", "mangledName": "$s8Razorpay14UPITurboPluginP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : Razorpay.TokenPlugin, τ_0_0 : Razorpay.UPITurboLinkedBankAccountsProtocol, τ_0_0 : Razorpay.UPITurboLinkedUpiAccountsProtocol, τ_0_0 : Razorpay.UPITurboPrefetchProtocol>", "sugared_genericSig": "<Self : <PERSON><PERSON><PERSON><PERSON>.TokenPlugin, Self : <PERSON><PERSON><PERSON><PERSON>.UPITurboLinkedBankAccountsProtocol, Self : <PERSON><PERSON><PERSON>y.UPITurboLinkedUpiAccountsProtocol, Self : Razorpay.UPITurboPrefetchProtocol>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "TokenPlugin", "printedName": "TokenPlugin", "usr": "c:@M@Razorpay@objc(pl)TokenPlugin", "mangledName": "$s8Razorpay11TokenPluginP"}, {"kind": "Conformance", "name": "UPITurboLinkedBankAccountsProtocol", "printedName": "UPITurboLinkedBankAccountsProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboLinkedBankAccountsProtocol", "mangledName": "$s8Razorpay34UPITurboLinkedBankAccountsProtocolP"}, {"kind": "Conformance", "name": "UPITurboLinkedUpiAccountsProtocol", "printedName": "UPITurboLinkedUpiAccountsProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboLinkedUpiAccountsProtocol", "mangledName": "$s8Razorpay33UPITurboLinkedUpiAccountsProtocolP"}, {"kind": "Conformance", "name": "UPITurboPrefetchProtocol", "printedName": "UPITurboPrefetchProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchProtocol", "mangledName": "$s8Razorpay24UPITurboPrefetchProtocolP"}]}, {"kind": "TypeDecl", "name": "TokenPlugin", "printedName": "TokenPlugin", "children": [{"kind": "Function", "name": "initialize", "printedName": "initialize(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)TokenPlugin(im)initialize:", "mangledName": "$s8Razorpay11TokenPluginP10initializeyyypF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.TokenPlugin>", "sugared_genericSig": "<Self where Self : <PERSON><PERSON><PERSON><PERSON>.TokenPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)TokenPlugin", "mangledName": "$s8Razorpay11TokenPluginP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "Session", "printedName": "Session", "children": [{"kind": "Var", "name": "token", "printedName": "token", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:8Razorpay7SessionC5tokenSSvp", "mangledName": "$s8Razorpay7SessionC5tokenSSvp", "moduleName": "Razorpay", "declAttributes": ["Final", "HasStorage", "AccessControl"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:8Razorpay7SessionC5tokenSSvg", "mangledName": "$s8Razorpay7SessionC5tokenSSvg", "moduleName": "Razorpay", "implicit": true, "declAttributes": ["Final"], "accessorKind": "get"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(token:)", "children": [{"kind": "TypeNominal", "name": "Session", "printedName": "Razorpay.Session", "usr": "c:@M@Razorpay@objc(cs)Session"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:8Razorpay7SessionC5tokenACSS_tcfc", "mangledName": "$s8Razorpay7SessionC5tokenACSS_tcfc", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "init_kind": "Designated"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init()", "children": [{"kind": "TypeNominal", "name": "Session", "printedName": "Razorpay.Session", "usr": "c:@M@Razorpay@objc(cs)Session"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@Razorpay@objc(cs)Session(im)init", "mangledName": "$s8Razorpay7SessionCACycfc", "moduleName": "Razorpay", "overriding": true, "implicit": true, "objc_name": "init", "declAttributes": ["Dynamic", "ObjC", "Override"], "init_kind": "Designated"}], "declKind": "Class", "usr": "c:@M@Razorpay@objc(cs)Session", "mangledName": "$s8Razorpay7SessionC", "moduleName": "Razorpay", "declAttributes": ["AccessControl", "ObjC"], "superclassUsr": "c:objc(cs)NSObject", "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "TurboSessionDelegate", "printedName": "TurboSessionDelegate", "children": [{"kind": "Function", "name": "fetchToken", "printedName": "fetchToken(completion:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Razorpay.Session) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Session", "printedName": "Razorpay.Session", "usr": "c:@M@Razorpay@objc(cs)Session"}]}], "declKind": "Func", "usr": "s:8Razorpay20TurboSessionDelegateP10fetchToken10completionyyAA0C0Cc_tF", "mangledName": "$s8Razorpay20TurboSessionDelegateP10fetchToken10completionyyAA0C0Cc_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.TurboSessionDelegate>", "sugared_genericSig": "<Self where Self : Razorpay.TurboSessionDelegate>", "protocolReq": true, "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "s:8Razorpay20TurboSessionDelegateP", "mangledName": "$s8Razorpay20TurboSessionDelegateP", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "UPITurboTPVPlugin", "printedName": "UPITurboTPVPlugin", "children": [{"kind": "Function", "name": "linkNewUpiAccount", "printedName": "linkNewUpiAccount(linkActionDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin(im)linkNewUpiAccountWithLinkActionDelegate:", "mangledName": "$s8Razorpay17UPITurboTPVPluginP17linkNewUpiAccount0D14ActionDelegateyyp_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setOrderId", "printedName": "setOrderId(orderId:)", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVPlugin", "printedName": "any Razorpay.UPITurboTPVPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin(im)setOrderIdWithOrderId:", "mangledName": "$s8Razorpay17UPITurboTPVPluginP10setOrderId05orderF0AaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setCustomerId", "printedName": "setCustomerId(customerId:)", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVPlugin", "printedName": "any Razorpay.UPITurboTPVPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin(im)setCustomerIdWithCustomerId:", "mangledName": "$s8Razorpay17UPITurboTPVPluginP13setCustomerId08customerF0AaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setMobileNumber", "printedName": "setMobileNumber(mobile:)", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVPlugin", "printedName": "any Razorpay.UPITurboTPVPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin(im)setMobileNumberWithMobile:", "mangledName": "$s8Razorpay17UPITurboTPVPluginP15setMobileNumber6mobileAaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setTpvBankAccount", "printedName": "setTpvBankAccount(tpvBankAccount:)", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVPlugin", "printedName": "any Razorpay.UPITurboTPVPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin(im)setTpvBankAccountWithTpvBankAccount:", "mangledName": "$s8Razorpay17UPITurboTPVPluginP17setTpvBankAccount03tpvfG0AaB_pyp_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVPlugin", "mangledName": "$s8Razorpay17UPITurboTPVPluginP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "UPITurboPrefetchProtocol", "printedName": "UPITurboPrefetchProtocol", "children": [{"kind": "Function", "name": "setCustomerMobile", "printedName": "setCustomerMobile(mobile:)", "children": [{"kind": "TypeNominal", "name": "UPITurboPrefetchProtocol", "printedName": "any Razorpay.UPITurboPrefetchProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchProtocol"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchProtocol(im)setCustomerMobileWithMobile:", "mangledName": "$s8Razorpay24UPITurboPrefetchProtocolP17setCustomerMobile6mobileAaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setCustomerId", "printedName": "setCustomerId(customerId:)", "children": [{"kind": "TypeNominal", "name": "UPITurboPrefetchProtocol", "printedName": "any Razorpay.UPITurboPrefetchProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchProtocol"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchProtocol(im)setCustomerIdWithCustomerId:", "mangledName": "$s8Razorpay24UPITurboPrefetchProtocolP13setCustomerId08customerG0AaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "prefetchAndLinkUpiAccounts", "printedName": "prefetchAndLinkUpiAccounts(linkAccountWithUPIPinNotSet:linkActionDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchProtocol(im)prefetchAndLinkUpiAccountsWithLinkAccountWithUPIPinNotSet:linkActionDelegate:", "mangledName": "$s8Razorpay24UPITurboPrefetchProtocolP26prefetchAndLinkUpiAccounts27linkAccountWithUPIPinNotSet0J14ActionDelegateySb_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "prefetchAndLinkUpiAccounts", "printedName": "prefetchAndLinkUpiAccounts(linkAccountWithUPIPinNotSet:linkActionDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "hasDefaultArg": true, "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:8Razorpay24UPITurboPrefetchProtocolPAAE26prefetchAndLinkUpiAccounts27linkAccountWithUPIPinNotSet0J14ActionDelegateySb_yptF", "mangledName": "$s8Razorpay24UPITurboPrefetchProtocolPAAE26prefetchAndLinkUpiAccounts27linkAccountWithUPIPinNotSet0J14ActionDelegateySb_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchProtocol>", "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchProtocol", "mangledName": "$s8Razorpay24UPITurboPrefetchProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "UPITurboUIPlugin", "printedName": "UPITurboUIPlugin", "children": [{"kind": "Var", "name": "TPV", "printedName": "TPV", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboTPVUIPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVUIPlugin", "printedName": "any Razorpay.UPITurboTPVUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(py)TPV", "mangledName": "$s8Razorpay16UPITurboUIPluginP3TPVAA0B11TPVUIPlugin_pSgvp", "moduleName": "Razorpay", "protocolReq": true, "declAttributes": ["ObjC"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboTPVUIPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVUIPlugin", "printedName": "any Razorpay.UPITurboTPVUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(im)TPV", "mangledName": "$s8Razorpay16UPITurboUIPluginP3TPVAA0B11TPVUIPlugin_pSgvg", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "corePlugin", "printedName": "corePlugin", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboPlugin", "printedName": "any Razorpay.UPITurboPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(py)corePlugin", "mangledName": "$s8Razorpay16UPITurboUIPluginP10corePluginAA0bE0_pSgvp", "moduleName": "Razorpay", "protocolReq": true, "declAttributes": ["ObjC"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboPlugin", "printedName": "any Razorpay.UPITurboPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboPlugin"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(im)corePlugin", "mangledName": "$s8Razorpay16UPITurboUIPluginP10corePluginAA0bE0_pSgvg", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "paymentPlugin", "printedName": "paymentPlugin", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.PluginPaymentDelegate)?", "children": [{"kind": "TypeNominal", "name": "PluginPaymentDelegate", "printedName": "any Razorpay.PluginPaymentDelegate", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(py)paymentPlugin", "mangledName": "$s8Razorpay16UPITurboUIPluginP13paymentPluginAA0E15PaymentDelegate_pSgvp", "moduleName": "Razorpay", "protocolReq": true, "declAttributes": ["ObjC"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.PluginPaymentDelegate)?", "children": [{"kind": "TypeNominal", "name": "PluginPaymentDelegate", "printedName": "any Razorpay.PluginPaymentDelegate", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(im)paymentPlugin", "mangledName": "$s8Razorpay16UPITurboUIPluginP13paymentPluginAA0E15PaymentDelegate_pSgvg", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "accessorKind": "get"}]}, {"kind": "Function", "name": "linkNewUpiAccount", "printedName": "linkNewUpiAccount(mobileNumber:color:completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(im)linkNewUpiAccountWithMobileNumber:color:completionHandler:", "mangledName": "$s8Razorpay16UPITurboUIPluginP17linkNewUpiAccount12mobileNumber5color17completionHandlerySS_SSyypSg_AHtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "manageUpiAccount", "printedName": "manageUpiAccount(mobileNumber:color:completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(im)manageUpiAccountWithMobileNumber:color:completionHandler:", "mangledName": "$s8Razorpay16UPITurboUIPluginP16manageUpiAccount12mobileNumber5color17completionHandlerySS_SSyypSg_AHtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getUpiAccountObject", "printedName": "getUpiAccountObject(upiAccounts:)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "[[Swift.AnyHashable : Any]]?", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[[Swift.AnyHashable : Any]]", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "usr": "s:Sa"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(im)getUpiAccountObjectWithUpiAccounts:", "mangledName": "$s8Razorpay16UPITurboUIPluginP19getUpiAccountObject11upiAccountsSaySDys11AnyHashableVypGGSgypSg_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getLinkedUpiAccounts", "printedName": "getLinkedUpiAccounts(mobileNumber:resultDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin(im)getLinkedUpiAccountsWithMobileNumber:resultDelegate:", "mangledName": "$s8Razorpay16UPITurboUIPluginP20getLinkedUpiAccounts12mobileNumber14resultDelegateySS_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin", "mangledName": "$s8Razorpay16UPITurboUIPluginP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : Razorpay.TokenPlugin, τ_0_0 : Razorpay.UPITurboPrefetchWithUIProtocol>", "sugared_genericSig": "<Self : <PERSON><PERSON><PERSON><PERSON>.TokenPlugin, Self : Razorpay.UPITurboPrefetchWithUIProtocol>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "TokenPlugin", "printedName": "TokenPlugin", "usr": "c:@M@Razorpay@objc(pl)TokenPlugin", "mangledName": "$s8Razorpay11TokenPluginP"}, {"kind": "Conformance", "name": "UPITurboPrefetchWithUIProtocol", "printedName": "UPITurboPrefetchWithUIProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol", "mangledName": "$s8Razorpay30UPITurboPrefetchWithUIProtocolP"}]}, {"kind": "TypeDecl", "name": "UPITurboTPVUIPlugin", "printedName": "UPITurboTPVUIPlugin", "children": [{"kind": "Function", "name": "setOrderId", "printedName": "setOrderId(orderId:)", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVUIPlugin", "printedName": "any Razorpay.UPITurboTPVUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin(im)setOrderIdWithOrderId:", "mangledName": "$s8Razorpay19UPITurboTPVUIPluginP10setOrderId05orderF0AaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setCustomerId", "printedName": "setCustomerId(customerId:)", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVUIPlugin", "printedName": "any Razorpay.UPITurboTPVUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin(im)setCustomerIdWithCustomerId:", "mangledName": "$s8Razorpay19UPITurboTPVUIPluginP13setCustomerId08customerF0AaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setMobileNumber", "printedName": "setMobileNumber(mobile:)", "children": [{"kind": "TypeNominal", "name": "UPITurboTPVUIPlugin", "printedName": "any Razorpay.UPITurboTPVUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin(im)setMobileNumberWithMobile:", "mangledName": "$s8Razorpay19UPITurboTPVUIPluginP15setMobileNumber6mobileAaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "linkNewUpiAccountWithUI", "printedName": "linkNewUpiAccountWithUI(amountInDisplayFormat:color:completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin(im)linkNewUpiAccountWithUIWithAmountInDisplayFormat:color:completionHandler:", "mangledName": "$s8Razorpay19UPITurboTPVUIPluginP23linkNewUpiAccountWithUI21amountInDisplayFormat5color17completionHandlerySS_SSyypSg_AHtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboTPVUIPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboTPVUIPlugin>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboTPVUIPlugin", "mangledName": "$s8Razorpay19UPITurboTPVUIPluginP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "UPITurboUIInternalPlugin", "printedName": "UPITurboUIInternalPlugin", "children": [{"kind": "Function", "name": "linkNewUpiAccount", "printedName": "linkNewUpiAccount(amountInDisplayFormat:color:completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "s:8Razorpay24UPITurboUIInternalPluginP17linkNewUpiAccount21amountInDisplayFormat5color17completionHandlerySS_SSyypSg_AHtctF", "mangledName": "$s8Razorpay24UPITurboUIInternalPluginP17linkNewUpiAccount21amountInDisplayFormat5color17completionHandlerySS_SSyypSg_AHtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIInternalPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIInternalPlugin>", "protocolReq": true, "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "prefetchAndLinkUpiAccounts", "printedName": "prefetchAndLinkUpiAccounts(amountInDisplayFormat:color:completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "s:8Razorpay24UPITurboUIInternalPluginP26prefetchAndLinkUpiAccounts21amountInDisplayFormat5color17completionHandlerySS_SSyypSg_AHtctF", "mangledName": "$s8Razorpay24UPITurboUIInternalPluginP26prefetchAndLinkUpiAccounts21amountInDisplayFormat5color17completionHandlerySS_SSyypSg_AHtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboUIInternalPlugin>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboUIInternalPlugin>", "protocolReq": true, "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "s:8Razorpay24UPITurboUIInternalPluginP", "mangledName": "$s8Razorpay24UPITurboUIInternalPluginP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : Razorpay.UPITurboUIPlugin>", "sugared_genericSig": "<Self : Razorpay.UPITurboUIPlugin>", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "UPITurboUIPlugin", "printedName": "UPITurboUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin", "mangledName": "$s8Razorpay16UPITurboUIPluginP"}, {"kind": "Conformance", "name": "TokenPlugin", "printedName": "TokenPlugin", "usr": "c:@M@Razorpay@objc(pl)TokenPlugin", "mangledName": "$s8Razorpay11TokenPluginP"}, {"kind": "Conformance", "name": "UPITurboPrefetchWithUIProtocol", "printedName": "UPITurboPrefetchWithUIProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol", "mangledName": "$s8Razorpay30UPITurboPrefetchWithUIProtocolP"}]}, {"kind": "TypeDecl", "name": "UPITurboPrefetchWithUIProtocol", "printedName": "UPITurboPrefetchWithUIProtocol", "children": [{"kind": "Function", "name": "setCustomerMobile", "printedName": "setCustomerMobile(mobile:)", "children": [{"kind": "TypeNominal", "name": "UPITurboPrefetchWithUIProtocol", "printedName": "any Razorpay.UPITurboPrefetchWithUIProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol(im)setCustomerMobileWithMobile:", "mangledName": "$s8Razorpay30UPITurboPrefetchWithUIProtocolP17setCustomerMobile6mobileAaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchWithUIProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchWithUIProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setColor", "printedName": "setColor(color:)", "children": [{"kind": "TypeNominal", "name": "UPITurboPrefetchWithUIProtocol", "printedName": "any Razorpay.UPITurboPrefetchWithUIProtocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol(im)setColorWithColor:", "mangledName": "$s8Razorpay30UPITurboPrefetchWithUIProtocolP8setColor5colorAaB_pSS_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchWithUIProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchWithUIProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "prefetchAndLinkUpiAccountsWithUI", "printedName": "prefetchAndLinkUpiAccountsWithUI(completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol(im)prefetchAndLinkUpiAccountsWithUIWithCompletionHandler:", "mangledName": "$s8Razorpay30UPITurboPrefetchWithUIProtocolP026prefetchAndLinkUpiAccountsD2UI17completionHandleryyypSg_AFtc_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchWithUIProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchWithUIProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setUpiPinWithUI", "printedName": "setUpiPinWithUI(_:completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol(im)setUpiPinWithUI:completionHandler:", "mangledName": "$s8Razorpay30UPITurboPrefetchWithUIProtocolP09setUpiPinD2UI_17completionHandleryyp_yypSg_AFtctF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchWithUIProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchWithUIProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "prefetchAndLinkUpiAccountsWithUI", "printedName": "prefetchAndLinkUpiAccountsWithUI(completionHandler:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Any?, Any?) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "(Any?, Any?)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:Sq"}]}]}], "declKind": "Func", "usr": "s:8Razorpay30UPITurboPrefetchWithUIProtocolPAAE026prefetchAndLinkUpiAccountsD2UI17completionHandleryyypSg_AFtc_tF", "mangledName": "$s8Razorpay30UPITurboPrefetchWithUIProtocolPAAE026prefetchAndLinkUpiAccountsD2UI17completionHandleryyypSg_AFtc_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboPrefetchWithUIProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboPrefetchWithUIProtocol>", "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboPrefetchWithUIProtocol", "mangledName": "$s8Razorpay30UPITurboPrefetchWithUIProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "UPITurboLinkedUpiAccountsProtocol", "printedName": "UPITurboLinkedUpiAccountsProtocol", "children": [{"kind": "Function", "name": "getLinkedUpiAccounts", "printedName": "getLinkedUpiAccounts(mobileNumber:resultDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboLinkedUpiAccountsProtocol(im)getLinkedUpiAccountsWithMobileNumber:resultDelegate:", "mangledName": "$s8Razorpay33UPITurboLinkedUpiAccountsProtocolP03getcdE012mobileNumber14resultDelegateySS_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboLinkedUpiAccountsProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboLinkedUpiAccountsProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getLinkedUpiAccounts", "printedName": "getLinkedUpiAccounts(customerId:resultDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboLinkedUpiAccountsProtocol(im)getLinkedUpiAccountsWithCustomerId:resultDelegate:", "mangledName": "$s8Razorpay33UPITurboLinkedUpiAccountsProtocolP03getcdE010customerId14resultDelegateySS_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboLinkedUpiAccountsProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboLinkedUpiAccountsProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboLinkedUpiAccountsProtocol", "mangledName": "$s8Razorpay33UPITurboLinkedUpiAccountsProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "UPITurboLinkedBankAccountsProtocol", "printedName": "UPITurboLinkedBankAccountsProtocol", "children": [{"kind": "Function", "name": "getLinkedBankAccounts", "printedName": "getLinkedBankAccounts(mobileNumber:resultDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboLinkedBankAccountsProtocol(im)getLinkedBankAccountsWithMobileNumber:resultDelegate:", "mangledName": "$s8Razorpay34UPITurboLinkedBankAccountsProtocolP03getcdE012mobileNumber14resultDelegateySS_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboLinkedBankAccountsProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboLinkedBankAccountsProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getLinkedBankAccounts", "printedName": "getLinkedBankAccounts(customerId:resultDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)UPITurboLinkedBankAccountsProtocol(im)getLinkedBankAccountsWithCustomerId:resultDelegate:", "mangledName": "$s8Razorpay34UPITurboLinkedBankAccountsProtocolP03getcdE010customerId14resultDelegateySS_yptF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.UPITurboLinkedBankAccountsProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.UPITurboLinkedBankAccountsProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)UPITurboLinkedBankAccountsProtocol", "mangledName": "$s8Razorpay34UPITurboLinkedBankAccountsProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "RazorpayProtocol", "printedName": "RazorpayProtocol", "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)RazorpayProtocol", "mangledName": "$s8Razorpay0A8ProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "RazorpayResultProtocol", "printedName": "RazorpayResultProtocol", "children": [{"kind": "Function", "name": "onComplete", "printedName": "onComplete(response:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)RazorpayResultProtocol(im)onCompleteWithResponse:", "mangledName": "$s8Razorpay0A14ResultProtocolP10onComplete8responseySDys11AnyHashableVypG_tF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.RazorpayResultProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.RazorpayResultProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)RazorpayResultProtocol", "mangledName": "$s8Razorpay0A14ResultProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : Razorpay.RazorpayProtocol>", "sugared_genericSig": "<Self : Razorpay.RazorpayProtocol>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "RazorpayProtocol", "printedName": "RazorpayProtocol", "usr": "c:@M@Razorpay@objc(pl)RazorpayProtocol", "mangledName": "$s8Razorpay0A8ProtocolP"}]}, {"kind": "TypeDecl", "name": "RazorpayPaymentCompletionProtocol", "printedName": "RazorpayPaymentCompletionProtocol", "children": [{"kind": "Function", "name": "onPaymentError", "printedName": "onPaymentError(_:description:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)RazorpayPaymentCompletionProtocol(im)onPaymentError:description:", "mangledName": "$s8Razorpay0A25PaymentCompletionProtocolP02onB5Error_11descriptionys5Int32V_SStF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.RazorpayPaymentCompletionProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.RazorpayPaymentCompletionProtocol>", "deprecated": true, "protocolReq": true, "declAttributes": ["Available", "ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "onPaymentSuccess", "printedName": "onPaymentSuccess(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)RazorpayPaymentCompletionProtocol(im)onPaymentSuccess:", "mangledName": "$s8Razorpay0A25PaymentCompletionProtocolP02onB7SuccessyySSF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.RazorpayPaymentCompletionProtocol>", "sugared_genericSig": "<Self where Self : Razorpay.RazorpayPaymentCompletionProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)RazorpayPaymentCompletionProtocol", "mangledName": "$s8Razorpay0A25PaymentCompletionProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : Razorpay.RazorpayProtocol>", "sugared_genericSig": "<Self : Razorpay.RazorpayProtocol>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "RazorpayProtocol", "printedName": "RazorpayProtocol", "usr": "c:@M@Razorpay@objc(pl)RazorpayProtocol", "mangledName": "$s8Razorpay0A8ProtocolP"}]}, {"kind": "TypeDecl", "name": "RazorpayPaymentCompletionProtocolWithData", "printedName": "RazorpayPaymentCompletionProtocolWithData", "children": [{"kind": "Function", "name": "onPaymentError", "printedName": "onPaymentError(_:description:andData:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "[Swift.AnyHashable : Any]?", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)RazorpayPaymentCompletionProtocolWithData(im)onPaymentError:description:andData:", "mangledName": "$s8Razorpay0A33PaymentCompletionProtocolWithDataP02onB5Error_11description03andF0ys5Int32V_SSSDys11AnyHashableVypGSgtF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.RazorpayPaymentCompletionProtocolWithData>", "sugared_genericSig": "<Self where Self : Razorpay.RazorpayPaymentCompletionProtocolWithData>", "deprecated": true, "protocolReq": true, "declAttributes": ["Available", "ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "onPaymentSuccess", "printedName": "onPaymentSuccess(_:andData:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "[Swift.AnyHashable : Any]?", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)RazorpayPaymentCompletionProtocolWithData(im)onPaymentSuccess:andData:", "mangledName": "$s8Razorpay0A33PaymentCompletionProtocolWithDataP02onB7Success_03andF0ySS_SDys11AnyHashableVypGSgtF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.RazorpayPaymentCompletionProtocolWithData>", "sugared_genericSig": "<Self where Self : Razorpay.RazorpayPaymentCompletionProtocolWithData>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)RazorpayPaymentCompletionProtocolWithData", "mangledName": "$s8Razorpay0A33PaymentCompletionProtocolWithDataP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : Razorpay.RazorpayProtocol>", "sugared_genericSig": "<Self : Razorpay.RazorpayProtocol>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "RazorpayProtocol", "printedName": "RazorpayProtocol", "usr": "c:@M@Razorpay@objc(pl)RazorpayProtocol", "mangledName": "$s8Razorpay0A8ProtocolP"}]}, {"kind": "TypeDecl", "name": "ExternalWalletSelectionProtocol", "printedName": "ExternalWalletSelectionProtocol", "children": [{"kind": "Function", "name": "onExternalWalletSelected", "printedName": "onExternalWalletSelected(_:withPaymentData:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "[Swift.AnyHashable : Any]?", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)ExternalWalletSelectionProtocol(im)onExternalWalletSelected:withPaymentData:", "mangledName": "$s8Razorpay31ExternalWalletSelectionProtocolP02onbC8Selected_15withPaymentDataySS_SDys11AnyHashableVypGSgtF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.ExternalWalletSelectionProtocol>", "sugared_genericSig": "<Self where Self : <PERSON><PERSON><PERSON><PERSON>.ExternalWalletSelectionProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)ExternalWalletSelectionProtocol", "mangledName": "$s8Razorpay31ExternalWalletSelectionProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "TypeDecl", "name": "MagicXResultProtocol", "printedName": "MagicXResultProtocol", "children": [{"kind": "Function", "name": "onCheckoutUrlGenerated", "printedName": "onCheckoutUrlGenerated(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(pl)MagicXResultProtocol(im)onCheckoutUrlGenerated:", "mangledName": "$s8Razorpay20MagicXResultProtocolP22onCheckoutUrlGeneratedyySSF", "moduleName": "Razorpay", "genericSig": "<τ_0_0 where τ_0_0 : Razorpay.MagicXResultProtocol>", "sugared_genericSig": "<Self where Self : <PERSON><PERSON><PERSON><PERSON>.MagicXResultProtocol>", "protocolReq": true, "declAttributes": ["ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@Razorpay@objc(pl)MagicXResultProtocol", "mangledName": "$s8Razorpay20MagicXResultProtocolP", "moduleName": "Razorpay", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "PluginPaymentModel", "printedName": "PluginPaymentModel", "children": [{"kind": "Var", "name": "merchantKey", "printedName": "merchantKey", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:8Razorpay18PluginPaymentModelC11merchantKeySSvp", "mangledName": "$s8Razorpay18PluginPaymentModelC11merchantKeySSvp", "moduleName": "Razorpay", "declAttributes": ["Final", "HasStorage", "AccessControl"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:8Razorpay18PluginPaymentModelC11merchantKeySSvg", "mangledName": "$s8Razorpay18PluginPaymentModelC11merchantKeySSvg", "moduleName": "Razorpay", "implicit": true, "declAttributes": ["Final"], "accessorKind": "get"}]}, {"kind": "Var", "name": "dictPaymentInformation", "printedName": "dictPaymentInformation", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Var", "usr": "s:8Razorpay18PluginPaymentModelC04dictC11InformationSDys11AnyHashableVypGvp", "mangledName": "$s8Razorpay18PluginPaymentModelC04dictC11InformationSDys11AnyHashableVypGvp", "moduleName": "Razorpay", "declAttributes": ["Final", "HasStorage", "AccessControl"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Accessor", "usr": "s:8Razorpay18PluginPaymentModelC04dictC11InformationSDys11AnyHashableVypGvg", "mangledName": "$s8Razorpay18PluginPaymentModelC04dictC11InformationSDys11AnyHashableVypGvg", "moduleName": "Razorpay", "implicit": true, "declAttributes": ["Final"], "accessorKind": "get"}]}, {"kind": "Var", "name": "delegate", "printedName": "delegate", "children": [{"kind": "TypeNominal", "name": "PluginPaymentCompletionDelegate", "printedName": "any Razorpay.PluginPaymentCompletionDelegate", "usr": "s:8Razorpay31PluginPaymentCompletionDelegateP"}], "declKind": "Var", "usr": "s:8Razorpay18PluginPaymentModelC8delegateAA0bC18CompletionDelegate_pvp", "mangledName": "$s8Razorpay18PluginPaymentModelC8delegateAA0bC18CompletionDelegate_pvp", "moduleName": "Razorpay", "declAttributes": ["Final", "HasStorage", "AccessControl"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "PluginPaymentCompletionDelegate", "printedName": "any Razorpay.PluginPaymentCompletionDelegate", "usr": "s:8Razorpay31PluginPaymentCompletionDelegateP"}], "declKind": "Accessor", "usr": "s:8Razorpay18PluginPaymentModelC8delegateAA0bC18CompletionDelegate_pvg", "mangledName": "$s8Razorpay18PluginPaymentModelC8delegateAA0bC18CompletionDelegate_pvg", "moduleName": "Razorpay", "implicit": true, "declAttributes": ["Final"], "accessorKind": "get"}]}, {"kind": "Function", "name": "getMerchantKey", "printedName": "getMerchantKey()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "s:8Razorpay18PluginPaymentModelC14getMerchantKeySSyF", "mangledName": "$s8Razorpay18PluginPaymentModelC14getMerchantKeySSyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getPaymentInfoDict", "printedName": "getPaymentInfoDict()", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Func", "usr": "s:8Razorpay18PluginPaymentModelC03getC8InfoDictSDys11AnyHashableVypGyF", "mangledName": "$s8Razorpay18PluginPaymentModelC03getC8InfoDictSDys11AnyHashableVypGyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getDelegate", "printedName": "getDelegate()", "children": [{"kind": "TypeNominal", "name": "PluginPaymentCompletionDelegate", "printedName": "any Razorpay.PluginPaymentCompletionDelegate", "usr": "s:8Razorpay31PluginPaymentCompletionDelegateP"}], "declKind": "Func", "usr": "s:8Razorpay18PluginPaymentModelC11getDelegateAA0bc10CompletionF0_pyF", "mangledName": "$s8Razorpay18PluginPaymentModelC11getDelegateAA0bc10CompletionF0_pyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init()", "children": [{"kind": "TypeNominal", "name": "PluginPaymentModel", "printedName": "Razorpay.PluginPaymentModel", "usr": "c:@M@Razorpay@objc(cs)PluginPaymentModel"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@Razorpay@objc(cs)PluginPaymentModel(im)init", "mangledName": "$s8Razorpay18PluginPaymentModelCACycfc", "moduleName": "Razorpay", "overriding": true, "implicit": true, "objc_name": "init", "declAttributes": ["Dynamic", "ObjC", "Override"], "init_kind": "Designated"}], "declKind": "Class", "usr": "c:@M@Razorpay@objc(cs)PluginPaymentModel", "mangledName": "$s8Razorpay18PluginPaymentModelC", "moduleName": "Razorpay", "declAttributes": ["AccessControl", "ObjC"], "superclassUsr": "c:objc(cs)NSObject", "hasMissingDesignatedInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "SystemConfiguration", "printedName": "SystemConfiguration", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Photos", "printedName": "Photos", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "TypeDecl", "name": "<PERSON><PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON><PERSON>", "children": [{"kind": "Function", "name": "initWithWebView", "printedName": "initWithWebView(_:andMerchantKey:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "WKWebView", "printedName": "WebKit.WKWebView", "usr": "c:objc(cs)WKWebView"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)Otpelf(cm)initWithWebView:andMerchantKey:", "mangledName": "$s8Razorpay6OtpelfC15initWithWebView_14andMerchantKeyySo05WKWebF0C_SSSgtFZ", "moduleName": "Razorpay", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getSharedInstance", "printedName": "getSharedInstance()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Razorpay.Otpelf?", "children": [{"kind": "TypeNominal", "name": "<PERSON><PERSON><PERSON><PERSON>", "printedName": "Razorpay.Otpelf", "usr": "c:@M@Razorpay@objc(cs)Otpelf"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)Otpelf(cm)getSharedInstance", "mangledName": "$s8Razorpay6OtpelfC17getSharedInstanceACSgyFZ", "moduleName": "Razorpay", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setPaymentData", "printedName": "setPaymentData(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)Otpelf(im)setPaymentData:", "mangledName": "$s8Razorpay6OtpelfC14setPaymentDatayySDys11AnyHashableVypGF", "moduleName": "Razorpay", "declAttributes": ["AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "webView", "printedName": "webView(didFinish:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "ImplicitlyUnwrappedOptional", "printedName": "WebKit.WKNavigation?", "children": [{"kind": "TypeNominal", "name": "WKNavigation", "printedName": "WebKit.WKNavigation", "usr": "c:objc(cs)WKNavigation"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)Otpelf(im)webViewWithDidFinish:error:", "mangledName": "$s8Razorpay6OtpelfC7webView9didFinishySo12WKNavigationCSg_tKF", "moduleName": "Razorpay", "objc_name": "webViewWithDidFinish:error:", "declAttributes": ["AccessControl", "ObjC"], "throwing": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "close", "printedName": "close()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)Otpelf(im)close", "mangledName": "$s8Razorpay6OtpelfC5closeyyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:@M@Razorpay@objc(cs)Otpelf", "mangledName": "$s8Razorpay6OtpelfC", "moduleName": "Razorpay", "declAttributes": ["AccessControl", "ObjC"], "superclassUsr": "c:objc(cs)NSObject", "hasMissingDesignatedInitializers": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "CXAvailability", "printedName": "CXAvailability", "declKind": "Class", "usr": "s:8Razorpay14CXAvailabilityC", "mangledName": "$s8Razorpay14CXAvailabilityC", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "hasMissingDesignatedInitializers": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "TypeDecl", "name": "CXError", "printedName": "CXError", "declKind": "Struct", "usr": "s:8Razorpay7CXErrorV", "mangledName": "$s8Razorpay7CXErrorV", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "CoreTelephony", "printedName": "CoreTelephony", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "TurboPluginManager", "printedName": "TurboPluginManager", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init()", "children": [{"kind": "TypeNominal", "name": "TurboPluginManager", "printedName": "Razorpay.TurboPluginManager", "usr": "s:8Razorpay18TurboPluginManagerC"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:8Razorpay18TurboPluginManagerCACycfc", "mangledName": "$s8Razorpay18TurboPluginManagerCACycfc", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "init_kind": "Designated"}, {"kind": "Function", "name": "getSessionId", "printedName": "getSessionId()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "s:8Razorpay18TurboPluginManagerC12getSessionIdSSSgyF", "mangledName": "$s8Razorpay18TurboPluginManagerC12getSessionIdSSSgyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getMercahntKey", "printedName": "getMercahntKey()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "s:8Razorpay18TurboPluginManagerC14getMercahntKeySSSgyF", "mangledName": "$s8Razorpay18TurboPluginManagerC14getMercahntKeySSSgyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getHTMLLoaingPage", "printedName": "getHTMLLoaingPage()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "s:8Razorpay18TurboPluginManagerC17getHTMLLoaingPageSSyF", "mangledName": "$s8Razorpay18TurboPluginManagerC17getHTMLLoaingPageSSyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "isCFBEnabledUser", "printedName": "isCFBEnabledUser()", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "s:8Razorpay18TurboPluginManagerC16isCFBEnabledUserSbyF", "mangledName": "$s8Razorpay18TurboPluginManagerC16isCFBEnabledUserSbyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getBaseAnalyticsProperties", "printedName": "getBaseAnalyticsProperties()", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Func", "usr": "s:8Razorpay18TurboPluginManagerC26getBaseAnalyticsPropertiesSDys11AnyHashableVypGyF", "mangledName": "$s8Razorpay18TurboPluginManagerC26getBaseAnalyticsPropertiesSDys11AnyHashableVypGyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "trackEvent", "printedName": "trackEvent(eventName:payload:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Func", "usr": "s:8Razorpay18TurboPluginManagerC10trackEvent9eventName7payloadySS_SDys11AnyHashableVypGtF", "mangledName": "$s8Razorpay18TurboPluginManagerC10trackEvent9eventName7payloadySS_SDys11AnyHashableVypGtF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "submitAnalyticsEvents", "printedName": "submitAnalyticsEvents()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "s:8Razorpay18TurboPluginManagerC21submitAnalyticsEventsyyF", "mangledName": "$s8Razorpay18TurboPluginManagerC21submitAnalyticsEventsyyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "gatherAnalyticsDataCustomUI", "printedName": "gatherAnalyticsDataCustomUI()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "[Swift.String : Any]?", "children": [{"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.String : Any]", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "s:8Razorpay18TurboPluginManagerC27gatherAnalyticsDataCustomUISDySSypGSgyF", "mangledName": "$s8Razorpay18TurboPluginManagerC27gatherAnalyticsDataCustomUISDySSypGSgyF", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "s:8Razorpay18TurboPluginManagerC", "mangledName": "$s8Razorpay18TurboPluginManagerC", "moduleName": "Razorpay", "declAttributes": ["AccessControl"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "TypeDecl", "name": "RazorpayCheckout", "printedName": "RazorpayCheckout", "children": [{"kind": "Var", "name": "upiTurbo", "printedName": "upiTurbo", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboUIPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboUIPlugin", "printedName": "any Razorpay.UPITurboUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(py)upiTurbo", "mangledName": "$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvp", "moduleName": "Razorpay", "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl", "ObjC"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboUIPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboUIPlugin", "printedName": "any Razorpay.UPITurboUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)upiTurbo", "mangledName": "$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvg", "moduleName": "Razorpay", "implicit": true, "declAttributes": ["Final", "ObjC"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboUIPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboUIPlugin", "printedName": "any Razorpay.UPITurboUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)setUpiTurbo:", "mangledName": "$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvs", "moduleName": "Razorpay", "implicit": true, "declAttributes": ["Final", "ObjC"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvM", "mangledName": "$s8Razorpay0A8CheckoutC8upiTurboAA16UPITurboUIPlugin_pSgvM", "moduleName": "Razorpay", "implicit": true, "declAttributes": ["Final"], "accessorKind": "_modify"}]}, {"kind": "Function", "name": "initWithKey", "printedName": "initWithKey(_:andDelegate:)", "children": [{"kind": "TypeNominal", "name": "RazorpayCheckout", "printedName": "Razorpay.RazorpayCheckout", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "RazorpayProtocol", "printedName": "any Razorpay.RazorpayProtocol", "usr": "c:@M@Razorpay@objc(pl)RazorpayProtocol"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(cm)initWithKey:andDelegate:", "mangledName": "$s8Razorpay0A8CheckoutC11initWithKey_11andDelegateACSS_AA0A8Protocol_ptFZ", "moduleName": "Razorpay", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "initWithKey", "printedName": "initWithKey(_:andDelegateWithData:)", "children": [{"kind": "TypeNominal", "name": "RazorpayCheckout", "printedName": "Razorpay.RazorpayCheckout", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "RazorpayPaymentCompletionProtocolWithData", "printedName": "any Razorpay.RazorpayPaymentCompletionProtocolWithData", "usr": "c:@M@Razorpay@objc(pl)RazorpayPaymentCompletionProtocolWithData"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(cm)initWithKey:andDelegateWithData:", "mangledName": "$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4DataACSS_AA0a25PaymentCompletionProtocoldH0_ptFZ", "moduleName": "Razorpay", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "initWithKey", "printedName": "initWithKey(_:andDelegateWithData:plugin:)", "children": [{"kind": "TypeNominal", "name": "RazorpayCheckout", "printedName": "Razorpay.RazorpayCheckout", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "RazorpayPaymentCompletionProtocolWithData", "printedName": "any Razorpay.RazorpayPaymentCompletionProtocolWithData", "usr": "c:@M@Razorpay@objc(pl)RazorpayPaymentCompletionProtocolWithData"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "(any Razorpay.UPITurboUIPlugin)?", "children": [{"kind": "TypeNominal", "name": "UPITurboUIPlugin", "printedName": "any Razorpay.UPITurboUIPlugin", "usr": "c:@M@Razorpay@objc(pl)UPITurboUIPlugin"}], "usr": "s:Sq"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(cm)initWithKey:andDelegateWithData:plugin:", "mangledName": "$s8Razorpay0A8CheckoutC11initWithKey_011andDelegateD4Data6pluginACSS_AA0a25PaymentCompletionProtocoldH0_pAA16UPITurboUIPlugin_pSgtFZ", "moduleName": "Razorpay", "static": true, "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "publishUri", "printedName": "publishUri(with:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(cm)publishUriWith:", "mangledName": "$s8Razorpay0A8CheckoutC10publishUri4withySS_tFZ", "moduleName": "Razorpay", "static": true, "objc_name": "publishUriWith:", "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setExternalWalletSelectionDelegate", "printedName": "setExternalWalletSelectionDelegate(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "ExternalWalletSelectionProtocol", "printedName": "any Razorpay.ExternalWalletSelectionProtocol", "usr": "c:@M@Razorpay@objc(pl)ExternalWalletSelectionProtocol"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)setExternalWalletSelectionDelegate:", "mangledName": "$s8Razorpay0A8CheckoutC34setExternalWalletSelectionDelegateyyAA0deF8Protocol_pF", "moduleName": "Razorpay", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "open", "printedName": "open(_:displayController:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}, {"kind": "TypeNominal", "name": "UIViewController", "printedName": "UIKit.UIViewController", "usr": "c:objc(cs)UIViewController"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)open:displayController:", "mangledName": "$s8Razorpay0A8CheckoutC4open_17displayControllerySDys11AnyHashableVypG_So06UIViewE0CtF", "moduleName": "Razorpay", "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "open", "printedName": "open(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)open:", "mangledName": "$s8Razorpay0A8CheckoutC4openyySDys11AnyHashableVypGF", "moduleName": "Razorpay", "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "open", "printedName": "open(_:displayController:arrExternalPaymentEntities:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}, {"kind": "TypeNominal", "name": "UIViewController", "printedName": "UIKit.UIViewController", "usr": "c:objc(cs)UIViewController"}, {"kind": "TypeNominal", "name": "Array", "printedName": "[any Razorpay.PluginPaymentDelegate]", "children": [{"kind": "TypeNominal", "name": "PluginPaymentDelegate", "printedName": "any Razorpay.PluginPaymentDelegate", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate"}], "usr": "s:Sa"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)open:displayController:arrExternalPaymentEntities:", "mangledName": "$s8Razorpay0A8CheckoutC4open_17displayController26arrExternalPaymentEntitiesySDys11AnyHashableVypG_So06UIViewE0CSayAA06PluginH8Delegate_pGtF", "moduleName": "Razorpay", "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "checkIntegration", "printedName": "checkIntegration(withMerchantKey:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(cm)checkIntegrationWithMerchantKey:", "mangledName": "$s8Razorpay0A8CheckoutC16checkIntegration15withMerchantKeyySS_tFZ", "moduleName": "Razorpay", "static": true, "objc_name": "checkIntegrationWithMerchantKey:", "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "open", "printedName": "open(_:arrExternalPaymentEntities:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.AnyHashable : Any]", "children": [{"kind": "TypeNominal", "name": "AnyHashable", "printedName": "<PERSON>.AnyHashable", "usr": "s:s11AnyHashableV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "usr": "s:SD"}, {"kind": "TypeNominal", "name": "Array", "printedName": "[any Razorpay.PluginPaymentDelegate]", "children": [{"kind": "TypeNominal", "name": "PluginPaymentDelegate", "printedName": "any Razorpay.PluginPaymentDelegate", "usr": "c:@M@Razorpay@objc(pl)PluginPaymentDelegate"}], "usr": "s:Sa"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)open:arrExternalPaymentEntities:", "mangledName": "$s8Razorpay0A8CheckoutC4open_26arrExternalPaymentEntitiesySDys11AnyHashableVypG_SayAA06PluginF8Delegate_pGtF", "moduleName": "Razorpay", "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "openMagicX", "printedName": "openMagicX(storefrontUrl:itemsData:withDelegate:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "MagicXResultProtocol", "printedName": "any Razorpay.MagicXResultProtocol", "usr": "c:@M@Razorpay@objc(pl)MagicXResultProtocol"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)openMagicXWithStorefrontUrl:itemsData:withDelegate:", "mangledName": "$s8Razorpay0A8CheckoutC10openMagicX13storefrontUrl9itemsData12withDelegateySS_SSAA0D15XResultProtocol_ptF", "moduleName": "Razorpay", "objc_name": "openMagicXWithStorefrontUrl:itemsData:withDelegate:", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "close", "printedName": "close()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)close", "mangledName": "$s8Razorpay0A8CheckoutC5closeyyF", "moduleName": "Razorpay", "declAttributes": ["Final", "AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "clearUserData", "printedName": "clearUserData()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout(im)clearUserData", "mangledName": "$s8Razorpay0A8CheckoutC13clearUserDatayyF", "moduleName": "Razorpay", "declAttributes": ["Final", "AccessControl", "ObjC", "RawDocComment"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:@M@Razorpay@objc(cs)RazorpayCheckout", "mangledName": "$s8Razorpay0A8CheckoutC", "moduleName": "Razorpay", "declAttributes": ["Final", "AccessControl", "ObjC"], "superclassUsr": "c:objc(cs)NSObject", "hasMissingDesignatedInitializers": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CommonCrypto", "printedName": "CommonCrypto", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "WebKit", "printedName": "WebKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "CommonCrypto", "printedName": "CommonCrypto", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "SwiftUI", "printedName": "SwiftUI", "declKind": "Import", "moduleName": "Razorpay"}, {"kind": "Import", "name": "DeveloperToolsSupport", "printedName": "DeveloperToolsSupport", "declKind": "Import", "moduleName": "Razorpay"}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RemoteConfigHelper.swift", "kind": "Dictionary", "offset": 608, "length": 3, "value": "[]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RemoteConfigHelper.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 5575, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RemoteConfigHelper.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 5621, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RemoteConfigHelper.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 6679, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RemoteConfigHelper.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 6725, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayInternetUtils.swift", "kind": "StringLiteral", "offset": 372, "length": 16, "value": "\"Razorpay Queue\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/AnalyticsUtil.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 226, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/AnalyticsUtil.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 484, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/AnalyticsUtil.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 526, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutBridgeErrorResponse.swift", "kind": "StringLiteral", "offset": 399, "length": 6, "value": "\"code\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/PluginPaymentProtocols.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 3851, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 421, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 628, "length": 10, "value": "\"rzp_test\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 722, "length": 24, "value": "\"USER_CANCELLED_PAYMENT\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 787, "length": 26, "value": "\"https://api.razorpay.com\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 851, "length": 18, "value": "\"api.razorpay.com\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 961, "length": 8, "value": "\"3.0.27\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1027, "length": 7, "value": "\"Apple\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1069, "length": 5, "value": "\"iOS\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1101, "length": 11, "value": "\"Razorpay_\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1145, "length": 6, "value": "\"_iOS\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1412, "length": 18, "value": "\"merchant_options\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1452, "length": 5, "value": "\"url\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1484, "length": 10, "value": "\"timezone\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1525, "length": 14, "value": "\"merchant_key\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1576, "length": 20, "value": "\"delegate_with_data\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1629, "length": 16, "value": "\"Checkout Login\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1682, "length": 20, "value": "\"webview_user_agent\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1801, "length": 24, "value": "\"HIDE_OPINIONATED_ALERT\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1867, "length": 15, "value": "\"RZP_DEVICE_ID\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 1959, "length": 36, "value": "\"RZP_BUTLER_CURRENT_SETTING_VERSION\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 2495, "length": 19, "value": "\"rzp_remote_config\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 2600, "length": 35, "value": "\"RzpUnhandledExceptionsDetails.txt\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 2704, "length": 20, "value": "\"RZP_OTPELF_JS_FILE\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 2758, "length": 17, "value": "\"RZP_OTPELF_HASH\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 2841, "length": 47, "value": "\"https://beta-lumberjack.razorpay.com/v1/track\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 2926, "length": 42, "value": "\"https://lumberjack.razorpay.com/v1/track\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 3006, "length": 46, "value": "\"mqrkrLdcszc2MZR/PXN1QWHVui8WnkP7c0BrYBDcicA=\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 3174, "length": 61, "value": "\"https://lumberjack-metrics.razorpay.com/v1/frontend-metrics\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/RazorpayConstants.swift", "kind": "StringLiteral", "offset": 3278, "length": 18984, "value": "\"<html><head><title>Processing, Please Wait...</title><meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"/><meta http-equiv=\"pragma\" content=\"no-cache\"><meta http-equiv=\"cache-control\" content=\"no-cache\"><style><style> html, body { width: 100%; } #content { width: 88%; max-width: 520px; text-align: center; position: absolute; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); top: 50%; left: 50%; } #title { color: #528ff0; font-size: 22px; } #ldr { width: 100%; height: 1px; position: relative; background: #e1e1e1; } #lding { width: 0; height: 3px; top: -1px; background: #528ff0; position: absolute; animation: spin 20s cubic-bezier(0,0.1,0,1) forwards; -webkit-animation: spin 20s cubic-bezier(0,0.1,0,1) forwards; } @-webkit-keyframes spin { 0% { width: 0; } 100% { width: 90%; } } @keyframes spin { 0% { width: 0; } 100% { width: 90%; } } #desc { font-size: 16px; color: #8a8a8a; } #content > div { margin-bottom: 20px; } form { display: none; } .hide { display: none; }</style></head><body><div id=\"content\"> <div id=\"title\">Processing Payment</div> <div id=\"ldr\"> <div id=\"lding\"></div> </div> <div id=\"desc\">Please wait while we fetch your transaction details and process your payment</div> <img id=\"rzplogo\" style=\"width:160px;margin-top:80px\" src=\"data:image/png;base64,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\"/></div></body> <script> function post(path, params) { var method = \"post\"; var form = document.createElement(\"form\"); form.setAttribute(\"method\", method); form.setAttribute(\"action\", path); for(var key in params) { if(params.hasOwnProperty(key)) { var hiddenField = document.createElement(\"input\"); hiddenField.setAttribute(\"type\", \"hidden\"); hiddenField.setAttribute(\"name\", key); hiddenField.setAttribute(\"value\", params[key]); form.appendChild(hiddenField);}} document.body.appendChild(form); form.submit(); } function hideRzpLogo(){ document.getElementById('rzplogo').className = 'hide'; }</script> </html>\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/InternalOtpelf.swift", "kind": "Array", "offset": 3113, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/InternalOtpelf.swift", "kind": "StringLiteral", "offset": 3323, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/InternalOtpelf.swift", "kind": "Array", "offset": 3555, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/InternalOtpelf.swift", "kind": "StringLiteral", "offset": 3731, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/InternalRazorpay.swift", "kind": "StringLiteral", "offset": 388, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/InternalRazorpay.swift", "kind": "IntegerLiteral", "offset": 1029, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/InternalRazorpay.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1055, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/InternalRazorpay.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1088, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/InternalRazorpay.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 3940, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/InternalRazorpay.swift", "kind": "StringLiteral", "offset": 12016, "length": 11, "value": "\"rURIEvent\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/NavigationManager.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 376, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/NavigationManager.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 484, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/NetworkDetails.swift", "kind": "StringLiteral", "offset": 518, "length": 33, "value": "\"ReachabilityChangedNotification\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/NetworkDetails.swift", "kind": "StringLiteral", "offset": 641, "length": 21, "value": "\"reachabilityChanged\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/NetworkDetails.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2039, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/NetworkDetails.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 3753, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/NetworkDetails.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 3824, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/NetworkDetails.swift", "kind": "StringLiteral", "offset": 3961, "length": 32, "value": "\"uk.co.ashleymills.reachability\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 15882, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 15978, "length": 27, "value": "\"could not get data source\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "FloatLiteral", "offset": 16165, "length": 3, "value": "7.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "FloatLiteral", "offset": 16169, "length": 5, "value": "255.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "FloatLiteral", "offset": 16183, "length": 4, "value": "39.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "FloatLiteral", "offset": 16188, "length": 5, "value": "255.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "FloatLiteral", "offset": 16201, "length": 4, "value": "83.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "FloatLiteral", "offset": 16206, "length": 5, "value": "255.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "IntegerLiteral", "offset": 16220, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 16345, "length": 13, "value": "\"theme_color\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 16445, "length": 7, "value": "\"alpha\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 16501, "length": 5, "value": "\"red\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 16557, "length": 7, "value": "\"green\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 16614, "length": 6, "value": "\"blue\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 16821, "length": 99, "value": "\"body in onLoad has a missing value of one or more colors. Filling default color -> body = \""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 16919, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "IntegerLiteral", "offset": 17237, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "IntegerLiteral", "offset": 17257, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "IntegerLiteral", "offset": 17274, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "IntegerLiteral", "offset": 17293, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 17483, "length": 7, "value": "\"theme\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 17542, "length": 7, "value": "\"color\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 17671, "length": 58, "value": "\"body in onLoad doesn't have theme_color - body = \""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 17728, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 18159, "length": 84, "value": "\"invalid hex passed in the theme color initOptions -> hexString = \""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 18242, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 18557, "length": 58, "value": "\"body in onLoad doesn't have theme_color - body = \""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "StringLiteral", "offset": 18614, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout+JSBridge.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 18863, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayMagicxVC.swift", "kind": "StringLiteral", "offset": 331, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayMagicxVC.swift", "kind": "StringLiteral", "offset": 362, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayMagicxVC.swift", "kind": "StringLiteral", "offset": 392, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OpinioatedSDKChecksHandler.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1280, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OpinioatedSDKChecksHandler.swift", "kind": "StringLiteral", "offset": 1314, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OpinioatedSDKChecksHandler.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1485, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OpinioatedSDKChecksHandler.swift", "kind": "Array", "offset": 1536, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OpinioatedSDKChecksHandler.swift", "kind": "StringLiteral", "offset": 1567, "length": 56, "value": "\"https://github.com/razorpay/razorpay-pod/releases/tag/\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OpinioatedSDKChecksHandler.swift", "kind": "StringLiteral", "offset": 1657, "length": 54, "value": "\"A version update was found. Click here to go to docs\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OpinioatedSDKChecksHandler.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1746, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OpinioatedSDKChecksHandler.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1924, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 312, "length": 10, "value": "\"Checkout\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 357, "length": 29, "value": "\"com.razorpay.CheckoutOtpelf\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 431, "length": 34, "value": "\"ADs2QQrYoddL3DH2JGNqoxUXkREp6j2q\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 500, "length": 34, "value": "\"M1Hau4XAZ3UBWGyXngFYnFsfRdhxLjfS\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 573, "length": 34, "value": "\"rgsbC1zmcIwZ5qolhXXAO0CZdBtpAff5\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 641, "length": 34, "value": "\"EPl0bxz9OvsD5IylM1M28Mv2n3v9XBsr\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 716, "length": 80, "value": "\"https://stage-butler.razorpay.in/v1/settings?tenant=ios_customui&version=1.0.*\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 836, "length": 79, "value": "\"https://butler.razorpay.com/v1/settings?tenant=android_customui&version=3.0.*\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 960, "length": 34, "value": "\"qGcvFap9keVGEN78OyRbSbYQYvlM6N56\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 1040, "length": 75, "value": "\"https://butler.razorpay.com/v1/settings?tenant=ios_customui&version=1.0.*\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 1158, "length": 34, "value": "\"ZDRhODRiMWM4NTc1MjI5MWViN2M4MWI0\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 1225, "length": 14, "value": "\"standard_ios\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 1272, "length": 10, "value": "\"standard\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/CheckoutConstants.swift", "kind": "StringLiteral", "offset": 1317, "length": 7, "value": "\"1.4.1\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 314, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "StringLiteral", "offset": 358, "length": 64, "value": "\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "IntegerLiteral", "offset": 473, "length": 10, "value": "1388534400"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "IntegerLiteral", "offset": 521, "length": 8, "value": "14776336"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "StringLiteral", "offset": 767, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "StringLiteral", "offset": 808, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "StringLiteral", "offset": 842, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "StringLiteral", "offset": 873, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "StringLiteral", "offset": 919, "length": 26, "value": "\"com.razorpay.serialQueue\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1093, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "StringLiteral", "offset": 15752, "length": 64, "value": "\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Lumberjack.swift", "kind": "IntegerLiteral", "offset": 15865, "length": 2, "value": "62"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 289, "length": 47, "value": "\"Unable to fetch payment methods. Please retry\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 370, "length": 47, "value": "\"No key 'methods' in the preferences returned.\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 461, "length": 51, "value": "\"Unable to fetch. Kindly check the subscription id\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 546, "length": 39, "value": "\"Error in fetching webview user agent.\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 617, "length": 31, "value": "\"UPI App is/are not Available.\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 687, "length": 66, "value": "\"Unable to json serialize the data returned in getPaymentMethods.\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 790, "length": 92, "value": "\"Something went  wrong, please try again! If issue persists <NAME_EMAIL>\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 925, "length": 38, "value": "\"Payment processing cancelled by user\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1029, "length": 144, "value": "\"Are you sure you want to cancel the current transaction ? You will be taken back to checkout page, where you can choose another payment option\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1218, "length": 24, "value": "\"User Cancelled Payment\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1320, "length": 6, "value": "\"code\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1328, "length": 19, "value": "\"BAD_REQUEST_ERROR\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1349, "length": 13, "value": "\"description\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1364, "length": 38, "value": "\"Payment processing cancelled by user\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1404, "length": 8, "value": "\"source\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1414, "length": 10, "value": "\"customer\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1426, "length": 6, "value": "\"step\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1434, "length": 24, "value": "\"payment_authentication\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1460, "length": 8, "value": "\"reason\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Constants.swift", "kind": "StringLiteral", "offset": 1470, "length": 19, "value": "\"payment_cancelled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckoutVC.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 696, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckoutVC.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 846, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckoutVC.swift", "kind": "StringLiteral", "offset": 14694, "length": 14, "value": "\"user_aborted\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckoutVC.swift", "kind": "StringLiteral", "offset": 14732, "length": 9, "value": "\"success\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckoutVC.swift", "kind": "StringLiteral", "offset": 14764, "length": 8, "value": "\"failed\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/CrashReporter.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 282, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 384, "length": 16, "value": "\"otpelf.enabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 429, "length": 17, "value": "\"otpelf.endpoint\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 479, "length": 21, "value": "\"otpelf.js_file_name\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 538, "length": 26, "value": "\"otpelf.version_file_name\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 594, "length": 15, "value": "\"availableApps\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 641, "length": 20, "value": "\"ios.ns_exc_enabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 710, "length": 37, "value": "\"ios.unhand_exc_sleep_time_milli_sec\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 783, "length": 24, "value": "\"ios.unhand_exc_enabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 839, "length": 20, "value": "\"ios.update_sdk_msg\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 898, "length": 27, "value": "\"ios.show_sdk_update_alert\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 961, "length": 24, "value": "\"ios.latest_sdk_version\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1161, "length": 23, "value": "\"CurrentSettingVersion\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1219, "length": 9, "value": "\"AuthKey\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1267, "length": 14, "value": "\"Content-Type\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1341, "length": 16, "value": "\"Settingversion\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1416, "length": 33, "value": "\"unhand_exc_sleep_time_milli_sec\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 1451, "length": 2, "value": "30"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1463, "length": 18, "value": "\"config_end_point\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1483, "length": 41, "value": "\"https://butler.razorpay.com/v1/settings\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1534, "length": 8, "value": "\"enable\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1544, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1558, "length": 10, "value": "\"checkout\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Dictionary", "offset": 1570, "length": 51, "value": "[(\"end_point\", \"/v1/checkout\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1631, "length": 13, "value": "\"permissions\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1660, "length": 16, "value": "\"custom_message\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1678, "length": 46, "value": "\"We need SMS permission to auto fill bank OTP\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1738, "length": 23, "value": "\"enable_custom_message\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1763, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1782, "length": 15, "value": "\"max_ask_count\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 1799, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1820, "length": 8, "value": "\"otpelf\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1844, "length": 8, "value": "\"enable\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1854, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1872, "length": 10, "value": "\"settings\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Dictionary", "offset": 1884, "length": 53, "value": "[(\"platform\", \"android\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1951, "length": 10, "value": "\"endpoint\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 1963, "length": 41, "value": "\"https://cdn.razorpay.com/static/otpelf/\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2018, "length": 14, "value": "\"js_file_name\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2034, "length": 11, "value": "\"otpelf.js\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2059, "length": 19, "value": "\"version_file_name\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2080, "length": 14, "value": "\"version.json\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2114, "length": 7, "value": "\"retry\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2137, "length": 9, "value": "\"enabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2148, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2166, "length": 11, "value": "\"max_count\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 2179, "length": 2, "value": "-1"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2201, "length": 14, "value": "\"one_time_otp\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Dictionary", "offset": 2217, "length": 40, "value": "[(\"enabled\", false)]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2267, "length": 11, "value": "\"analytics\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2294, "length": 12, "value": "\"lumberjack\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2326, "length": 5, "value": "\"key\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2333, "length": 34, "value": "\"NmFhYWUwMDQ2NmMyOGUyNmFhNzQ0ODk2\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2385, "length": 16, "value": "\"sdk_identifier\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2403, "length": 17, "value": "\"customuiandroid\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2438, "length": 11, "value": "\"end_point\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2451, "length": 42, "value": "\"https://lumberjack.razorpay.com/v1/track\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2511, "length": 8, "value": "\"enable\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2521, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2559, "length": 19, "value": "\"update_sdk_config\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2594, "length": 16, "value": "\"latest_version\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 2612, "length": 2, "value": "17"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2628, "length": 5, "value": "\"msg\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2635, "length": 65, "value": "\"Razorpay: A new version of the sdk is available. Please update.\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2714, "length": 14, "value": "\"enable_alert\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2730, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2754, "length": 5, "value": "\"upi\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2775, "length": 23, "value": "\"isWhiteListingEnabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2800, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2818, "length": 17, "value": "\"blackListedApps\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Array", "offset": 2837, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 2853, "length": 17, "value": "\"whiteListedApps\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Array", "offset": 2872, "length": 1560, "value": "[\"com.google.android.apps.nbu.paisa.user\", \"in.org.npci.upiapp\", \"com.phonepe.app\", \"com.myairtelapp\", \"com.myairtelapp.debug\", \"com.csam.icici.bank.imobile\", \"net.one97.paytm\", \"com.sbi.upi\", \"com.upi.axispay\", \"com.samsung.android.spaymini\", \"com.samsung.android.spay\", \"com.snapwork.hdfc\", \"com.fss.pnbpsp\", \"com.icicibank.pockets\", \"com.bankofbaroda.upi\", \"com.freecharge.android\", \"com.fss.unbipsp\", \"com.axis.mobile\", \"com.mycompany.kvb\", \"com.fss.vijayapsp\", \"com.dena.upi.gui\", \"com.fss.jnkpsp\", \"com.olive.kotak.upi\", \"com.enstage.wibmo.hdfc\", \"com.bsb.hike\", \"com.fss.idfcpsp\", \"com.abipbl.upi\", \"com.microsoft.mobile.polymer\", \"com.finopaytech.bpayfino\", \"com.mgs.obcbank\", \"com.upi.federalbank.org.lotza\", \"com.mgs.induspsp\", \"ai.wizely.android\", \"com.olive.dcb.upi\", \"com.mgs.yesmerchantnative.prod\", \"com.example.demo\", \"in.amazon.mShop.android.shopping\", \"com.mipay.wallet.in\", \"in.bajajfinservmarkets.app\"]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4446, "length": 23, "value": "\"upiAppsPreferredOrder\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Array", "offset": 4471, "length": 226, "value": "[\"in.bajajfinservmarkets.app\", \"com.google.android.apps.nbu.paisa.user\", \"in.org.npci.upiapp\", \"com.phonepe.app\", \"net.one97.paytm\"]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4717, "length": 14, "value": "\"static_rules\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4747, "length": 18, "value": "\"identify_network\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4785, "length": 6, "value": "\"visa\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4793, "length": 4, "value": "\"^4\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4815, "length": 12, "value": "\"mastercard\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4829, "length": 18, "value": "\"^(5[1-5]|2[2-7])\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4865, "length": 11, "value": "\"maestro16\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4878, "length": 38, "value": "\"^50(81(25|26|59|92)|8227)|4(437|681)\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4934, "length": 6, "value": "\"amex\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4942, "length": 8, "value": "\"^3[47]\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4968, "length": 7, "value": "\"rupay\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 4977, "length": 131, "value": "\"^(508[5-9]|60(80(0|)[^0]|8[1-4]|8500|698[5-9]|699|7[^9]|79[0-7]|798[0-4])|65(2(1[5-9]|[2-9])|30|31[0-4])|817[2-9]|81[89]|820[01])\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5126, "length": 10, "value": "\"discover\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5138, "length": 19, "value": "\"^(65[1,3-9]|6011)\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5175, "length": 9, "value": "\"maestro\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5186, "length": 21, "value": "\"^(6|5(0|[6-9])).{5}\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5225, "length": 8, "value": "\"diners\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5235, "length": 10, "value": "\"^3[0689]\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5263, "length": 5, "value": "\"jcb\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5270, "length": 5, "value": "\"^35\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5303, "length": 21, "value": "\"network_card_length\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5344, "length": 5, "value": "\"jcb\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5351, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5371, "length": 10, "value": "\"discover\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5383, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5403, "length": 6, "value": "\"visa\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5411, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5431, "length": 12, "value": "\"mastercard\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5445, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5465, "length": 6, "value": "\"amex\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5473, "length": 2, "value": "15"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5493, "length": 8, "value": "\"diners\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5503, "length": 2, "value": "14"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5523, "length": 9, "value": "\"maestro\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5534, "length": 2, "value": "19"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5554, "length": 11, "value": "\"maestro16\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5567, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5587, "length": 9, "value": "\"default\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5598, "length": 2, "value": "19"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5618, "length": 7, "value": "\"rupay\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5627, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5657, "length": 7, "value": "\"logos\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5684, "length": 8, "value": "\"wallet\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Dictionary", "offset": 5694, "length": 126, "value": "[(\"base_url\", \"https://cdn.razorpay.com/wallet/\"), (\"extension\", \"png\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5838, "length": 11, "value": "\"wallet_sq\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Dictionary", "offset": 5851, "length": 129, "value": "[(\"base_url\", \"https://cdn.razorpay.com/wallet-sq/\"), (\"extension\", \"png\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 5998, "length": 6, "value": "\"bank\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Dictionary", "offset": 6006, "length": 124, "value": "[(\"base_url\", \"https://cdn.razorpay.com/bank/\"), (\"extension\", \"gif\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6164, "length": 5, "value": "\"ios\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6185, "length": 20, "value": "\"unhand_exc_enabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 6207, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6225, "length": 33, "value": "\"unhand_exc_sleep_time_milli_sec\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "IntegerLiteral", "offset": 6260, "length": 2, "value": "30"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6276, "length": 16, "value": "\"ns_exc_enabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 6294, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6312, "length": 16, "value": "\"update_sdk_msg\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6330, "length": 85, "value": "\"A new version of the sdk is available (shown only when using test key or simulator)\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6429, "length": 23, "value": "\"show_sdk_update_alert\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 6454, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6472, "length": 20, "value": "\"latest_sdk_version\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6494, "length": 7, "value": "\"1.1.1\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "StringLiteral", "offset": 6515, "length": 15, "value": "\"availableApps\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "Array", "offset": 6532, "length": 5669, "value": "[[(\"uriScheme\", \"tez://upi/pay\"), (\"mandateUriScheme\", \"tez://upi/mandate\"), (\"appName\", \"Google Pay\"), (\"shortcode\", \"google_pay\")], [(\"uriScheme\", \"phonepe://pay\"), (\"mandateUriScheme\", \"phonepe://mandate\"), (\"appName\", \"PhonePe\"), (\"shortcode\", \"phonepe\")], [(\"uriScheme\", \"paytmmp://upi/pay\"), (\"mandateUriScheme\", \"paytmmp://upi/mandate\"), (\"appName\", \"Paytm\"), (\"shortcode\", \"paytm\")], [(\"uriScheme\", \"credpay://upi/pay\"), (\"appName\", \"CRED\"), (\"shortcode\", \"cred\")], [(\"uriScheme\", \"mobikwik://upi/pay\"), (\"appName\", \"Mobikwik\"), (\"shortcode\", \"mobikwik\")], [(\"uriScheme\", \"freecharge://upi/pay\"), (\"appName\", \"Freecharge\"), (\"shortcode\", \"freecharge\")], [(\"uriScheme\", \"amazonpay://upi/pay\"), (\"appName\", \"AmazonPay\"), (\"shortcode\", \"amazonpay\")], [(\"uriScheme\", \"navi://upi/pay\"), (\"appName\", \"Navi\"), (\"shortcode\", \"navi\")], [(\"uriScheme\", \"kiwi://upi/pay\"), (\"appName\", \"Kiwi\"), (\"shortcode\", \"kiwi\")], [(\"uriScheme\", \"payzapp://upi/pay\"), (\"appName\", \"PayZapp\"), (\"shortcode\", \"payzapp\")], [(\"uriScheme\", \"jupiter://upi/pay\"), (\"appName\", \"Jupiter\"), (\"shortcode\", \"jupiter\")], [(\"uriScheme\", \"sbiyono://upi/pay\"), (\"appName\", \"SBI Yono\"), (\"shortcode\", \"sbiyono\")], [(\"uriScheme\", \"whatsapp://upi/pay\"), (\"appName\", \"Whatsapp\"), (\"shortcode\", \"whatsapp\")], [(\"uriScheme\", \"slice://upi/pay\"), (\"appName\", \"Slice\"), (\"shortcode\", \"slice\")], [(\"uriScheme\", \"bobupi://upi/pay\"), (\"appName\", \"BoB World UPI\"), (\"shortcode\", \"bobupi\")], [(\"uriScheme\", \"myjio://upi/pay\"), (\"appName\", \"MyJio\"), (\"shortcode\", \"myjio\")], [(\"uriScheme\", \"freecharge://upi/pay\"), (\"appName\", \"Freecharge\"), (\"shortcode\", \"freecharge\")], [(\"uriScheme\", \"omnicard://upi/pay\"), (\"appName\", \"Omnicard\"), (\"shortcode\", \"omnicard\")], [(\"uriScheme\", \"shriramone://upi/pay\"), (\"appName\", \"Shriram One\"), (\"shortcode\", \"shriramone\")], [(\"uriScheme\", \"indusmobile://upi/pay\"), (\"appName\", \"IndusInd\"), (\"shortcode\", \"indusmobile\")], [(\"uriScheme\", \"bhim://upi/pay\"), (\"appName\", \"Bhim\"), (\"shortcode\", \"bhim\")], [(\"uriScheme\", \"popclubapp://upi/pay\"), (\"appName\", \"PopClub\"), (\"shortcode\", \"popclubapp\")], [(\"uriScheme\", \"in.fampay.app://upi/pay\"), (\"appName\", \"Fampay\"), (\"shortcode\", \"fampay\")]]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/BaseRemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 12412, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OtpelfConstants.swift", "kind": "StringLiteral", "offset": 251, "length": 11, "value": "\"JsStorage\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OtpelfConstants.swift", "kind": "StringLiteral", "offset": 337, "length": 10, "value": "\"platform\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OtpelfConstants.swift", "kind": "StringLiteral", "offset": 349, "length": 5, "value": "\"ios\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OtpelfConstants.swift", "kind": "StringLiteral", "offset": 364, "length": 5, "value": "\"sdk\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OtpelfConstants.swift", "kind": "StringLiteral", "offset": 385, "length": 6, "value": "\"type\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OtpelfConstants.swift", "kind": "StringLiteral", "offset": 433, "length": 9, "value": "\"version\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/OtpelfConstants.swift", "kind": "StringLiteral", "offset": 607, "length": 87406, "value": "\"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\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 275, "length": 31, "value": "\"static_rules.identify_network\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 338, "length": 34, "value": "\"static_rules.network_card_length\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 407, "length": 36, "value": "\"static_rules.logos.wallet.base_url\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 478, "length": 37, "value": "\"static_rules.logos.wallet.extension\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 553, "length": 39, "value": "\"static_rules.logos.wallet_sq.base_url\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 630, "length": 40, "value": "\"static_rules.logos.wallet_sq.extension\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 703, "length": 34, "value": "\"static_rules.logos.bank.base_url\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 770, "length": 35, "value": "\"static_rules.logos.bank.extension\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 833, "length": 19, "value": "\"ios.availableApps\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1230, "length": 20, "value": "\"latest_sdk_version\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1298, "length": 18, "value": "\"config_end_point\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1318, "length": 41, "value": "\"https://butler.razorpay.com/v1/settings\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1369, "length": 8, "value": "\"enable\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1379, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1393, "length": 10, "value": "\"checkout\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Dictionary", "offset": 1405, "length": 51, "value": "[(\"end_point\", \"/v1/checkout\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1466, "length": 13, "value": "\"permissions\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1495, "length": 16, "value": "\"custom_message\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1513, "length": 46, "value": "\"We need SMS permission to auto fill bank OTP\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1573, "length": 23, "value": "\"enable_custom_message\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1598, "length": 5, "value": "false"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1617, "length": 15, "value": "\"max_ask_count\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 1634, "length": 1, "value": "1"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1655, "length": 8, "value": "\"otpelf\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1679, "length": 8, "value": "\"enable\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1689, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1707, "length": 10, "value": "\"settings\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Dictionary", "offset": 1719, "length": 53, "value": "[(\"platform\", \"android\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1786, "length": 10, "value": "\"endpoint\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1798, "length": 41, "value": "\"https://cdn.razorpay.com/static/otpelf/\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1853, "length": 14, "value": "\"js_file_name\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1869, "length": 11, "value": "\"otpelf.js\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1894, "length": 19, "value": "\"version_file_name\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1915, "length": 14, "value": "\"version.json\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1949, "length": 7, "value": "\"retry\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 1972, "length": 9, "value": "\"enabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1983, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2001, "length": 11, "value": "\"max_count\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 2014, "length": 2, "value": "-1"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2036, "length": 14, "value": "\"one_time_otp\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Dictionary", "offset": 2052, "length": 40, "value": "[(\"enabled\", false)]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2102, "length": 11, "value": "\"analytics\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2129, "length": 12, "value": "\"lumberjack\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2161, "length": 5, "value": "\"key\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2168, "length": 34, "value": "\"NmFhYWUwMDQ2NmMyOGUyNmFhNzQ0ODk2\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2220, "length": 16, "value": "\"sdk_identifier\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2238, "length": 17, "value": "\"customuiandroid\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2273, "length": 11, "value": "\"end_point\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2286, "length": 42, "value": "\"https://lumberjack.razorpay.com/v1/track\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2346, "length": 8, "value": "\"enable\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2356, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2394, "length": 19, "value": "\"update_sdk_config\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2429, "length": 16, "value": "\"latest_version\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 2447, "length": 2, "value": "17"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2463, "length": 5, "value": "\"msg\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2470, "length": 65, "value": "\"Razorpay: A new version of the sdk is available. Please update.\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2549, "length": 14, "value": "\"enable_alert\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2565, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2589, "length": 5, "value": "\"upi\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2610, "length": 23, "value": "\"isWhiteListingEnabled\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 2635, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2653, "length": 17, "value": "\"blackListedApps\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Array", "offset": 2672, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 2688, "length": 17, "value": "\"whiteListedApps\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Array", "offset": 2707, "length": 1560, "value": "[\"com.google.android.apps.nbu.paisa.user\", \"in.org.npci.upiapp\", \"com.phonepe.app\", \"com.myairtelapp\", \"com.myairtelapp.debug\", \"com.csam.icici.bank.imobile\", \"net.one97.paytm\", \"com.sbi.upi\", \"com.upi.axispay\", \"com.samsung.android.spaymini\", \"com.samsung.android.spay\", \"com.snapwork.hdfc\", \"com.fss.pnbpsp\", \"com.icicibank.pockets\", \"com.bankofbaroda.upi\", \"com.freecharge.android\", \"com.fss.unbipsp\", \"com.axis.mobile\", \"com.mycompany.kvb\", \"com.fss.vijayapsp\", \"com.dena.upi.gui\", \"com.fss.jnkpsp\", \"com.olive.kotak.upi\", \"com.enstage.wibmo.hdfc\", \"com.bsb.hike\", \"com.fss.idfcpsp\", \"com.abipbl.upi\", \"com.microsoft.mobile.polymer\", \"com.finopaytech.bpayfino\", \"com.mgs.obcbank\", \"com.upi.federalbank.org.lotza\", \"com.mgs.induspsp\", \"ai.wizely.android\", \"com.olive.dcb.upi\", \"com.mgs.yesmerchantnative.prod\", \"com.example.demo\", \"in.amazon.mShop.android.shopping\", \"com.mipay.wallet.in\", \"in.bajajfinservmarkets.app\"]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4281, "length": 23, "value": "\"upiAppsPreferredOrder\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Array", "offset": 4306, "length": 226, "value": "[\"in.bajajfinservmarkets.app\", \"com.google.android.apps.nbu.paisa.user\", \"in.org.npci.upiapp\", \"com.phonepe.app\", \"net.one97.paytm\"]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4552, "length": 14, "value": "\"static_rules\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4582, "length": 18, "value": "\"identify_network\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4620, "length": 6, "value": "\"visa\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4628, "length": 4, "value": "\"^4\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4650, "length": 12, "value": "\"mastercard\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4664, "length": 18, "value": "\"^(5[1-5]|2[2-7])\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4700, "length": 11, "value": "\"maestro16\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4713, "length": 38, "value": "\"^50(81(25|26|59|92)|8227)|4(437|681)\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4769, "length": 6, "value": "\"amex\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4777, "length": 8, "value": "\"^3[47]\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4803, "length": 7, "value": "\"rupay\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4812, "length": 131, "value": "\"^(508[5-9]|60(80(0|)[^0]|8[1-4]|8500|698[5-9]|699|7[^9]|79[0-7]|798[0-4])|65(2(1[5-9]|[2-9])|30|31[0-4])|817[2-9]|81[89]|820[01])\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4961, "length": 10, "value": "\"discover\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 4973, "length": 19, "value": "\"^(65[1,3-9]|6011)\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5010, "length": 9, "value": "\"maestro\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5021, "length": 21, "value": "\"^(6|5(0|[6-9])).{5}\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5060, "length": 8, "value": "\"diners\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5070, "length": 10, "value": "\"^3[0689]\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5098, "length": 5, "value": "\"jcb\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5105, "length": 5, "value": "\"^35\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5138, "length": 21, "value": "\"network_card_length\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5179, "length": 5, "value": "\"jcb\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5186, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5206, "length": 10, "value": "\"discover\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5218, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5238, "length": 6, "value": "\"visa\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5246, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5266, "length": 12, "value": "\"mastercard\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5280, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5300, "length": 6, "value": "\"amex\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5308, "length": 2, "value": "15"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5328, "length": 8, "value": "\"diners\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5338, "length": 2, "value": "14"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5358, "length": 9, "value": "\"maestro\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5369, "length": 2, "value": "19"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5389, "length": 11, "value": "\"maestro16\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5402, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5422, "length": 9, "value": "\"default\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5433, "length": 2, "value": "19"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5453, "length": 7, "value": "\"rupay\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "IntegerLiteral", "offset": 5462, "length": 2, "value": "16"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5492, "length": 7, "value": "\"logos\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5519, "length": 8, "value": "\"wallet\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Dictionary", "offset": 5529, "length": 126, "value": "[(\"base_url\", \"https://cdn.razorpay.com/wallet/\"), (\"extension\", \"png\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5673, "length": 11, "value": "\"wallet_sq\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Dictionary", "offset": 5686, "length": 129, "value": "[(\"base_url\", \"https://cdn.razorpay.com/wallet-sq/\"), (\"extension\", \"png\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5833, "length": 6, "value": "\"bank\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Dictionary", "offset": 5841, "length": 124, "value": "[(\"base_url\", \"https://cdn.razorpay.com/bank/\"), (\"extension\", \"gif\")]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "StringLiteral", "offset": 5999, "length": 5, "value": "\"ios\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CustomuiOtpelf/RemoteConfig.swift", "kind": "Dictionary", "offset": 6006, "length": 747, "value": "[(\"availableApps\", [[(\"uriScheme\", \"tez://upi/pay\"), (\"appName\", \"GooglePay\"), (\"shortcode\", \"google_pay\")], [(\"uriScheme\", \"phonepe://pay\"), (\"appName\", \"PhonePe\"), (\"shortcode\", \"phonepe\")], [(\"uriScheme\", \"paytmmp://upi/pay\"), (\"appName\", \"Paytm\"), (\"shortcode\", \"paytm\")], [(\"uriScheme\", \"credpay://checkout\"), (\"appName\", \"Cred\"), (\"shortcode\", \"cred\")]])]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/RazorpayCheckout.swift", "kind": "StringLiteral", "offset": 377, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 219, "length": 3, "value": "8.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 239, "length": 3, "value": "4.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 442, "length": 4, "value": "0.15"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 450, "length": 3, "value": "0.8"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 493, "length": 3, "value": "0.9"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 568, "length": 4, "value": "0.05"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 595, "length": 4, "value": "0.85"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 744, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1316, "length": 1, "value": "2"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1323, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1449, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 1451, "length": 5, "value": "255.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1465, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 1467, "length": 5, "value": "255.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1480, "length": 1, "value": "0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 1482, "length": 5, "value": "255.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 1496, "length": 3, "value": "0.6"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1546, "length": 3, "value": "-10"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1555, "length": 3, "value": "-10"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1607, "length": 2, "value": "20"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1659, "length": 2, "value": "20"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1724, "length": 4, "value": "true"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "IntegerLiteral", "offset": 1771, "length": 2, "value": "20"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2155, "length": 3, "value": "0.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2180, "length": 3, "value": "0.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2248, "length": 3, "value": "4.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2293, "length": 3, "value": "1.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2322, "length": 3, "value": "0.7"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2444, "length": 3, "value": "2.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2492, "length": 3, "value": "1.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2650, "length": 3, "value": "4.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2703, "length": 3, "value": "0.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Main/Toast.swift", "kind": "FloatLiteral", "offset": 2740, "length": 3, "value": "0.0"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/WebViewBridges.swift", "kind": "StringLiteral", "offset": 235, "length": 14, "value": "\"OTPElfBridge\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/Otpelf/WebViewBridges.swift", "kind": "StringLiteral", "offset": 271, "length": 15, "value": "\"StorageBridge\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/OpinionatedAlertVC.swift", "kind": "StringLiteral", "offset": 413, "length": 2, "value": "\"\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/CheckoutOtpelf/Classes/OpinionatedAlertVC.swift", "kind": "Array", "offset": 451, "length": 2, "value": "[]"}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/DerivedData/iphoneos/Build/Intermediates.noindex/RazorpayIOS.build/Release-iphoneos/CheckoutOtpelf.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 738, "length": 15, "value": "\"Razorpay_Logo\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/DerivedData/iphoneos/Build/Intermediates.noindex/RazorpayIOS.build/Release-iphoneos/CheckoutOtpelf.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 882, "length": 12, "value": "\"check_mark\""}, {"filePath": "/Users/<USER>/Projects/razorpay-ios/RazorpayIOS/DerivedData/iphoneos/Build/Intermediates.noindex/RazorpayIOS.build/Release-iphoneos/CheckoutOtpelf.build/DerivedSources/GeneratedAssetSymbols.swift", "kind": "StringLiteral", "offset": 1018, "length": 9, "value": "\"warning\""}]}