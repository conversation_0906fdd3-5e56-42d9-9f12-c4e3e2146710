ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/Google-Maps-iOS-Utils" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_email_sender" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "${PODS_CONFIGURATION_BUILD_DIR}/fluttertoast" "${PODS_CONFIGURATION_BUILD_DIR}/geocoding_ios" "${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple" "${PODS_CONFIGURATION_BUILD_DIR}/google_maps_flutter_ios" "${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "${PODS_CONFIGURATION_BUILD_DIR}/razorpay_flutter" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/smart_auth" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios" "${PODS_ROOT}/GoogleMaps/Base/Frameworks" "${PODS_ROOT}/GoogleMaps/Maps/Frameworks" "${PODS_ROOT}/razorpay-pod/Pod" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMaps/Base" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMaps/Maps" "${PODS_XCFRAMEWORKS_BUILD_DIR}/razorpay-pod"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_email_sender/flutter_email_sender.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage/flutter_secure_storage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/fluttertoast/fluttertoast.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/geocoding_ios/geocoding_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple/geolocator_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios/image_picker_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple/permission_handler_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/razorpay_flutter/razorpay_flutter.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/smart_auth/smart_auth.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios/url_launcher_ios.framework/Headers" "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/GoogleMaps"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift $(inherited) $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift
OTHER_LDFLAGS = $(inherited) -ObjC -l"c++" -l"z" -framework "Accelerate" -framework "Contacts" -framework "CoreData" -framework "CoreGraphics" -framework "CoreImage" -framework "CoreLocation" -framework "CoreTelephony" -framework "CoreText" -framework "GLKit" -framework "GoogleMaps" -framework "GoogleMapsBase" -framework "GoogleMapsCore" -framework "GoogleMapsUtils" -framework "ImageIO" -framework "Metal" -framework "OpenGLES" -framework "QuartzCore" -framework "Razorpay" -framework "SystemConfiguration" -framework "UIKit" -framework "flutter_email_sender" -framework "flutter_secure_storage" -framework "fluttertoast" -framework "geocoding_ios" -framework "geolocator_apple" -framework "google_maps_flutter_ios" -framework "image_picker_ios" -framework "path_provider_foundation" -framework "permission_handler_apple" -framework "razorpay_flutter" -framework "shared_preferences_foundation" -framework "smart_auth" -framework "url_launcher_ios"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/Flutter" "-F${PODS_CONFIGURATION_BUILD_DIR}/Google-Maps-iOS-Utils" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleMaps" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_email_sender" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "-F${PODS_CONFIGURATION_BUILD_DIR}/fluttertoast" "-F${PODS_CONFIGURATION_BUILD_DIR}/geocoding_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple" "-F${PODS_CONFIGURATION_BUILD_DIR}/google_maps_flutter_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "-F${PODS_CONFIGURATION_BUILD_DIR}/razorpay-pod" "-F${PODS_CONFIGURATION_BUILD_DIR}/razorpay_flutter" "-F${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/smart_auth" "-F${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
