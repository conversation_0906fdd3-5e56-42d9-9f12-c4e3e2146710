class ApiConfig {
  // Development configuration
  static const String _devBaseUrl = 'http://192.0.0.2:6111';

  // Production configuration - Update this with your production API URL
  static const String _prodBaseUrl = 'https://your-production-api.com';

  // Current environment - Change this for production
  static const bool _isProduction = false;

  // Get the appropriate base URL based on environment
  static String get baseUrl => _isProduction ? _prodBaseUrl : _devBaseUrl;

  // API endpoints
  static const String registerEndpoint = '/register';
  static const String verifyOtpEndpoint = '/otp-verification';
  static const String loginEndpoint = '/login';
  static const String logoutEndpoint = '/logout';
  static const String profileEndpoint = '/me';
  static const String updateProfileEndpoint = '/profile';
  static const String forgotPasswordEndpoint = '/password/forgot';
  static const String resetPasswordEndpoint = '/password/reset';
  static const String testEndpoint = '/api/test';

  // Vendor endpoints
  static const String vendorsEndpoint = '/api/vendors';
  static const String vendorByIdEndpoint = '/api/vendors'; // + /{id}
  static const String vendorBySlugEndpoint = '/api/vendors/slug'; // + /{slug}
  static const String searchVendorsEndpoint = '/api/vendors/search';

  // Menu item endpoints
  static const String menuItemsEndpoint = '/api/menu-items';
  static const String menuItemByIdEndpoint = '/api/menu-items'; // + /{id}
  static const String menuItemBySlugEndpoint =
      '/api/menu-items/slug'; // + /{slug}
  static const String menuItemsByVendorEndpoint =
      '/api/menu-items/vendor'; // + /{vendorId}
  static const String menuItemsByCategoryEndpoint =
      '/api/menu-items/category'; // + /{category}
  static const String searchMenuItemsEndpoint = '/api/menu-items/search';

  // Order endpoints
  static const String ordersEndpoint = '/api/orders';
  static const String orderByIdEndpoint = '/api/orders'; // + /{id}
  static const String orderTimerEndpoint = '/api/orders'; // + /{id}/timer
  static const String orderStatusEndpoint = '/api/orders'; // + /{id}/status

  // Review endpoints
  static const String reviewsEndpoint = '/api/reviews';
  static const String userReviewsEndpoint = '/api/reviews/user/my-reviews';
  static const String reviewsByTargetEndpoint =
      '/api/reviews'; // + /{targetType}/{targetId}
  static const String orderReviewStatusEndpoint =
      '/api/reviews/order'; // + /{orderId}/status

  // Favorites endpoints
  static const String favoritesEndpoint = '/api/favorites';
  static const String userFavoritesEndpoint = '/api/favorites/user';
  static const String favoriteVendorsEndpoint = '/api/favorites/vendors';
  static const String favoriteMenuItemsEndpoint = '/api/favorites/menu-items';
  static const String addFavoriteEndpoint = '/api/favorites';
  static const String removeFavoriteEndpoint =
      '/api/favorites'; // + /{favoriteId}
  static const String toggleFavoriteEndpoint = '/api/favorites/toggle';
  static const String checkFavoriteEndpoint =
      '/api/favorites/check'; // + /{itemType}/{itemId}

  // Request timeouts
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 10);

  // Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Environment info
  static bool get isProduction => _isProduction;
  static bool get isDevelopment => !_isProduction;

  // Debug logging
  static bool get enableApiLogging => isDevelopment;
}
