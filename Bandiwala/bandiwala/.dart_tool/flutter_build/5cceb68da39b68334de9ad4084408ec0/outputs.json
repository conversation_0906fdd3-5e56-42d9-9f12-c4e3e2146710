["/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/Flutter.framework/Flutter", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/ios/Flutter/ephemeral/flutter_lldbinit", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/App", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/Info.plist", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/veg_icon.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/Pani.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/chicken_burger.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/bandiwala_logo.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/non_veg_icon.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/chicken_manchurian.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/pavbhaji.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/masala_dosa.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/welcome_image.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/uttapam.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/idli.webp", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/gulab_jamun.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/google_logo.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/masala_dosa.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/veg_fried_rice.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/masala_chai.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/Idli.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/french_fries.webp", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/paniPuri.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/HyderabadiBiryani.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/Samosa.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/samosa.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.js", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.css", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/NOTICES.Z", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Debug-iphoneos/App.framework/flutter_assets/NativeAssetsManifest.json"]