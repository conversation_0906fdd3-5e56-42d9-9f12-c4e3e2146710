{"version": 2, "files": [{"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/App", "hash": "5ee137ec11084166e604ca423fc6dd15"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7e827f3c407d93dfa01d1c8cac14af80"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/real_time_location_service.dart", "hash": "0e998f367f8ada7717f0e2cda6fc6fcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/devtool.dart", "hash": "2d7d80b5c908559a133f8729b6e755c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "e3127548d819af5ec9ecb10b5732b28e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtableitempattern.dart", "hash": "983a75a70218eda5b2e68e97f3293f5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winscard.g.dart", "hash": "58538b3b7601a27a2d6c24c2d74161b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdevice.dart", "hash": "19cbf88ec406061bbb2e9260b18510a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "10f2d960c7d6250bbc47fdf5c6875480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/type_conversion.dart", "hash": "032c93433e86ca78b8bb93e654c620e8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "01aec7b419ee4a50145b3ccdd2a85fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "f77f6a903d346f842a7fe474e427d6a9"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/animated_quantity_indicator.dart", "hash": "0ec04bcb2e7f63885ed13ca8bcd0f9e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader3.dart", "hash": "e97932f0cef53e2c018203ac3cf1c7e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensormanager.dart", "hash": "d1ed08ede0f2e8ca979d575b3b6b8c64"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/order_notification_modal.dart", "hash": "b1450c7ace2f22bd8d428ed59684bce6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/widgets/_pin_item.dart", "hash": "d138b0e6922686b71fe46cdd9793e46f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants_nodoc.dart", "hash": "161940e07ff4c31a1235e5ff9b16108e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/order_provider.dart", "hash": "1c50b32b2a0c60888e4bbb7dea85963d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/pubspec.yaml", "hash": "44bbdd5b86f78ea95ebf987456f189f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/geocoding_platform_interface.dart", "hash": "1fdf07321ae164dc5820e81264e967cf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitem2.dart", "hash": "370b2d8885d48ae8d0316c6f0bb0ce05"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/Flutter.framework/Flutter", "hash": "20dde71b55d1d90ec047c6d85ab8d6a0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader.dart", "hash": "54fa8ef3118882b34ee76276accb2c10"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/favorite_button.dart", "hash": "1d4ac597cb94a9605d39fe03a8bb1b46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "hash": "33f4d3cc878470ce68dbb3087c0cf584"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_denied_exception.dart", "hash": "c4c40bc2b2ff494b428e2d6ab0ed1fc6"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/chicken_manchurian.jpeg", "hash": "cc0da1e5ccce5844df7c2a772d4f104c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/flutter_secure_storage_windows.dart", "hash": "141745c6e29022622def8ba527cfd60c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/winrt_helpers.dart", "hash": "8a032ca2b66b8be21ce8368f80406db7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/marker.dart", "hash": "6653472447ae5e13d9c9af3bbb20c0ce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationexpandcollapsepattern.dart", "hash": "109f9e8f02a0c411aead05d93b5258f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtransformpattern.dart", "hash": "d2659325876491c5e79bf348a57ccb66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/location_settings.dart", "hash": "6a71940bcc46e93aee4bc1ca944037fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding-2.2.2/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "2936420e0c8ddba21d283d969f5147d6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/netapi32.g.dart", "hash": "5f080ef031fd5b2c581f580bbdefd983"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/errors.dart", "hash": "8b0b489cb15690ca7aa27a82947d2270"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatadispenserex.dart", "hash": "9817557f3ca542a625629bbc0bb2c6b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtreewalker.dart", "hash": "6ff721f374b943a1562c8e05dbcf814f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/windows_options.dart", "hash": "b4355b7f9f9e50017ce52a8bda654dd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/manager.dart", "hash": "f314fc69edd5d7d323464c9aa7c01f9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfilesenumerator.dart", "hash": "9834ef8e867f05559620160927fc6964"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/darty.dart", "hash": "46cf771248c5422e7c6d9c3b3942105c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "eea9d5a977d3ff4f46bb63a0f140c738"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ierrorinfo.dart", "hash": "30b1813d0f71eea6cae262f843f196a5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/bandiwala_logo.jpeg", "hash": "072d84045a1120a05803a2ad344547b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/emailjs-1.3.0/LICENSE", "hash": "a89ce44cac0f67025e44d139a32425c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement7.dart", "hash": "7b704b062a8ddbec0176222cf280563c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/url_launcher.dart", "hash": "10bbfa83fe7c3c8f8a4964a3e96e5b58"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/feedback_provider.dart", "hash": "cb9fbc1e4687ba7cb5e6c2da91ebc8de"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/vendor_service.dart", "hash": "649f822e5cf5c165997ed759d9ce184b"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/ios/Flutter/AppFrameworkInfo.plist", "hash": "5eb1ee18836d512da62e476379865f8d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/more_screen.dart", "hash": "4d221500e12c4247204335f6c146f020"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/Idli.jpg", "hash": "dd810aa0d0d6efeaacf0404c607b78d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/propsys.g.dart", "hash": "f21f9f81d251e1481096a9638691d07e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/delivery_partner.dart", "hash": "f3b501a759b9a46f5c393dff10ff513c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib/image_picker.dart", "hash": "0fa6597e197515cef31263aa53dedcf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "9c051d9a4098051ba8258eae9aae3195"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/lib/src/sms_code_result.dart", "hash": "262e99127e05568464307f4584c92ee1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/emailjs-1.3.0/lib/src/models/emailjs_response_status.dart", "hash": "9e61eee1c3fdad6bb3d22233095070da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/emailjs-1.3.0/lib/emailjs.dart", "hash": "92368a4c7d1b61cac1d681b345e4aa7c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/favorite_orders_screen.dart", "hash": "629fb4908a83fe1a7b8cce932c2d54d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumwbemclassobject.dart", "hash": "1480d297c2637befd67fbf59b4840a1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.1/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages.g.dart", "hash": "d8a6ceefc2ed13b75c503d01c8911fd6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/src/messages.g.dart", "hash": "bee9a89328e73d06f9b915e157deffe1"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/google_logo.png", "hash": "75700646b68987bb6cc3097e84c95963"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "cbeab9c259374c922b24d3cbd1cb6aa4"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_request_in_progress_exception.dart", "hash": "679db8fe68683e030815afa856663565"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/uxtheme.g.dart", "hash": "c1b28c1b4b0d1008a417c0f6800696b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationmultipleviewpattern.dart", "hash": "36e887f22fdae55fb0ea02599cd189f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/image_picker_linux.dart", "hash": "1936d57a483f9894c5b5c3087b446809"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "d3b40ca9660164ac83b714d6e2df3843"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/src/messages.g.dart", "hash": "95bd0247422d589a2b39cd985a000749"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "f5dab330de9938d8ad99263892810f3d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "20b03effe92fdb82cb2b1efcf637be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/url_launcher_string.dart", "hash": "ec94194f35d48443f468a3b06ef69845"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumnetworkconnections.dart", "hash": "e34d7dd943080889020806fbd56e32d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/engine/transport/http_client_adapter.dart", "hash": "6dd3ef870db8b75842ff412517a46912"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "f60846aa76dab98607aa06c9bd6cf1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemimagefactory.dart", "hash": "d04edc39b6d3477197606ec9c969e738"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/lib/src/event.dart", "hash": "5030c407565889225a0f5dc11c8e0b8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/inline.dart", "hash": "5b3e856525ee8046183fed46364f86e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellcheckerfactory.dart", "hash": "502de1243ccffce33e394a25f669b96b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "hash": "aef722a64f462b84d30dad6278040fb4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "138038335aa2c209f231b2694d5aae3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/url_launcher_android.dart", "hash": "42d0000dd58d923eb70183595232c299"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/global_bottom_nav.dart", "hash": "3b877131e9b37278364c2d61b3c5934c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/lib/razorpay_flutter.dart", "hash": "aeffb7688aa4e59e087aeb9831728718"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient3.dart", "hash": "18b7345ba375180fbdb4bd38938f764d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/ios/Runner/Info.plist", "hash": "d238155084ee97fd15ba722d1a5b4089"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/Authentication/logout.dart", "hash": "0def80c4385b42fc89ef6c102c34645c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "abbe93b36782df11e43e348dadf52e94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/LICENSE", "hash": "1112ce02e778f3cdd6243071b4dd304c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworklistmanager.dart", "hash": "853dcca1f602ac69e366e5d6afcba80e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/delivery_navigation_service.dart", "hash": "9dc20f15c545ce63b59be2ed689a0d4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemresources.dart", "hash": "3546603acafc7773ff75c32041c86fce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement.dart", "hash": "d1f3ee169c9fd7a9dec5eb03a4a33525"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/regexp.dart", "hash": "10ca1bc893fd799f18a91afb7640ec26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxpackagereader.dart", "hash": "59137da0b55aefe8a4074891792a55b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/masala_dosa.jpg", "hash": "8875dd608db5d341eb0d9865577929c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/method_channel/serialization.dart", "hash": "230370950ca07a138928c73df4c0ad52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/bluetoothapis.g.dart", "hash": "dee1906acf6e5639024770351149df59"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/tile_overlay_updates.dart", "hash": "6323a0368f25b450b38dd30b55a884b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestapplicationsenumerator.dart", "hash": "c4029d239f6cfaf6d919757bbd59b403"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0/LICENSE", "hash": "01e92af78cd38fbf9e3c37dbcaba15e2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "d9eb28b2265932eb628ad0c3a123bee7"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/pavbhaji.jpg", "hash": "eae423ef9314d7f7acb0737b2d64c9b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumidlist.dart", "hash": "d404d0c85a223afe9402bc0c33f60886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/geolocator_apple.dart", "hash": "0190cf8d95873b9bcfdf00c1580334e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/heatmap.dart", "hash": "5a315ac9d2a60b5e5dd4487decf1d5cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages_async.g.dart", "hash": "2bd174cad1b04e4cca9ba7ac37905e5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "38df6f8cafb853c1acf0f6e6a4b4950c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_string_array.dart", "hash": "dce5e400c1f0958583196f9db05de7b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/tile_overlay.dart", "hash": "e8d02355e5669eaf544832dc9807a50a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/ground_overlay_updates.dart", "hash": "646cf56f9f2a0995871d49133dab7efb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "ce666dc6b4d730d3cb07e6bfc64a8825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/support_screen.dart", "hash": "8c34c4152d2b6755eed7b7f4c3b0ea4e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "952fb243dbdb00bfe11b0293238b115d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/date_format.dart", "hash": "e0e1b6343cb4c49cf84e50e7bac742c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/widgets/delivery_current_order_widget.dart", "hash": "cdb2383d1fac229f23bdbacfb93b00a4"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/manage_products_screen.dart", "hash": "63707212de737a4d55e2956efc1e800a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "7217dd37b49bab8e0319d4fb26d14d8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataassemblyimport.dart", "hash": "2c79ebc319fae192d118d1483a0a0b07"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/Idli.jpg", "hash": "dd810aa0d0d6efeaacf0404c607b78d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/errors/no_result_found_exception.dart", "hash": "82401cd2748e687908e7b693aa5811ca"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/backend_cart_provider.dart", "hash": "b940cade06491613261bdfa8c0371764"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/legacy_api.dart", "hash": "e62a8f39ad332b5e313b0be97f2d280f"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/admin/vendors_management_screen.dart", "hash": "cfe30ae42469867f7e294592080d274d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "hash": "c23f3b290b75c80a3b2be36e880f5f2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/tap.dart", "hash": "2174cee3aa85b6a1cb77f1e9f1f54f7b"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/your_orders_screen.dart", "hash": "ee1c6901dfbbb7b5f722f1001105b740"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/role_navigation_service.dart", "hash": "1f996e4247d7b78ee484c91b5ad8d032"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/assets/toastify.css", "hash": "c4da0fc89c24df5cc6a180d9ac851706"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/date_symbols.dart", "hash": "4c94c1ae460dd53255786f0ce3b53463"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/number_parser.dart", "hash": "6e3f753d2e2717b4f36ffa7c34b58dce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "hash": "0350d89163d2f0ced1ab36a631c63fdf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "2458910beb2b4f3b177a7db027cf7d34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework.dSYM/Contents/Resources/DWARF/App", "hash": "8a8cc661a3e1e9c780714d9b71478821"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation5.dart", "hash": "d879c3156e19f2b290c4d6eed1de5e89"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/engine/transport/io_websocket_transport.dart", "hash": "8174dbe4c78a9a7531c6bc4693052300"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader6.dart", "hash": "33186ffed4f0249b40a7d6161b7c2351"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/new_cart.dart", "hash": "d381dc9604e0ad6426035515bd44201c"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.css", "hash": "c4da0fc89c24df5cc6a180d9ac851706"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement9.dart", "hash": "5c637f86e1850037099c738512c748c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienummoniker.dart", "hash": "43758553461b83b7bf6696d099d81613"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.2/lib/google_maps_flutter.dart", "hash": "5f5958e293670abf0cb769a8d9620ab9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/src/messages.g.dart", "hash": "5f926ede3679035dbc9b078b96518878"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "hash": "e64d63aabc0975a7e9fdb384598c2f8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationspreadsheetitempattern.dart", "hash": "7c279a8dd6f509d8f7a9c5a29ea35852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/combase.dart", "hash": "90ed8a12c97e362a162da690203df055"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/HyderabadiBiryani.jpg", "hash": "bc0be78fa59a8d6a9fe687118ba624f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation.dart", "hash": "a4282bea82459c80d5a209b34f6b6086"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/edit_profile_screen.dart", "hash": "023b5220c6575f386dbdeeb8e33da526"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/idispatch.dart", "hash": "872b28cd09e937b0b3089f6c72cf310d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/reassemble_handler.dart", "hash": "17dd5087a9b407563f662fc112624260"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "8b65a0312de1594ea0989e8ce1d4b257"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/tile.dart", "hash": "adcbdd4bbb3f5b1d1fbb842f6d761299"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "a4c1dffb16d559eb4d22bac89777780e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "hash": "5e93843d2c9e385658de36a256646d3c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/platform_interface/image_picker_platform.dart", "hash": "38982dc702bc4583fd29314508a32c17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/error_helpers.dart", "hash": "73c0a59e2d19aea71c6029f871aa9f67"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/number_format_parser.dart", "hash": "61a0deef2a4f0ebaed506bb2a22c5185"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/map_address_picker_screen.dart", "hash": "cf740aac2563ae68bc93009ad41bca13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_device.dart", "hash": "5de9b4234c869bfb7f58138e26207e64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/number_format.dart", "hash": "c4a5c35dbaaf6daebc97eac0f89b1f68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "hash": "01077b1240404868a99cdfb2c892cdff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/stream_transform.dart", "hash": "2f811178fd6401d1399cf8b09cc1f9f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellingerror.dart", "hash": "6338f981e88891ddf1b5674bc7baac0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/events/map_event.dart", "hash": "c9ab535a7eafb8e75a6d6f78508fc7df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "f9963c0de15655f08d11298175dd45fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winmm.g.dart", "hash": "f81746c322f4a5269713665a1c9ab0fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.65.0/LICENSE", "hash": "b3896c42c38a76b4ed9d478ca19593e4"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/AssetManifest.bin", "hash": "3dd55e9ba27cf2bffc162c427170a82b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/reviews_screen.dart", "hash": "f8df10740f61ba4478f8c2cd84bc54cc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/profile_screen.dart", "hash": "b3585272f9d317fa653a9461cd820595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/ios_options.dart", "hash": "704d7f872888ec6e9697123a180fd95d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatatables2.dart", "hash": "f1f175eff474684786b1b6980f386aca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/cart_provider.dart", "hash": "3141fd6a57757250968a8a9a06ba823e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material.dart", "hash": "76611c76bf37be8fc89798858b6c7685"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iagileobject.dart", "hash": "4bc403cec1c5846051bca88edb712a8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/circle_updates.dart", "hash": "5cb1dc85dbeedb5da38de7a95b38e74a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "377731ed35ad8d1d36dcfd532a3d308e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/kernel32.g.dart", "hash": "f17de3d9e71117cf29f360cbfaab5348"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "eca5aa939aa9722ead4b6c347fb4d11a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/order_service.dart", "hash": "e5f53b6be81d16bfa2ca52e5b32073df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/pinput.dart", "hash": "18f1eff8ef9ca65edf684e20adb4bc4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/proxy_provider.dart", "hash": "57b51f6f00c6bc3a29abbf83fbd804f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_source.dart", "hash": "da5faa2d91b7029347d1a39bc0060cb2"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/google_logo.png", "hash": "75700646b68987bb6cc3097e84c95963"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "fdf500742b45dff0abb3db9cbd350fd4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "aff0bd5981a82f881b4ac72a321ee9c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quad.dart", "hash": "9a043d96e7ae40786de66219219bea4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/logging.dart", "hash": "60fd6d17602ae0c1d18e791d6b1b79cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iprovideclassinfo.dart", "hash": "874923f8d9b00546393faf2d37e102ef"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/menu_item_service.dart", "hash": "ac82ed4ff7959584b2aafde2dc2c35f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuri.dart", "hash": "3dd3dfc2d8c37cb47724659f8d2098e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb3.dart", "hash": "4d9f681599b9aba645421097eda46139"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iconnectionpoint.dart", "hash": "81f755ba6336a4b4cfc0e376b4f8244d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/feedback_model.dart", "hash": "40fb1f9d887fb6d3f75673f66cdb7ab1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/comdlg32.g.dart", "hash": "5f5e77b562118490a9610f89d30b3990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/types.dart", "hash": "3098c320bfb4fb78df8b7b16aee58eef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "6cf1ca324535366e2ea214049ffc9918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "6b396237a38f3417babe500724de8a84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "a594e2e46c047f44912e93f2e38f4a47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdeviceenumerator.dart", "hash": "40e923ff78f2c9cba09cb0652fd34e26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactoryentry.dart", "hash": "52312cfbaf36f7c5af3e9dd495a2d33a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/favorites_provider.dart", "hash": "bccb9029e5d8be76688181c390df2667"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/web_gesture_handling.dart", "hash": "9c5414cf3e41339780448ad6093672e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrangearray.dart", "hash": "91c64b5db3c0685967d4a0ce319c5b59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/variant.dart", "hash": "ea1d5b4167d55eb79626b0ad1bfe6a58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imodalwindow.dart", "hash": "69cf74c98799489b81531323ecc6cc74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/filetime.dart", "hash": "3c1a4805a8645f3bdc5369321eddb32c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/tile_overlay.dart", "hash": "ad78654699d9a9692b5dc86353148343"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/async_expand.dart", "hash": "edf98e44de04feefa196e98d41cb7813"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumstring.dart", "hash": "3f9dcf46067e3b9d3f41240d43c1c59c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/heatmap.dart", "hash": "9283ec6b773c6a71ff375deeea0f0324"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "f12e483651d7be5d2c469562f6bb2cf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_service.dart", "hash": "da632f4b0e209fd38e988f5c951a424e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/veg_icon.png", "hash": "a59fb852c65f22dd10cb7583d219e1f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/flutter_secure_storage.dart", "hash": "5a944801c9b2bd3447f982168b31e46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellchecker2.dart", "hash": "28ceeb83a01abb867ca28f6c8b6e3e97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ibindctx.dart", "hash": "48b80cd162c0a5db716c6df6af6d33a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/widgets/delivery_stats_widget.dart", "hash": "ba13144fca22c35b20b36831958c88c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "4fa52a6cb3ac24b95e99a20d034f43c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/ole32.g.dart", "hash": "ae656f0ed8c596c679f657c63db1697e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfile.dart", "hash": "bd68bf024d2e14c1e7fb775612927625"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_permission.dart", "hash": "2c1328c414252b20b52d7e1c8505d81d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "6189af9ddf633811ffb6414cb9d3f744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/item_details.dart", "hash": "601acbb15ea67572efefb8f08be664a0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "3ab9652d1101aac3b5d74a4495d860ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/cluster.dart", "hash": "361a6978a64d43044754857130422be4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/propertykey.dart", "hash": "9e8d52d54397c70b7c20ef9436ca38c9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "224c14ef0447e287cbae1b7aed416290"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "6dbd6092d46d1cfb37491463002e960e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/functions.dart", "hash": "e0123a0c77b03d27b3e2e899764c8d7a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9c9f1e70fac06b3e87bb33ece047c4cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiostreamvolume.dart", "hash": "9c9f9bcb5f1fed59a9b35912bbd2cf56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/polygon.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/pinput.dart", "hash": "c44f7e5768d2430b02a1d918f81810d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/maps_object.dart", "hash": "f572011c4cc65498fc878731ddb338b2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/invalid_permission_exception.dart", "hash": "7837827426418dcd8970e0032a918ccf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4a2215ab704d09e97121c1bb71942b3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfactory.dart", "hash": "93d835e43f33ca5ed96e6e85a392c1e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/intersection_result.dart", "hash": "832666b4f69945b957b6399ec677085b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/inherited_provider.dart", "hash": "dd618a65e1f3400d8224fedb42a1881b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iinitializewithwindow.dart", "hash": "0748bf03bcf37edd1d571959e45a5cc0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/quaternion.dart", "hash": "82a52b42ca10c86b0f48afea0cbe9ac7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/gdi32.g.dart", "hash": "dacaaea99d06403132897f545645cd7b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechvoice.dart", "hash": "de4097d4b754d587b6c15dec5805d0e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/parser/binary.dart", "hash": "ea9e8272e3992ca679ba31d78814bd66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange3.dart", "hash": "4f4a2d291e23c96c7ae0d4dbc9598c54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/engine/parser/encodePacket.dart", "hash": "ae999f27aecf1149c0a13127e3f85592"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersistfile.dart", "hash": "90f6ddbbfda2bb5dd2ab2e05c777def0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/feedback_service.dart", "hash": "e5fe0c46f4f707cc3ffa7debea09a88c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "eaed941ddb98b44c090d06e0be0a7562"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/item_details_slug.dart", "hash": "2d6e89e4abf5eebb70eb2b675df47eb5"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/email_service.dart", "hash": "ff736c00d396d479ca634356c1b5ecf8"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/address_selection_widget.dart", "hash": "39f23e0b0db1776fd9e00437a1829858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/_internal.dart", "hash": "ef4618b5bf737a7625f62d841127c69d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/welcomeScreen.dart", "hash": "a0d49684cf6e9bddf92b569173488667"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "10cf10518abe4a916f2cb9ed7c4b635f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/models.dart", "hash": "8a3608c32ef31373460e707ad220237a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "f357bc5433a3205fc48000ad8c569c5b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9b98b196040f00fd2fbaf5f7a2309e6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtablepattern.dart", "hash": "acfd72d6ba5e1799364d54d9b5871d9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellcheckerchangedeventhandler.dart", "hash": "79ad9ffc538ede29c2cc688c386d717b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix2.dart", "hash": "ac51c125ed5881de5309794becbacc8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiorenderclient.dart", "hash": "d32d5d6e1a22dea95ed2e78d62d2aff9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/utilities.dart", "hash": "db8fd891fdcab94313f26c82f3ff2476"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionenumerator.dart", "hash": "379b3eed65ef945d342de0a84831c177"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "990244fbee5d6f551e98a4bcce092389"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "61cf3ac380d43d042f8d9b7e7f6a11e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "561522058c0ec0f631fe295300d190e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworkconnection.dart", "hash": "cd2e632e852f1e82cc2084c49e16500a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/change_notifier_provider.dart", "hash": "3ce0eeefa3058c1955fb1f435ce9928b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "ceafe3fee68e6597afe301af3cc318c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextpattern.dart", "hash": "b208f3c23955741947ead4a75ca0aa3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "hash": "43bf805d1c539a8158296b167f65064c"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/package_config_subset", "hash": "3bfa0d9d0a76f05d6f9bed06ee7d6e0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcustomnavigationpattern.dart", "hash": "84de591d644b29f5e21052bd9c84263c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/admin/orders_management_screen.dart", "hash": "9e69c8b7c44864260a558a0fc7b545b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "hash": "27a9e4cafdd931f481840ea15b053cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/delivery_area_service.dart", "hash": "8b81cccbb75a031101a242e276341236"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/payment_service.dart", "hash": "707c60abe4f693f7a43f2647025c32dc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/level.dart", "hash": "49f3213e86d2bafdd814ac4df3d114ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/file_selector_macos.dart", "hash": "20f3c0d39cbc5c2fdb223745edcecdec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumspellingerror.dart", "hash": "5f3836dd0624aa9d429b6a4d5b9d0321"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/types.dart", "hash": "3353f65796638e830b18ffdf1a678a3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_polyline_points-2.1.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/delivery_assignment_modal.dart", "hash": "7466086bbdfa3c1bd392486872a0f27d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "4349dd08c33e677b65d9e00f13c35d2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemconfigurerefresher.dart", "hash": "313def06f0e97cc30450b2ac2d1cdfdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/activity_missing_exception.dart", "hash": "79443d9def8c2f6b6acfc2816be9c6af"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader4.dart", "hash": "5a65f8839771af0fad5b2cf647703264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumnetworks.dart", "hash": "dabc834de271cc395f1aa1b0e9484fc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/image_picker_platform_interface.dart", "hash": "b152cc1792a66ac4574b7f54d8e2c374"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "2122bbdb5de249ae3f2444fe234a5afb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/implementations/method_channel_geolocator.dart", "hash": "f236f79ad83d0fb0b86b75561ef1d4d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationgriditempattern.dart", "hash": "89cf0f2546a2003c136706514de87702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/method_channel/method_channel_file_selector.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_delegate.dart", "hash": "35512e89f2b31322744090b018902bab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "28219fbae9045c4c3217c0f3fd6fa7ef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/permission_definitions_not_found_exception.dart", "hash": "37811c1d6ef37aade25e3c631bfa230e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/ground_overlay.dart", "hash": "7cab7c6d70a64443b1bd47b35eda836e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/take_until.dart", "hash": "6c873115296f0c3c72777a00c58437c1"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/create_review_screen.dart", "hash": "240d56dc885ccaa1a76a65707eb500e9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "0c520a6b1ab38e0f294c3ddbc2ec9737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "58490e33e6e99c4e4e313491a36cf23f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/Authentication/forgot_password.dart", "hash": "77699566a533477aa607c9ab872337db"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdragpattern.dart", "hash": "27ae743f7cf34ade61bc99eba4dedd81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "hash": "acb5f13d5c2cb9a6c302487169ead3dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "752b2b12f0829a4d0abb699adad87062"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "hash": "4958f9fde0e398a31f8adfff89caf4d2"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/cart_service.dart", "hash": "d5a80f12d543adc814f5bb8d58307126"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "c069ad8b31e18adb75c27530f218957a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/samosa.jpg", "hash": "07c85a82d83d61afd038ebde092c3655"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "33adcae8de663e2e8f8f410da7fc8023"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/admin/feedback_management_screen.dart", "hash": "2bbc249cf604b286dba9c3a1f2c01794"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/utils/pinput_utils_mixin.dart", "hash": "cb2ee0448db57b7ca9872bedd8b56675"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/web_options.dart", "hash": "7dff3a0a1b5652f08f2267907c79844e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/bandiwala_logo.jpeg", "hash": "072d84045a1120a05803a2ad344547b5"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/2999f8efc27b6cbc654ead82006b9a59/App.framework.dSYM/Contents/Resources/DWARF/App", "hash": "8a8cc661a3e1e9c780714d9b71478821"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "bf00ea3c58b6ee2b3f5422cfc3e3cd2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector4.dart", "hash": "7d33539b36e15268e2f05b15a9f5e887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_options.dart", "hash": "5f44f436ff7b1129b18a489faab45005"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "bce1bb799fa4cc899b6525721e14c9aa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/socket_io_client.dart", "hash": "1db935ceb45993fafaecaa7822382baf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/micro_money.dart", "hash": "391b7eda9bffdd4386292eae157d449c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclock.dart", "hash": "889b2a51969dc04727672d045f214d9e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/Screens/vendor_dashboard.dart", "hash": "6cf986af015069719f95eb20be8c4660"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/main.dart", "hash": "9309ed77306637aefd6e10dbc8f74f7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "84589f907e3e4d8fc72e5c786a0530f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/ntdll.g.dart", "hash": "b738d8d6cc15b84589aee0b3cabf344f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/callbacks.dart", "hash": "b020749262d0d602700cd21e6f41acdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/utils/pinput_constants.dart", "hash": "79a5154ec35eb971c2a34803998b6f2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/intl.dart", "hash": "9947d163dc33c2a6804841d23ff4b9e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumresources.dart", "hash": "01f72c7d1903117cf6423e4a5e5916eb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/maps_object_updates.dart", "hash": "66f9c57e1bbecf1c3d8b67a3a4d1fb71"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/rate_limit.dart", "hash": "8fed4025dd6b2e77558d840be04059f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/user_app_bar.dart", "hash": "bfa494b40a7fea9b4ddfd6c20a9c897d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/xinput1_4.g.dart", "hash": "aa553bed1d888de4b53878cd701f022f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestapplication.dart", "hash": "bc01545a1cca050f2067c0b6163a4755"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "81036c1ed2827ac1db9fee5a900f568d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/add_product_screen.dart", "hash": "f8702a723d4698a9f36537dfb2a1cc9a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "c816d604c95b060fbb4fa0831ad7523d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/profile_screen.dart", "hash": "88e5beca4f133e65e224c45a470c8c79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/string_stack.dart", "hash": "aa27dfc54687394062db977707839be5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/lib/src/credential.dart", "hash": "2d85ceacb4d531b439eda14cc01bb744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement5.dart", "hash": "c5f70755693110e17c5b6bf2e04d7a54"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwinhttprequest.dart", "hash": "abcb8cf9c1f0bd0abc0fbf82868470fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "a7ca596d88ce54ac52360d6988d7c9c8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/aggregate_sample.dart", "hash": "cd164203dbc14d5701b8940f084fd58c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dwmapi.g.dart", "hash": "07bef83d35c41f53d65fda935b414231"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/LICENSE", "hash": "6a18ff02d63121c12f07c3f2e1dceee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechobjecttokens.dart", "hash": "d2026a23132fde8bda02e43a141f020b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_value_indicator_shape.dart", "hash": "949350c1ca059ddb517d7f4f80b21ecd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/uttapam.jpg", "hash": "d79966c7ad589182e70dc1aa8b88aa37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/dispatcher.dart", "hash": "cf07a18bf186a6805d8c384420518376"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/number_symbols_data.dart", "hash": "607a9180d57d1885fbf3b44ac080f162"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/rometadata.g.dart", "hash": "cd81c79fc585d5ed8858c3c530ec90f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "91f73f40856927e688e1707a923db3e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispnotifysource.dart", "hash": "32170db149f7552573abb976a101f186"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/aabb2.dart", "hash": "8a05c4ee4d75a485389f2e5c2f6618e6"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/cart_badge.dart", "hash": "20fe6b32952c74c222d70fddad6e72ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "8678afc1455a658ddf2382ad887eec66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/obb3.dart", "hash": "f7fd689f4549dd97ac670c72e4d617c6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "865354d8941afe9359c093d59d7b282f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/nested.dart", "hash": "5c621d343831cbb9619557942e6b7d9f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/analytics_screen.dart", "hash": "a8c4a13068c9ba1e85e427918ce36370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "hash": "456c173e6b3ea24a522a57a8bcb3c0e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/engine/parseqs.dart", "hash": "0bb6d96237ff4ee22e1785539d7ebd48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/lib/src/serialization.dart", "hash": "758b51cbaf3686abb5bdac528fdae475"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "d6008bafffb5b2e7bf16e59a9d3ad934"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/number_symbols.dart", "hash": "6c1b7903629a7ad4cb985f0898953db1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackagedependency.dart", "hash": "2a81dde5f9a7f961c0f86199e56b5f88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdroptargetpattern.dart", "hash": "a54cd317ab692f04c4fc26e8cd9cfce5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/position_update_exception.dart", "hash": "c9d1e5ab90e2aff40b49980d1045cb31"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/lib/src/event_emitter.dart", "hash": "a572ca46ecfcd599aa868c105c460144"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dbghelp.g.dart", "hash": "740bd95d3579057bcb116d73ff96875d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/geolocator_platform_interface.dart", "hash": "34a0e92ce017d86c6feb973b6a30b64f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/geocoding_platform_interface.dart", "hash": "81079bb62160785044af3810b63a15b8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/src/google_map_inspector_ios.dart", "hash": "4cc5c06032e753889957080fa13b0647"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "8b20b418804c1d6e59afdfcae6e84728"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/image_picker_ios.dart", "hash": "75290287531fff47b4eab2f25f050d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatadispenser.dart", "hash": "3fc24d3b43ff4a6b63811978cfb697e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersistmemory.dart", "hash": "4beadd212378edebcbcbd4f68f9a183a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/admin_models.dart", "hash": "d70ce84c5b73201c275239abb3bb8a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellservice.dart", "hash": "bde779c0e81c5e48653c2dbc3d589cd3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemarray.dart", "hash": "1f105c42a107237821eb50094abf6b4c"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/uttapam.jpg", "hash": "d79966c7ad589182e70dc1aa8b88aa37"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "15ee790ce6b1c0a29d38af8094ad1722"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/idli.webp", "hash": "1490f761726ec9a4afc687a46354e944"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/src/google_maps_flutter_ios.dart", "hash": "9574e53409e80d75909c679e35b6c2be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispvoice.dart", "hash": "9a998f6a132477da93895bae423e3ac1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/LICENSE", "hash": "a27f596192906ba7cf9b6375cef4573a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding-2.2.2/lib/geocoding.dart", "hash": "1e110a1326ea2c97965df268fd2e0985"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/chicken_burger.jpg", "hash": "8089168f5763b0307314de2494de6e33"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "659b88645890c6437ea5ce4928e8871e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationitemcontainerpattern.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/admin/settings_screen.dart", "hash": "fc4484997baef10bc53f139f0eb2d6f9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/lib/src/types/foreground_settings.dart", "hash": "6dc114d430c80174a99383c29eb57d15"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "e88cac3fc4dc6a17d2bd13549d433704"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/screen_coordinate.dart", "hash": "f907f96f75316a11711b459bf6c7b747"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextpattern2.dart", "hash": "1dfa85bd16bf08ae91f9cceb02ef1563"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/user_repository.dart", "hash": "f8b4c55f3259a1f10eaccd764461ed0f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactory.dart", "hash": "5d461db74d04d7e270d13a5a8a340796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "35e99597a2bc1839b114f890463b5dad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/ui.dart", "hash": "bab602eb0688ea2e4ce1dee9902193f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/date_builder.dart", "hash": "2c721a938baa2ccf947cc49f3e6cd4e8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "d5363426c1acae1c7410b4096cefd94d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/enums.dart", "hash": "0126627ff99705319e77e37504e41d22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/date_format_field.dart", "hash": "a9e35bc352293fff8cee637c13d9bcc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/wtsapi32.g.dart", "hash": "d165617d9abc7ef296c4fc875471fae7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationnotcondition.dart", "hash": "1fec236f729d3217c13d42295fe3faf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/util/unknown_js_type.dart", "hash": "f2f12f2a3ebce4639d78084016b8f09b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/permission_handler_platform_interface.dart", "hash": "54e3fc58f0992b887be63771a3d82202"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/apple_options.dart", "hash": "d4efda9ec695d776e6e7e0c6e33b6a4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/frustum.dart", "hash": "218ecb2798a6fb1ec08cd5c993d98269"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/Samosa.jpg", "hash": "07c85a82d83d61afd038ebde092c3655"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/from_handlers.dart", "hash": "b631bb6ec102953c2b84347f00544869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/src/messages.g.dart", "hash": "f1c7d23cd6db9504510e67e2957b4aef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "f500fac00bc25f66e6f49f5ca6de723a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "130ada4ea6283eb536d5d8eb0786a631"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "77e3a9ed54e0497465a4346f273bcccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/common_callbacks.dart", "hash": "edeb3ef79020eb4835ccfc72c6b7a89a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iunknown.dart", "hash": "71bc337251bdc832a7be27b05297de39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation2.dart", "hash": "db81a2e27f649abaf9de88c1f92b2686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/url_launcher_ios.dart", "hash": "11803ff481a58d66000cbea8c68e2af4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/platform_interface/google_maps_flutter_platform.dart", "hash": "b503e86a5e7a9c1a8b909a72afd75238"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "hash": "8dac3815609f98dfefa968bc2ea4a408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "8f1d7bd8be5bc9a71d3131f835abdb80"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/samosa.jpg", "hash": "07c85a82d83d61afd038ebde092c3655"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/location.dart", "hash": "836295c2aebc4951c13afe0014530125"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/Info.plist", "hash": "5eb1ee18836d512da62e476379865f8d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/order_summary_screen.dart", "hash": "c78d4c90d2a4a419845f4a50ae6a8711"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2c582bec6fc77f68c975f84d2252ed8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/engine/transport/io_http_client_adapter.dart", "hash": "418d634b67e4c7863359f6e3997ee453"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "93576d7d8731bea65013886f9194df15"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5d99a505ddc69d5accc4e5a83f5cfa4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/heatmap_updates.dart", "hash": "ec27a08abecc719984d4d481b6b67db3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "hash": "1c52a06a48033bea782314ca692e09cd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_accuracy.dart", "hash": "6deecb644bc140e21eff85fa3487c41b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/models/models.dart", "hash": "9097e5377036b3a0c428d105c016509d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/Authentication/login.dart", "hash": "5b9776bf1bdbdb242a74fbed51a1a717"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/src/serialization.dart", "hash": "292651436f487b4c9b3352a39ab5247a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdevicecollection.dart", "hash": "7c246a3f46fba3b11765f3b103095947"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemlocator.dart", "hash": "8e0c72d73e1298cb4655ed8bfb6b9a37"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/src/messages.g.dart", "hash": "3ff09a7edec90fdf07e59bc3514ea474"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/LICENSE", "hash": "a27f596192906ba7cf9b6375cef4573a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/engine/transport/io_transports.dart", "hash": "54440d91dd764d9660e610b457727fb7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "47ccb32c843b4075a001e612853b2a31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/iphlpapi.g.dart", "hash": "603e877f67a065e3f92f62c54940b121"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/menu_item_provider.dart", "hash": "1425f431650cfb2011e4ce2cb3965782"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackageid.dart", "hash": "6216df76c2226292e1e6c05135e22217"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/list_to_blob.dart", "hash": "56d7144236503f311a7d9a966eaf2fbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement2.dart", "hash": "b84e7e0b89d2bad9ce9064ec3afc6119"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/masala_chai.jpg", "hash": "68ca61a818eb6e5f991e5a0f9d3dea00"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/log_record.dart", "hash": "703c5e391948c58228960d4941618099"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemclassobject.dart", "hash": "e1908e595346f6fa2b9cafaf3c4f4b15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/plural_rules.dart", "hash": "2241f880365723564463d0bec35a4ba2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "204fb623e2b782051e9bcb6e320e97c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.2/lib/src/google_map.dart", "hash": "7e5e4fabc3a667eba8cb58a49c752276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/shell32.g.dart", "hash": "c313566bafbe45ee136f94f682b189fb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.1/lib/geolocator.dart", "hash": "389ceea04159e321b90d1ef300afd431"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/extensions/integer_extensions.dart", "hash": "73ca94dbbbfdf54a4125b937afb164d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/itypeinfo.dart", "hash": "5823785db5bc1f46ea00828058f2a973"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "57d74766f36a3d72789bc7466ae44dba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "075310a7fe661b71e9a583aab7ed4869"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/search_results.dart", "hash": "ca071ce4392c82a9661fea122d8b25ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllinkdatalist.dart", "hash": "4e8aaa21ed0ade8f7601849694c81ce5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/exceptions.dart", "hash": "8bfe962047c432ca659d01097491e8b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionpattern2.dart", "hash": "2066a7e06691045adc327c174ab0854f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensor.dart", "hash": "e78db72a9d4ba4e067c0afb9ab7c4518"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/ray.dart", "hash": "5d9bdad87735a99fb4a503c5bee7c7fb"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/share_feedback_screen.dart", "hash": "3421c7636862fb7178a97f931c25dd05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix3.dart", "hash": "64b9fc5ffdc9f1ba801b6ccf099347b1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/on.dart", "hash": "72e3dab849e56dd4bf487065a0d92ef5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/win32.dart", "hash": "e3851989de381ed948b1c290a299a2c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/engine/parser/decodePacket.dart", "hash": "a11e7c22dce4c4535e8309b5ac318125"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "805f831d339e4ab9e6b172b2bf845809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "hash": "02f24ac14177b434c3bebf0a9a2980db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/url_launcher_macos.dart", "hash": "ff296a17d3582fcd8fe99bfb544a3978"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "6fc640633e357a75291efec1c68b02ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/settings_screen.dart", "hash": "f95fe364268cea3e4de8fd7bb182cb16"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/test/test_flutter_secure_storage_platform.dart", "hash": "362bf1b65ae84f1129622a8814a50aad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/earnings_screen.dart", "hash": "2acfa7498eec3b787bb14eb075b2ff34"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/gulab_jamun.jpg", "hash": "9d1a0f1879edee257468cab67d9a4bfb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/widgets/delivery_bottom_nav.dart", "hash": "b4b8cb9f2bb6236cdd64a782ca6037b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/unpack_utf16.dart", "hash": "cfab296797450689ec04e7984e7d80e3"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/Pani.jpeg", "hash": "96f43a043ce0c2bb33fd8159b267fb8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/value_listenable_provider.dart", "hash": "bbd255fe46712b372dfe3b99cb340068"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/non_veg_icon.png", "hash": "f05565db430e025238a9f2f36bff6677"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/util/event_emitter.dart", "hash": "904cc08d3f226b40dcda6dc379e14e7e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "b0f444b219eafe3ec2bb9e8a09e545f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/2999f8efc27b6cbc654ead82006b9a59/app.dill", "hash": "7ee2f624372a24a8ace9649f70d64d31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/masala_dosa.jpg", "hash": "8875dd608db5d341eb0d9865577929c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "48a02b5ec3a8c6127b28927b5960d076"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetwork.dart", "hash": "d74c6cdab75d5d9b62ebf9cb07182a26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemfilter.dart", "hash": "ade1e21aa46fef03f979ccd8866f11e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechobjecttoken.dart", "hash": "28d0ea0e14e6f6d4fbc0784ea9e5b2ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winspool.g.dart", "hash": "21471f284b19e2a50238800becdb7419"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionitempattern.dart", "hash": "2132997caebcefd4e7f00446c795cede"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "hash": "cbe579638444243e204755d50bfeae50"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "86d7d305c24e6073b89718914fcd3ee0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/types.dart", "hash": "ce0d3155596e44df8dd0b376d8728971"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/french_fries.webp", "hash": "ca7f3f256b822502a65bb42299ef4482"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/irestrictederrorinfo.dart", "hash": "5a217487ee7b63aad11dcecb6a1ef25b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/scarddlg.g.dart", "hash": "f610f248b9a4132a9a920ccbf1d3cc5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iapplicationactivationmanager.dart", "hash": "c96999a0782dffe9bf8eeaf394caf3bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iclassfactory.dart", "hash": "adbacdd68acdd5e35ce91a3475a1be38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifileopendialog.dart", "hash": "79e42699e1c911b6f1ad9e3d54fdb3e8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/masala_dosa.jpeg", "hash": "140ce199a7e868c96cab37761c846df9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/FontManifest.json", "hash": "7b2a36307916a9721811788013e65289"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/shlwapi.g.dart", "hash": "44636e7c7555564a463937cfb744b79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/vendor_bottom_nav.dart", "hash": "8236bdf7a062252459eff7e2a61e8e00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllinkdual.dart", "hash": "8a9ddc700c157e2eed7422823ba8f6f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_string.dart", "hash": "097e09840cc00325fdbebaacd05f4827"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "d77516b410bc8410c6128cb39240acdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_options.dart", "hash": "44005c1b9f4a2f37139637ce53b7bcc7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_selection_type.dart", "hash": "dd685f95d5588b8d81d3913338ab9cd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/cluster_manager.dart", "hash": "dbeab7fac458b2f865b6cb5e5456847a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "fa60d1a6f81796232bc16dae4ed5f4ac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/macos_options.dart", "hash": "ef56d0c30c2ebbf770de5c7e9cd6f6a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/settings_screen.dart", "hash": "597fba152d6a4e4f6071bdd40ef88dbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/map_configuration_serialization.dart", "hash": "0392b2c674cd70455895a825597c92b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationboolcondition.dart", "hash": "276c1bd3d367490604e56ce50f55068b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/cluster_manager_updates.dart", "hash": "cab5ac94382c688bcf0c15a30113e860"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllink.dart", "hash": "49be686324cf0d48dcb5a31b280affbe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworklistmanagerevents.dart", "hash": "ef4187427d8c318086d02bedaaee9300"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/bitmap.dart", "hash": "fe8cecadafbbb32b56bc974dbef5ae57"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "e5ebffb07608ee2f93a7aa4c23848564"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iconnectionpointcontainer.dart", "hash": "d3c3db414185f054f187734f6e35734e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/Samosa.jpg", "hash": "07c85a82d83d61afd038ebde092c3655"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationvirtualizeditempattern.dart", "hash": "34ac34257c6ee30da8c0b6de4d0a5444"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/test_connection_screen.dart", "hash": "01f041dd07c6f115739ba1d55b37b5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "3e8df17480fcb123b3cdc775ca88dd89"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/extensions/extensions.dart", "hash": "351826c32455bc62ed885311dd1a1404"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "5c5a8f737a2cec1d969f4a9f8dc80a8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/constants.dart", "hash": "9a463f361999508124d9da4853b1ba5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/types.dart", "hash": "f4d93b039bc86c4a156848d06fbc2917"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/LICENSE", "hash": "3dc9bda837dbb78a989e9bd14155d857"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/cart.dart", "hash": "ec12da852e814859e04537e6a147c301"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "068ea69f3733bd1aa72b910e51b41b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "c442be28b905f64b74f6e9f8e5903820"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/menu_screen.dart", "hash": "606ccaed5b3b067284ba779c5a501f16"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/location_screen.dart", "hash": "07b50dec493a04aac866d8f330f151e1"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/debug/cart_debug_screen.dart", "hash": "d0fe573d65ad7e3f57244e0d6da149d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "063f2360bd47faba2c178ce7da715d92"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/src/messages.g.dart", "hash": "07d545e5e568302b0453b8848be6a678"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/image_picker_windows.dart", "hash": "4a9b1f00f6665e425a008a2201361658"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellfolder.dart", "hash": "4a3cc462f40576025abe65fff32c0e63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/LICENSE", "hash": "2890304873073d8f3814d3d6301b8de8"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/review_service.dart", "hash": "1e3eded88c59ec4064f9b52edfe1ec91"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/user32.g.dart", "hash": "cfcb95b36d6698367184c63483b98f9d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/welcome_image.jpeg", "hash": "cd3d246a8bacc13b01446702e7205d94"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "2346472ec1cfdb77f3b27d3b7af72d4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/lib/src/listener.dart", "hash": "95c953a68559412fdca689ca793e9d8c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/types/activity_type.dart", "hash": "709682c0dd3d4246f0d0e9e989fc9f30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/pattern_item.dart", "hash": "a9c5607b57384fbd81c5ae26b312b50f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "4e8a70d478371e0d995f080a6eaa8120"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "2354ff7691e352dd0fe56e0a46338db9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement3.dart", "hash": "d1d288dcc528cf44d85a1788108201b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/lib/flutter_email_sender.dart", "hash": "ffc91dd0d5f606459c8819391b5c1d81"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/src/flutter_secure_storage_windows_ffi.dart", "hash": "2ca4b9e92c39403717d2dcc32bed57e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/assets/toastify.js", "hash": "56e2c9cedd97f10e7e5f1cebd85d53e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/joint_type.dart", "hash": "4c74f3286505b2f61bc305bb025551b3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "6e22c7f1454c97560ef83096561678dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/file_selector_platform_interface.dart", "hash": "eeb75628a0a17d5d8b5dbe0eafc08a29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/gulab_jamun.jpg", "hash": "9d1a0f1879edee257468cab67d9a4bfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiocaptureclient.dart", "hash": "fcb48a4f74d20f9d0767c13fadb819df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "8e7a6f654b6ef374af586747a3ea912b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/magnification.g.dart", "hash": "89a8b628ccf4c51b05b54a5cd9efccba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/utils.dart", "hash": "f5e920fcc3b932c9b189f55e8839ff02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcondition.dart", "hash": "0469c2fefb6084f264cd0df8bce7263a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionmanager2.dart", "hash": "abafcf39ea8af4f661e2141f3a4607e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/widgets/_pinput_selection_gesture_detector_builder.dart", "hash": "5679eb7dc3bc713c0f5a3193cdab9d43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestospackagedependency.dart", "hash": "2880c8f7069a05a0e91104119bd918c4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "e8aae4779eccfdedd9c4b8cbce4ab952"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtogglepattern.dart", "hash": "1afa24c132e0ed2df8a83e7de36112e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/others_screen.dart", "hash": "5c993a00e669f0d0c75c3315658588ce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "4f3e0e3af33c5bdfbf1d32adeba91652"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/bthprops.g.dart", "hash": "cf53cbbbfbbbc6e5f3d0fab91b9f20a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/consumer.dart", "hash": "38c2b67895c0418bce6750d3751a5b26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitem.dart", "hash": "450065123492ed697e9b82cdd6742580"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactorymapping.dart", "hash": "4cbac1eea383d9069adc6a969f8f66d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/enums.dart", "hash": "4988e372f39136c7ab470d11011c08a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/engine/transport.dart", "hash": "5772fe5ab6cb0d3ef4b6c1ad3303d9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "eaf5aa7cf4fe19db30724f637b38257a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/HyderabadiBiryani.jpg", "hash": "bc0be78fa59a8d6a9fe687118ba624f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "822ae20c3b70355a4198594745c656f2"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/chicken_manchurian.jpeg", "hash": "cc0da1e5ccce5844df7c2a772d4f104c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "a101af17dcc01da8f97ef55242f0f167"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/file_selector_linux.dart", "hash": "25c44b3908d2602e0df540ca5b17da27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "d9511b6618e15c2df1d5d0ad39256ed1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/LICENSE", "hash": "2aacbb22761fd3ed84696ff1ecfb38fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps-8.1.1/LICENSE", "hash": "cf96fa0d649f7c7b16616d95e7880a73"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialog.dart", "hash": "3ba0400a5592af52e69a5f66816ecae2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/circle.dart", "hash": "bca0bfe3ca41406b698e27501b16244f"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/delivery_partner_service.dart", "hash": "92bb3b2ef76a3f9df299e9edf8951859"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclock2.dart", "hash": "286726a4ae635c3cb149cd640c3c096f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement4.dart", "hash": "90ad18cc7b92f34e912a56b5730afa4c"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/feedback_screen.dart", "hash": "95cdaa0e6bd646ed186d65af61758ce9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeventsource.dart", "hash": "cd336df9936bd016c7eeadf2ef3b4ee3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/circle.dart", "hash": "d520761a1279ff9bdb4cf4c681762ed7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdockpattern.dart", "hash": "70dffa2d7939cf32863e11a6560de9b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/opengl.dart", "hash": "21baec3598b81f16065716b8ee97c8bb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector.dart", "hash": "7ba48caa7a6a4eac8330274dae899e48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationgridpattern.dart", "hash": "1183f96a464afb812132566262172088"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "d110c5e3ee26058a3e9b4bba6440f15f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "hash": "92268162591ef8477b2f93a31ce9758c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/lib/geolocator_android.dart", "hash": "28039d2a949dbc017a05ba34280698d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "b75501071b7ff5d32ddab4c6ea5d2f84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/material.dart", "hash": "8ef67f192314481983c34c92a81ee5f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationstylespattern.dart", "hash": "de4c7b647a0bbf39c8873977902389f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/lib/src/types/android_settings.dart", "hash": "bb4b92648ab395eb8a548dc2114e942d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "dc037755b1140b31ffc8295fb9570cff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d99e76320b224b4518e76f311ef4a804"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/utils/enums.dart", "hash": "cb49234d186e23edb93ed94e92c75f45"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/models.dart", "hash": "af7f62cb833e10621451a45d9e6c49ed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "7150d31ecb453ea0d7516ebd2a56ff84"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/services.dart", "hash": "0330f85971391a5f5457a20e933fe264"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "4ec9c8dd6d6ecb43d26ebaef03abd1ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "hash": "20e7221c12677486628b48b0c30569f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensorcollection.dart", "hash": "c297d04053f831c4efb96b9e8dcafa0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader5.dart", "hash": "85574281bf7d7bee9722a21e092b4be0"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/masala_chai.jpg", "hash": "68ca61a818eb6e5f991e5a0f9d3dea00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "92e6028556e74c1dc297e332b473f78e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isupporterrorinfo.dart", "hash": "16aa1ef738ce39b1d4a46e95bd8fdfd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/structs.dart", "hash": "b51cea8017e3cbb294fe3b8066265c7e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/socket_service.dart", "hash": "92c5c29ec296f99181452fa00392f16d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationannotationpattern.dart", "hash": "1c877d25a9c369ae60db386f8a1a35ce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/widgets.dart", "hash": "946e37d543d3912bef54a551fb02ea1d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/NOTICES.Z", "hash": "3095ac2de7778bb57ebf0bd54c1562c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "095edf197865d16a71124cfaa427e31f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "5061e0737e2db44e82d8a8c12f328a48"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ivirtualdesktopmanager.dart", "hash": "ffd004f95154cc4fe026271fb8aed8cb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/ios.dart", "hash": "289513be33a0247db2f98df34b73e70c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechbasestream.dart", "hash": "3cb4064cdabe9e96b0334d5877aafedf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/plane.dart", "hash": "2a0078c9098cdc6357cbe70ce1642224"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemservices.dart", "hash": "cad771ca5668c41d6aa03f3af838edd2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/models/position.dart", "hash": "de40378f7ed011561b6ec6bbe2b5ed63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/bidi_formatter.dart", "hash": "5c81dd07124ccc849c310595d9cfe5be"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/about_screen.dart", "hash": "6b3c09630d407b8f72d221d96c9764ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/src/messages.g.dart", "hash": "5b9ec782f9739612abc43813e94f2545"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "dd510cd97dc23d22aebc7b60affd6329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_status.dart", "hash": "e644eae6cf851b3c46f83af266811a6e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permissions.dart", "hash": "1addb41b1ec88a6b5674bd3486e9e225"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/lib/smart_auth.dart", "hash": "23187fe2669da9c4c4667356b91846d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "78f88eba40852ba0b7700d94f3ecfec6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "c211cb790c5fc59f5bb6dcd61e0abcab"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/cart_item.dart", "hash": "42d460d4fbe495458715b0f428170438"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "8e471191ea3b6cdd6c970bf5be4cc86e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/2999f8efc27b6cbc654ead82006b9a59/App.framework/App", "hash": "5ed98dd6cf2848fc27bd6686372ae03d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "ebd06d8f4cce7c59735a2ba28d6dba97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclientduckingcontrol.dart", "hash": "8b1a62bc210bc087059381bbe1afa188"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immnotificationclient.dart", "hash": "6ef9470e2055a4adeffe2c757ce4073f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataimport2.dart", "hash": "40ffb3e4d1023f052603c4ced82ef3a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/callbacks.dart", "hash": "6ea609be06c440417711789df6e5530b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestproperties.dart", "hash": "25ff828118233f5852e97c3e15c2a5da"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/polyline_updates.dart", "hash": "8edfa5faf19ec8b75ed313d7af3444c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page.dart", "hash": "de67603c6b6c6f55fcd5f8b06423d29a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "ac902f2f74549f89e0be0f739d94f7f6"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/admin_navigation.dart", "hash": "26ec6f060f0881571449b5a5c61ea3c8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/bstr.dart", "hash": "dad362de854994a72a95c2ddfb4f4dd6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "3f3682db58f83007aada4d5c36376b90"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/parser/parser.dart", "hash": "084ffe43706fc4ffdd97bbc748bcd227"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "6d0b38802aff8cbe310e72f1a62750d6"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/idli.webp", "hash": "1490f761726ec9a4afc687a46354e944"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/listenable_provider.dart", "hash": "fe16b487322631b50c3cbb09de987315"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "873f01c9dae2d98c8df6fc08ca543aca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/review_card.dart", "hash": "4a43ce6c7940deda3175d61b0b77cb65"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/Pani.jpeg", "hash": "96f43a043ce0c2bb33fd8159b267fb8a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "7270419a025fdbf7840e542397db0c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessioncontrol.dart", "hash": "ec79f631eff71e86c32b832c1724df5f"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/location_service.dart", "hash": "0eecbe77c02eb011a716b17bc5a5d0a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionpattern.dart", "hash": "47221390638b312632445bde2ddc8b4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_handler_platform_interface.dart", "hash": "d3e01a9f299b192bb14b18062c49e562"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/2999f8efc27b6cbc654ead82006b9a59/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "c789dd4004265224055546db82c4c7c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumvariant.dart", "hash": "d0247fa5a3aacccf4023fe5c01469e14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/lib/src/messages.g.dart", "hash": "cf727a09b1e00f59514d81cb3267ee0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/lib/google_maps_flutter_android.dart", "hash": "936badc0ac65400cd636d0064f84fbf3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_preview.dart", "hash": "3208b2267d4d1b0d118b8fcdd774b753"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextchildpattern.dart", "hash": "b74f3bf94bc012c39c228948b2434413"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/marker_updates.dart", "hash": "4eb47b4043ba70283d3d0b16b24746ce"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/2999f8efc27b6cbc654ead82006b9a59/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/lib/src/smart_auth.dart", "hash": "3e3569575d1c1c7d9a02fd2ba3f583e2"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/active_orders_screen.dart", "hash": "96a72ce5050966d3b65c92c0f7ef987f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/veg_icon.png", "hash": "a59fb852c65f22dd10cb7583d219e1f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatatables.dart", "hash": "9aaf9cf926a4121e7dad07c34de018d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellchecker.dart", "hash": "f9114e11eefdb88a452875b8d583e267"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/enums.g.dart", "hash": "ebee8885b5afd397cfa8920eeccf88e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/veg_fried_rice.jpg", "hash": "ef3b597cbd4235c866602edcebaea0e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationwindowpattern.dart", "hash": "58739f2e107e62b5568a115d4bd0d523"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcacherequest.dart", "hash": "4d161c7f1cf07d8222224f941e4241ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/text_direction.dart", "hash": "45f61fb164130d22fda19cf94978853d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtransformpattern2.dart", "hash": "79a8e92d9a9d2929d6f432a49ce22c21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationvaluepattern.dart", "hash": "3e61350adee7d79eed0c5d670bd6594e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "dc2cfe4408f094916cd5eb1d294d1f2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/merge.dart", "hash": "b011c1c4f56d867e644def305d52ba88"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "047052ee1e98c394dd79f1ddf5983b4d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag", "hash": "6cd606d3e368485de4ee213b4887f8a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifilesavedialog.dart", "hash": "dbf08eefdba5228054f4ebc6e6833c17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_dialog_options.dart", "hash": "c7a750b73798e6fbab221eff051e22c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifileisinuse.dart", "hash": "704fe549f6257bb0025634b59ed121a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/src/messages.g.dart", "hash": "d631809a6f4e20b7aa9ea7e17a6581de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.js", "hash": "56e2c9cedd97f10e7e5f1cebd85d53e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/socket.dart", "hash": "3fbf19fe6f389e35c8a411f5cb2dd68f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation4.dart", "hash": "5652ed8799ce8b441ea61e4baef419a4"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/review_provider.dart", "hash": "0c75ae899d459cccc8ae2d09268d8a7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/lost_data.dart", "hash": "3bc26601d19fa0f119ec8e7fc5fd6e23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/selector.dart", "hash": "6a72a2ba15880cab1e1d9a28a94f1a2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immendpoint.dart", "hash": "1f7230510c62078bbebc01fa06f1fb8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "cd0cbb4d29516ed6b03d1c68f0c08477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient.dart", "hash": "70ef104bba3a3ffb69b8ffe7b1baf6e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "630fe5f86ee37699c534f9c91f21f03c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/models/location.dart", "hash": "e613efc2193d2977daa4e19d197e636a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "hash": "b9518d8d712e1322277d9f323e98281c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "4201a655a36b0362d1b9f946b10b5e5e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/LICENSE", "hash": "eb51e6812edbf587a5462bf17f2692a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/provider.dart", "hash": "7c0851720900806fa2a397a81c81875e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/profile_service.dart", "hash": "96f1c8b9fd4eb87d57e4bc54cd36d0a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "hash": "43e95fe0932b68e415f370a7b5500cfd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "d5bcdae8bba4c191294311428a954783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/tile_provider.dart", "hash": "4297dd9c5fe4308f63ed78fd13ad9795"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/winmd_constants.dart", "hash": "16115596ace5bc18b10c61743655c625"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/parser/is_binary.dart", "hash": "a7fb8b46717fe9a06a8271d0f8ac6cd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/expansible.dart", "hash": "43bc92e2816a78f5d5987930bc3e804d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/widgets/delivery_available_orders_widget.dart", "hash": "713e6af91c19b1d48c5b2bd04e9adbfd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/sphere.dart", "hash": "d1089412c69c2ca9e4eeb1607cf0e96e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/int_to_hexstring.dart", "hash": "73cb6deeb88fdcc320cf8e089d51531d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader7.dart", "hash": "a60dd773b7d69b347521fb64257f9397"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/admin_service.dart", "hash": "1513f9423e8bd2270c1db148db17d77c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation6.dart", "hash": "2a3155e741ae2ae06a4a13cea8b719a4"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/Authentication/reset_password.dart", "hash": "fdd87c30d8bb7feeb26c2e35f5052a63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/linux_options.dart", "hash": "26c4f0c369b83e53900ac87bf7e0dcff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationandcondition.dart", "hash": "cdddb39c933c3ad3633502639963a480"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "29d1f8b59096b4d11d693c4102a08499"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/cap.dart", "hash": "94dfc479f1eabd41b61c86556dbb1232"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "b3d31c9c130a73d5425905f361f63957"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/vendor_order_provider.dart", "hash": "15e39329a498c02263c1fc480def4e84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/api_service.dart", "hash": "0190ee6682685ee88e48ec99d3170e1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclockadjustment.dart", "hash": "87db5c50521a6f935c7dd4196eb44001"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/noise.dart", "hash": "e9fe7ebb2a16174d28ca146824370cec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/android_options.dart", "hash": "2d04b343ac3e272959ffa40b7b9d782c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "eda0152837e3eb094d8b1f6d0754f088"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/irunningobjecttable.dart", "hash": "62c7a9476527a2724b820dc7a40a5105"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/errors/errors.dart", "hash": "bc5858b686246ecfb23a51fad627b335"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/backend_favorites_service.dart", "hash": "f62ddfdd04fb43da2ab8c9f90b4f1701"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/guid.dart", "hash": "1699b455a9ecda569a7b35310ea98027"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "0bc495ddf9b02a06a5fc6934847e8708"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "721fe68e34a4747334faa11e91f93523"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math.dart", "hash": "703f2b29a9faedbb501bbc2cd99ba7b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/tools/shader_compiler.dart", "hash": "ba7a493f105b5731f098c7f2e132945b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app.dart", "hash": "dec43cdc695f6ef4f0a33ae459c0e58c"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/delivery_partner_provider.dart", "hash": "9290670d01e35772bfd0299fdd604008"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemrefresher.dart", "hash": "5026f3bc8f63a10ffb208a35e304f40c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/method_channel/method_channel_google_maps_flutter.dart", "hash": "e8ffa65f5f56a798da05e4ebe80f00b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "b0c6844b0af0cd0539060a0bfcbe3713"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/config/api_config.dart", "hash": "332cede2e1bb0d0d3bb7383fe140e3b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/matrix4.dart", "hash": "6250cc05770b9eca7a8010eaed7e5b94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/setupapi.g.dart", "hash": "875f7a3b998ac288c00e9f1edeb69342"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "7821d01f98c559fcbec46a41b4df7ebf"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/user_model.dart", "hash": "1a305c61a3192939d284296682073c30"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/notification_overlay_service.dart", "hash": "7cf618ae7486385b820390e02b1ef22b"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/admin/analytics_screen.dart", "hash": "e5b3df71402f785938d496770b70b4b5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib/permission_handler.dart", "hash": "ae9b498a0c3fd784a628e57eb92307aa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "1244032abcc6103795809163331238a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/lib/src/types/android_position.dart", "hash": "5c0a3ec997252f64985fe42fb37fc6fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/emailjs-1.3.0/lib/src/utils/validate_params.dart", "hash": "8e7e6f65b3f26d32109972fb45e24126"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/review.dart", "hash": "3a4938c7605f65f64e2dcc4d8f6d463b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/x_file.dart", "hash": "d06c42e6c83be207b86412e11889266a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/concatenate.dart", "hash": "42804a1a3f9bec032c0743b86b0a5548"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/src/messages.g.dart", "hash": "f381ed91de52f40a7dff4d2f0f3f6d4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/date_format_internal.dart", "hash": "46f06f2d32f61a3ebc7393f1ae97df27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/structs.g.dart", "hash": "557ed823467a80fba26020510f836870"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/admin/admin_app_bar.dart", "hash": "70fabcccab84d873819f590eda5ff558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialogcustomize.dart", "hash": "3308bebb4908701a4b9fea36a346ff20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/utils/codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/map_objects.dart", "hash": "2aa0272f8c4c9cc15ed4aa36b49f7181"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/geolocator_platform_interface.dart", "hash": "f97f27b271982baf14111fc68c555151"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/switch.dart", "hash": "5b436e60ead9eaf8b303aa72abc08744"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/vendor_provider.dart", "hash": "5150c7d93d5b2dbc316275f58eb8b542"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imoniker.dart", "hash": "7be696b635be2ab16fad5ea6c723dd1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationspreadsheetpattern.dart", "hash": "fac91a50f448265e9a9f97994e8b529e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/polyline.dart", "hash": "2beb6b3726f67d960c5c957f31c45579"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/provider.dart", "hash": "08fb5f27432143c416f473db763fa8c6"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/paniPuri.jpg", "hash": "785404d999a76bde4936523b53dbb49c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/map_widget_configuration.dart", "hash": "3b2aefbe75d694af4d40cc860af89919"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_menu_anchor.dart", "hash": "a749880c7b2c93609c79f05151beda3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "1286926784ce0908d414d696a6321e9f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "8fac1e5cad9ef06d9e55e6559c06b990"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationinvokepattern.dart", "hash": "942a7879522bdf82258a3383893665a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "7706f479f74f6076ef8113576fe54749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0/lib/src/platform_io.dart", "hash": "43425366b77c6b84704c6f626d1bba60"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/pavbhaji.jpg", "hash": "eae423ef9314d7f7acb0737b2d64c9b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/triangle.dart", "hash": "e3f9a51488bca91a3350831c8ad6722f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isequentialstream.dart", "hash": "2fd375e07e198a53eb645fb0e05de9d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/chicken_burger.jpg", "hash": "8089168f5763b0307314de2494de6e33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/earnings_screen.dart", "hash": "5b7223509c39ea33fbb456130f0006b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/geolocator_apple.dart", "hash": "517523644fe678d1dedbf87f16686848"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/method_channel/method_channel_image_picker.dart", "hash": "13b37731f32d54d63ecb4079379f025b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/x_type_group.dart", "hash": "826066d6663c91c94cee09406ded70be"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "329bc189be2701d02fb1b7975ecf329e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "a64e270c19c9e9ed0c5d9a17e0c4a5d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/multi_image_picker_options.dart", "hash": "5ad1b4844df9d51e4c957f292d696471"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "79d4fba74eb854577c9589fb33994287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "hash": "612951585458204d3e3aa22ecf313e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/config/app_config.dart", "hash": "aadb9a01f8aca747a81710766a3de8bd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/url_launcher_uri.dart", "hash": "3cb04add978cf19afa2d0c281e4c80b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessioncontrol2.dart", "hash": "63b3745a751f980bb7dabdd60ca65969"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/polyline.dart", "hash": "c56d215af6e0762f8edde256145042dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "1d3f3077faee6bebdc5279446f541502"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtexteditpattern.dart", "hash": "dec4d61e9b7fb95acd5269b1122ed1a4"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/AssetManifest.json", "hash": "f84158ac112024a8ef0bb8480a3793e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/retrieve_type.dart", "hash": "550bfd92eddfc12d28a028ef44f9cedd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/emailjs-1.3.0/lib/src/models/options.dart", "hash": "11bf2dba6bab3e21571ef6a50811bf36"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/favorite_model.dart", "hash": "eec434f2cc0f88cb39617d503f35d940"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/google_maps_flutter_platform_interface.dart", "hash": "4c0416309d2dc70df436ce304ed86154"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "0cd72a3b3ab10728d2b3234014f43d83"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/cart_migration_service.dart", "hash": "d32932062fa28f2e0bc9f75c2e340e42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector3.dart", "hash": "a1e740a70209acedc9ba1bff7141c14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/ground_overlay.dart", "hash": "7dca15b2185e6a21cc843ed1f49bd0a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "5666a74f3f21ee2fa9b0b2aa37360700"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/location_service_disabled_exception.dart", "hash": "190314300b619a2f73f112d1cfb29f76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensordatareport.dart", "hash": "2fc9d0ed6032b1e7f7c310e4bff73f23"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "f56109c40e6fe9e53f9c6ad021d25ff5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationpropertycondition.dart", "hash": "55b96cf000403acfb7c7d32447a4e102"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechvoicestatus.dart", "hash": "0048b001d840547e4f9cd7be7fad486e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/feedback_history_screen.dart", "hash": "ae7b19829e2e40ab4144601d0d79c376"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/order_history_screen.dart", "hash": "4818dc5ba29fc4c108b84c8337a8da7a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ichannelaudiovolume.dart", "hash": "1d94fcee85c9891374166b09889f8f09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/types.dart", "hash": "4a1d1bdbd4e9be4c8af1a6c656730a66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/async_provider.dart", "hash": "3a2d20718f772fbb710aec7dc5e0bf80"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/welcome_image.jpeg", "hash": "cd3d246a8bacc13b01446702e7205d94"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/picked_file.dart", "hash": "90a070dfee5777a4bca169be4bda3bb1"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/veg_fried_rice.jpg", "hash": "ef3b597cbd4235c866602edcebaea0e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/deferred_inherited_provider.dart", "hash": "59ae3a059b0ba1677002bed66f3b8c2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/url_launcher_windows.dart", "hash": "792062b629f33f12bf4aa68dd6601c50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/lib/src/google_maps_flutter_android.dart", "hash": "1d6e854bb8bd617b8176c95e1f7c752d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/Home.dart", "hash": "5fd0d4d3053855469b3c5aa08f985fb1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/utils/currency_formatter.dart", "hash": "b8bf9467726d2ac745ae1cac5fce12fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/compact_number_format.dart", "hash": "9068f4d63af1ec44245b76b7ab4dfa48"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "2d638931b01747be8315be89cd473caa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "0bda807c0c8098d0ca933cde19f49516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/macros.dart", "hash": "8016baf49ccbce205455e3fc0bddbb17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/french_fries.webp", "hash": "ca7f3f256b822502a65bb42299ef4482"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement6.dart", "hash": "91ddaac3aa28c7527659e459c7c60e68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersiststream.dart", "hash": "5f049e50ba97da4a4ead78fb7ff33dad"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "c9cd996cea2334f644c74ebbdb41f7f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemhiperfenum.dart", "hash": "adebe1537e162fcbe4404ab29e94fef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/polygon_updates.dart", "hash": "********************************"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/order.dart", "hash": "3fa27634c4ea2a8d2ca399e4855277dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/lost_data_response.dart", "hash": "064f79178a908761de1a6b8334a36b6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/global_state.dart", "hash": "d1476acb9e8c42fb16e1c26b03b06340"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/providers/auth_provider.dart", "hash": "eeac5275a74bb56940aa3c8fd2c83fd2"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/orders_screen.dart", "hash": "e3a16fddfbb1a4f5f19f3302d2fe881c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/lib/geocoding_android.dart", "hash": "bfbf16c2cc0c1b08b44c6fe479e45fc7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/lib/src/google_map_inspector_android.dart", "hash": "b0d8acb3d4bbc8c58e43cacc1f12b456"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersist.dart", "hash": "7e66e23bc10439da1ac0a1d84ef99ff3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/maps_object.dart", "hash": "50640193af96b7fe69e71e24489e2f58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationscrollitempattern.dart", "hash": "a3ab60b19b4725b3ea1d1b0cb1c64451"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date.dart", "hash": "f36568b4288388242cb6f7775cb60c42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation3.dart", "hash": "befcd208b8f5a4c002c3f3fcaa09ccfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/powrprof.g.dart", "hash": "b561eb6b0b72c619394c33c55b2e8301"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "56198ea7cfc4930ad8bcfc81a2061b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/where.dart", "hash": "a8ed3dae38fb7fa7baacfa77ac9bd53c"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/admin/admin_sidebar.dart", "hash": "d2accdc091605d5db610867ab4b7b3a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/menu_item.dart", "hash": "1a4d98374356c9ae7edf97a0ed7a86b8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/admin/users_management_screen.dart", "hash": "05f0bb4f9f20b36b3f90707dffae810f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "fbfdd6181c7ea8d5950c24b467debf31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/util/js_type_adapter.dart", "hash": "c55db9235fac0b0a7108c6b6d1c62c79"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/vendor_details.dart", "hash": "69f90b8c6c018f98eb791f318dfbb9ca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0/lib/universal_platform.dart", "hash": "618e2279c176cedeb74ff892ab2eaa23"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/lib/geocoding_ios.dart", "hash": "f405bb2ce978eb4923c7978a1aadec23"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/rating_display.dart", "hash": "3f6e8029b8b007d560ddc96b65b9d978"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/lib/google_maps_flutter_ios.dart", "hash": "9a7b521bfb19ec72b88dd11238b903be"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement8.dart", "hash": "110d15e94f599373085aba42911eadcf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "9b52b890a7d94fe05f5f3ab8b7324b35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dxva2.g.dart", "hash": "4725ff0ec5208552bafe4d6459df82fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_save_location.dart", "hash": "3c21d269eae774b7e06b8adbe73aa18e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/engine/parser/commons.dart", "hash": "c387e96a0de5c012b4ef3f8c7af299df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/oleaut32.g.dart", "hash": "01776292c19efee31306cb0679ab3773"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/src/types/apple_settings.dart", "hash": "2ac7879f9d9a899ccc53c9676ba711f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl_helpers.dart", "hash": "035f884069d09d77af2d64ffa2bb28d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-2.1.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/engine/socket.dart", "hash": "cf350a0433977e29d3356f8b65e1efa6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "166478d231aa67eb8e47a7b559955e6b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "edd2f9cabffc7ea6a5a9497a1b1beccd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/vector2.dart", "hash": "6b519d909b25ca9d144af7972d689c6f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/layouts/admin_layout.dart", "hash": "f99a793454269a70d040cc5feb9dbeea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionmanager.dart", "hash": "53ef1e482a9021fe353d68c9f8a1affc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechaudioformat.dart", "hash": "53ea2c5fd3a75125c1c5c2b4b323dd56"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationrangevaluepattern.dart", "hash": "844c6caebd7d2ff68149b6881deae9f0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/vendor_api_service.dart", "hash": "56075c471bd6b21e40be5e808e1fb308"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/file_selector_windows.dart", "hash": "0902c41eed709a7841f11130fac2a593"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "3d5ecec2ff4236c99de1acef7a20a152"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iknownfolder.dart", "hash": "6f95956e8269ea0bfb393ad11311b570"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/lib/eventify.dart", "hash": "602cde6d39e1b1cf009147ac03e5e366"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/map_configuration.dart", "hash": "64d014e5fa76a41e7a4eb54e9f319877"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "62a38b21e9ef4b8a8d5ae1db1c355bd1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "ca378f8a4dc93cea9ab759f410dcfdb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/image_picker_macos.dart", "hash": "0f0fc7bc5c7c2c1581ed2ed245baf136"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationobjectmodelpattern.dart", "hash": "3538e2e0fb5922d0a2639ae21ff1345f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5cbb66bc2f7ff989a32bc1e5ce5971e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/widgets/widgets.dart", "hash": "a730091b2aa82b842e1257e23bf87148"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/date_computation.dart", "hash": "37837bd1379e66f38e4a7775b6084d0e"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/masala_dosa.jpeg", "hash": "140ce199a7e868c96cab37761c846df9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/LICENSE", "hash": "6a18ff02d63121c12f07c3f2e1dceee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelementarray.dart", "hash": "6c217e68a4edaca67f7c4cf9b53a2638"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/delivery_dashboard.dart", "hash": "cff1cc3b23f0ee1e0eb8c5a1c9950a89"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/models/vendor.dart", "hash": "f1d9159997a695bf65618a55865fdf5b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "aae059b82ff751f6e81487ef98668661"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/socket_io_common.dart", "hash": "d48dc024debe181d3d098770af5736f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/emailjs-1.3.0/lib/src/api/send_json.dart", "hash": "8cef494c273b340e077a4f218eb7ed22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/platform_interface/file_selector_interface.dart", "hash": "5937c2b1cbdf77126bc2dd93570d3c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iknownfoldermanager.dart", "hash": "76c591c08a5c91070981e5e18d21a645"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/offer_screen.dart", "hash": "b5d71620654d01ea5d7432d1ce671cb6"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/Authentication/Signup.dart", "hash": "f2c6d1134582df9d9e31098955c18b10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "fc0c77cc9957db2d82d3e8d56f8ef9d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_ansi.dart", "hash": "d30eba29d046c1a8b7f029838de6e49f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/src/messages.g.dart", "hash": "114597dbbcfb24754b14f8261211d90f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/scan.dart", "hash": "de155f165219b10ba272bd030794873f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationscrollpattern.dart", "hash": "6f2c0852f8ccc6be4e3462b28b0e105f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "28e91fd9077820e2cb2eb981471636ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "67241b28b6ab2188280fb614f1607b2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/comctl32.g.dart", "hash": "983eeb962574f2966f2fb4fb6f79af73"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants_metadata.dart", "hash": "7a458ae2387a1d54657b7d330a7101e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flutter_version.dart", "hash": "ad5b018b42f4cfaf02739e10a48c3ca3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "67d5620f72c33680625822432b60b613"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/istream.dart", "hash": "f336bda23f89f07f340a3e16b0406f05"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/url_launcher_string.dart", "hash": "27e6c510107a34001ef90f889281633e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/favorites_service.dart", "hash": "1342f6cf6cd9be234c2022ad930697a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/version.g.dart", "hash": "679b8cdea258a721b151994c5c4aa9b1"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/services/permission_manager.dart", "hash": "3b5b5c0ba7824043e6fe23833459aef2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math/colors.dart", "hash": "9cd03844c4e859875c10c9708556a0db"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/async_map.dart", "hash": "2e3907a6bf1a5ac452581b5e1d72eadd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/enums/location_accuracy_status.dart", "hash": "6062adde7b02bc31a016151a95e32516"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/widgets/loading_state.dart", "hash": "fbacadca386de90fd46e7303a364e33e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/utils/extensions.dart", "hash": "0c60cb0180de47977a849703faa1527f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/advapi32.g.dart", "hash": "ca80e89c29289e7bd0153b09a83813f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/utils/error_handler.dart", "hash": "b880b2333d5e68a597a29fda7fbdd4d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "728c8c2ffdc4b584c67df65b41e6461f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange.dart", "hash": "73aa79d0da8711d3fe796ddbfb5d7585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/src/errors/already_subscribed_exception.dart", "hash": "6f236f4f809dcf6f1959e9536fbf1f18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "43ba6279385eca1e9d14a3e4d020a3ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/paniPuri.jpg", "hash": "785404d999a76bde4936523b53dbb49c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/wlanapi.g.dart", "hash": "0d11940ff8ff1c4818bbf5978c55a04d"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/delivery/widgets/order_notification_widget.dart", "hash": "da01ec04aa76edba4e6f140970d21e2b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/platform_interface/google_maps_inspector_platform.dart", "hash": "664d7c196e26344f89d0d5b45f19c32b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/vendor/pending_requests_screen.dart", "hash": "8a740b95da2ae988f5119f3873949b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationorcondition.dart", "hash": "4fade6330465dfa703dfe780444f8c7a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/io.dart", "hash": "2c21734ae994817f0963bcea30513c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/pinput_state.dart", "hash": "4888fe79e6984af8240284a98a4cc821"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataimport.dart", "hash": "d594c68b4bd289fec98c48d8753d4992"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/bidi.dart", "hash": "68634d4df864077f507d84d92953a99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialog2.dart", "hash": "b44f7c15167b08017c2831fc2ea95c1e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/src/engine/transport/http_client_adapter_factory.dart", "hash": "187b0ead267a6185bd6f56c4cc0bba22"}, {"path": "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "hash": "174cc783c9979e0b20985dbbd4f28bf6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/camera.dart", "hash": "bd516d1b7ac50694e0595178d094b2ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "c9ab6d9cf33f78fef3ff4ad99fc73390"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/image_picker_android.dart", "hash": "007c2b99a7ab8b0ea0ed298ac83d52b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemcontext.dart", "hash": "e060ba0218367e15110ec7001c8e07f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iinspectable.dart", "hash": "c66a0a686960f09a12ed2eb99e4ad3c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/logger.dart", "hash": "49b829330c9d1fa06c2856f5f2266921"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/non_veg_icon.png", "hash": "f05565db430e025238a9f2f36bff6677"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader2.dart", "hash": "9e2940d007af19bd5cf177e3be339363"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isimpleaudiovolume.dart", "hash": "fa2d84e94c7c5218924a785c41e40bb5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemobjectaccess.dart", "hash": "2eb057b74241d39f07417866b331168e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants.dart", "hash": "a32a056475fc5b8abb7b5c6ca6c9d9b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/service_status.dart", "hash": "5072fb1450640d8f46605ff67dafa147"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/utils/marker.dart", "hash": "be33b72abcb10e6aca9fc36bc210726f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/cluster_manager.dart", "hash": "fe6cae604866a18501e51c7b676e8841"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "2e0b7bb9c12ed9f989240a20a878badc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/lib/src/models/pin_theme.dart", "hash": "bfe0c00f2fa076a6c39b7290e1b991a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipropertystore.dart", "hash": "54900b8b687e3ae6fff475cdc4a79ef0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/lib/src/geolocator_android.dart", "hash": "ddebd456a9cb4aac500c65ca03c2786e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/io.dart", "hash": "a45632c7d0440400b3f7a2ce615d21c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient2.dart", "hash": "c8f773e164112ed7174c2a8ac494c222"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/order_details_screen.dart", "hash": "aac13b20c88a26cb68d907a40da223bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "ccdbac117e9349d3ceaa005c645277e2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/base.dart", "hash": "d0b83bff5ce65e6924939f442ae2c2a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/src/models/placemark.dart", "hash": "ccbc13b0b7e37d98dadf9eeea885ae36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.2/lib/src/controller.dart", "hash": "c976ff89ccc18c5aef4813401091e899"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/dialogs.dart", "hash": "3deaa1966e0bb94f98b6f402ad576915"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange2.dart", "hash": "6905ddd5343384c6898473c3d0a553a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "hash": "892733d7fba692457dcd57fcd8e3dadd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/src/engine/parser/parser.dart", "hash": "8e43101b2b4bed4795e7f5026f0c4d15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/lib/src/intl/constants.dart", "hash": "9a1952c60cdeb2917867cc0e3cea5391"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/src/types/polygon.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/crypt32.g.dart", "hash": "032051419cbe678eeb63004a5ffbcfda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechwaveformatex.dart", "hash": "95efdbdf332246f8a996a1219ed4a578"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/admin/admin_dashboard.dart", "hash": "15f244b6fd5078a9c1f32af6af43218e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "40d43557904504dbd816a205b73461b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "55b4fed5dadc735394ecc0e13867c2eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/combine_latest.dart", "hash": "abf3bd2ed039bc6a844d547e8039eae9"}, {"path": "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/lib/screens/user/saved_addresses_screen.dart", "hash": "afe9aeea5d44e3181d8099bb0cf696a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/idesktopwallpaper.dart", "hash": "80dcf36840d009af3563b72ef07995f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}]}