{"inputs": ["/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/2999f8efc27b6cbc654ead82006b9a59/App.framework/App", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/pubspec.yaml", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/tools/shader_compiler.dart", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/2999f8efc27b6cbc654ead82006b9a59/App.framework.dSYM/Contents/Resources/DWARF/App", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/pubspec.yaml", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/ios/Runner/Info.plist", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/ios/Flutter/AppFrameworkInfo.plist", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/veg_icon.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/Pani.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/chicken_burger.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/bandiwala_logo.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/non_veg_icon.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/chicken_manchurian.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/pavbhaji.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/masala_dosa.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/welcome_image.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/uttapam.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/idli.webp", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/gulab_jamun.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/google_logo.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/masala_dosa.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/veg_fried_rice.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/masala_chai.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/Idli.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/french_fries.webp", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/paniPuri.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/HyderabadiBiryani.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/Samosa.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/assets/samosa.jpg", "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/assets/toastify.js", "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/assets/toastify.css", "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/.dart_tool/flutter_build/2999f8efc27b6cbc654ead82006b9a59/native_assets.json", "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/emailjs-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.65.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_email_sender-6.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_polyline_points-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding-2.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps-8.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pinput-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/smart_auth-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/DOES_NOT_EXIST_RERUN_FOR_WILDCARD693948829"], "outputs": ["/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/App", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/Info.plist", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework.dSYM/Contents/Resources/DWARF/App", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/veg_icon.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/Pani.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/chicken_burger.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/bandiwala_logo.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/non_veg_icon.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/chicken_manchurian.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/pavbhaji.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/masala_dosa.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/welcome_image.jpeg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/uttapam.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/idli.webp", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/gulab_jamun.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/google_logo.png", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/masala_dosa.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/veg_fried_rice.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/masala_chai.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/Idli.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/french_fries.webp", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/paniPuri.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/HyderabadiBiryani.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/Samosa.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/assets/samosa.jpg", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.js", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/packages/fluttertoast/assets/toastify.css", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/NOTICES.Z", "/Users/<USER>/intern/bandiwala/Bandiwala/bandiwala/build/ios/Release-iphoneos/App.framework/flutter_assets/NativeAssetsManifest.json"]}